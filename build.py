#!/usr/bin/env python3
"""
LightRek S3同步工具编译脚本
支持Mac和Linux平台的可执行文件生成
"""

import os
import sys
import platform
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, cwd=None):
    """执行命令并返回结果"""
    print(f"执行命令: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"命令执行失败: {result.stderr}")
            return False
        print(f"命令执行成功: {result.stdout}")
        return True
    except Exception as e:
        print(f"命令执行异常: {e}")
        return False

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖包...")
    if not run_command("pip3 install -r requirements.txt"):
        print("❌ 依赖安装失败")
        return False
    print("✅ 依赖安装成功")
    return True

def create_spec_file():
    """创建PyInstaller规格文件"""
    print("📝 创建PyInstaller规格文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['start_lightrek.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('lightrek_config.json', '.'),
        ('lightrek logo 64px.png', '.'),
        ('lightrek logo 32px.png', '.'),
    ],
    hiddenimports=[
        'schedule',
        'sqlite3',
        'threading',
        'concurrent.futures',
        'xml.etree.ElementTree',
        'mimetypes',
        'fnmatch',
        'urllib.request',
        'urllib.parse',
        'urllib.error',
        'hashlib',
        'hmac',
        'base64',
        'time',
        'datetime',
        'json',
        'uuid',
        'logging',
        'dataclasses',
        'http.server',
        'socketserver'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='lightrek',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('lightrek.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 规格文件创建成功")
    return True

def build_executable():
    """编译可执行文件"""
    print("🔨 开始编译可执行文件...")
    
    # 获取系统信息
    system = platform.system().lower()
    arch = platform.machine().lower()
    
    print(f"系统: {system}")
    print(f"架构: {arch}")
    
    # 编译命令 - 使用python -m pyinstaller确保能找到命令
    cmd = "python3 -m PyInstaller --clean lightrek.spec"
    
    if not run_command(cmd):
        print("❌ 编译失败")
        return False
    
    print("✅ 编译成功")
    return True

def package_release():
    """打包发布文件"""
    print("📦 打包发布文件...")
    
    system = platform.system().lower()
    arch = platform.machine().lower()
    
    # 确定平台名称
    if system == "darwin":
        platform_name = "macos"
    elif system == "linux":
        platform_name = "linux"
    else:
        platform_name = system
    
    # 确定架构名称
    if arch in ["x86_64", "amd64"]:
        arch_name = "x64"
    elif arch in ["arm64", "aarch64"]:
        arch_name = "arm64"
    else:
        arch_name = arch
    
    # 创建发布目录
    release_name = f"lightrek-{platform_name}-{arch_name}"
    release_dir = Path("releases") / release_name
    
    if release_dir.exists():
        shutil.rmtree(release_dir)
    
    release_dir.mkdir(parents=True, exist_ok=True)
    
    # 复制可执行文件
    exe_name = "lightrek.exe" if system == "windows" else "lightrek"
    exe_path = Path("dist") / exe_name
    
    if exe_path.exists():
        shutil.copy2(exe_path, release_dir / exe_name)
        
        # 在Unix系统上设置执行权限
        if system in ["darwin", "linux"]:
            os.chmod(release_dir / exe_name, 0o755)
    else:
        print(f"❌ 可执行文件不存在: {exe_path}")
        return False
    
    # 复制配置文件和资源文件
    config_files = [
        "lightrek_config.json",
        "README.md",
        "lightrek logo 64px.png",
        "lightrek logo 32px.png"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            shutil.copy2(config_file, release_dir / config_file)
            print(f"✅ 复制文件: {config_file}")
        else:
            print(f"⚠️ 文件不存在: {config_file}")
    
    # 创建启动脚本
    if system in ["darwin", "linux"]:
        start_script = release_dir / "start.sh"
        with open(start_script, 'w') as f:
            f.write(f'''#!/bin/bash
# LightRek S3同步工具启动脚本

echo "🚀 启动 LightRek S3同步工具..."
echo "平台: {platform_name}-{arch_name}"
echo "版本: 1.0.0"
echo ""

# 检查权限
if [ ! -x "./{exe_name}" ]; then
    echo "设置执行权限..."
    chmod +x ./{exe_name}
fi

# 启动程序
./{exe_name}
''')
        os.chmod(start_script, 0o755)
    
    # 创建README
    readme_content = f"""# LightRek S3同步工具

## 系统信息
- 平台: {platform_name}
- 架构: {arch_name}
- 版本: 1.0.0

## 使用说明

### 启动程序
"""

    if system in ["darwin", "linux"]:
        readme_content += """
```bash
# 方式1: 使用启动脚本
./start.sh

# 方式2: 直接运行
./lightrek
```
"""
    else:
        readme_content += """
```cmd
lightrek.exe
```
"""

    readme_content += """
### 功能特性
- ✅ 多云存储支持（阿里云OSS、腾讯云COS、AWS S3等）
- ✅ 可视化Web管理界面
- ✅ 多任务并发同步
- ✅ 增量/全量/镜像同步模式
- ✅ 大文件分片传输（>100MB）
- ✅ 定时任务调度
- ✅ 实时进度监控
- ✅ 详细日志记录
- ✅ 连接测试和存储桶自动发现

### 访问地址
程序启动后，请访问: http://localhost:8001

### 配置文件
- `lightrek_config.json`: 主配置文件
- `lightrek.db`: SQLite数据库文件（自动创建）

### 注意事项
1. 确保网络连接正常
2. 配置正确的访问密钥和端点
3. 防火墙允许8001端口访问
4. 大文件传输需要足够的磁盘空间

### 技术支持
如有问题，请检查日志文件：`lightrek_sync.log`
"""

    with open(release_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ 发布包创建成功: {release_dir}")
    return True

def clean_build():
    """清理构建文件"""
    print("🧹 清理构建文件...")
    
    clean_dirs = ["build", "dist", "__pycache__"]
    clean_files = ["lightrek.spec"]
    
    for clean_dir in clean_dirs:
        if Path(clean_dir).exists():
            shutil.rmtree(clean_dir)
            print(f"删除目录: {clean_dir}")
    
    for clean_file in clean_files:
        if Path(clean_file).exists():
            os.remove(clean_file)
            print(f"删除文件: {clean_file}")
    
    print("✅ 清理完成")

def main():
    """主函数"""
    print("🚀 LightRek S3同步工具编译脚本")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 检查必要文件
    required_files = ["start_lightrek.py", "lightrek_task_manager.py", "lightrek_database.py"]
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少必要文件: {file}")
            sys.exit(1)
    
    try:
        # 步骤1: 安装依赖
        if not install_dependencies():
            sys.exit(1)
        
        # 步骤2: 创建规格文件
        if not create_spec_file():
            sys.exit(1)
        
        # 步骤3: 编译可执行文件
        if not build_executable():
            sys.exit(1)
        
        # 步骤4: 打包发布
        if not package_release():
            sys.exit(1)
        
        print("\n🎉 编译完成！")
        print("📁 发布文件位于 releases/ 目录")
        
    except KeyboardInterrupt:
        print("\n⚠️  编译被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 编译过程中发生错误: {e}")
        sys.exit(1)
    finally:
        # 清理构建文件
        clean_build()

if __name__ == "__main__":
    main() 