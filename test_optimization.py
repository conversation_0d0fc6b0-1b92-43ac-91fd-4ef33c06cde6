#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
LightRek S3同步工具 - 优化功能测试脚本
测试并行扫描、缓存机制和流式处理的性能提升效果
"""

import time
import os
import sys
from lightrek_task_manager import TaskManager, ConfigManager, SyncTask, S3Config
from lightrek_database import DatabaseManager

def test_optimization_performance():
    """测试优化性能"""
    print("🧪 LightRek S3同步优化功能测试")
    print("=" * 50)
    
    # 初始化组件
    config_manager = ConfigManager()
    db_manager = DatabaseManager()
    task_manager = TaskManager(config_manager, db_manager)
    
    # 显示优化状态
    optimization_stats = task_manager.get_optimization_statistics()
    print(f"优化功能状态: {'启用' if optimization_stats['optimization_enabled'] else '禁用'}")
    print(f"启用的功能: {', '.join(optimization_stats['features'])}")
    
    if optimization_stats['optimization_enabled']:
        print("\n✅ 优化功能已启用，包含以下特性：")
        print("   📊 并行扫描 - 源文件和目标文件同时扫描")
        print("   💾 元数据缓存 - 避免重复扫描")
        print("   🌊 流式处理 - 大量文件的分批处理")
        
        # 测试缓存管理器
        if task_manager.cache_manager:
            print("\n💾 缓存系统测试：")
            
            # 清理测试缓存
            task_manager.cache_manager.clear_cache()
            print("   - 清理旧缓存完成")
            
            # 获取缓存统计
            cache_stats = task_manager.cache_manager.get_cache_statistics("test-bucket")
            print(f"   - 缓存统计: {cache_stats}")
            
        # 测试并行扫描器
        if task_manager.parallel_scanner:
            print("\n🔄 并行扫描器测试：")
            scan_stats = task_manager.parallel_scanner.get_scan_statistics()
            print(f"   - 扫描统计: {scan_stats}")
            
        print("\n📈 性能预期（相比原始方式）：")
        print("   - 首次扫描：50-60% 性能提升")
        print("   - 后续扫描：90-95% 性能提升（使用缓存）")
        print("   - 10万文件：从41秒减少到2.25秒（后续扫描）")
        
    else:
        print("\n❌ 优化功能未启用，可能原因：")
        print("   - 缺少优化模块文件")
        print("   - 导入失败")
        print("   - 依赖库不完整")
    
    print("\n" + "=" * 50)
    print("测试完成")

def simulate_performance_comparison():
    """模拟性能对比"""
    print("\n📊 10万文件性能对比模拟")
    print("-" * 40)
    
    file_count = 100000
    
    # 原始方式耗时计算
    api_latency = 0.2  # 200ms per API call
    list_calls = file_count // 1000  # 每次1000个文件
    
    original_scan_time = list_calls * 2 * api_latency  # 源+目标扫描
    original_compare_time = file_count * 0.00001  # ETag比较
    original_total = original_scan_time + original_compare_time
    
    print(f"原始方式:")
    print(f"  - API调用次数: {list_calls * 2} (源: {list_calls}, 目标: {list_calls})")
    print(f"  - 扫描耗时: {original_scan_time:.1f}秒")
    print(f"  - 比较耗时: {original_compare_time:.1f}秒")
    print(f"  - 总耗时: {original_total:.1f}秒")
    
    # 优化方式耗时计算（首次）
    optimized_scan_time = max(list_calls * api_latency, list_calls * api_latency)  # 并行扫描
    optimized_compare_time = file_count * 0.000002  # 优化后的文件比较
    optimized_total_first = optimized_scan_time + optimized_compare_time
    
    print(f"\n优化方式（首次扫描）:")
    print(f"  - 并行API调用: {list_calls} 次（并行执行）")
    print(f"  - 扫描耗时: {optimized_scan_time:.1f}秒（并行）")
    print(f"  - 比较耗时: {optimized_compare_time:.1f}秒（优化比较）")
    print(f"  - 总耗时: {optimized_total_first:.1f}秒")
    print(f"  - 性能提升: {((original_total - optimized_total_first) / original_total * 100):.1f}%")
    
    # 优化方式耗时计算（缓存）
    cache_lookup_time = file_count * 0.000001  # 缓存查询
    changed_files = file_count * 0.05  # 假设5%文件有变化
    incremental_scan_time = (changed_files // 1000) * 2 * api_latency
    optimized_total_cached = cache_lookup_time + incremental_scan_time + optimized_compare_time
    
    print(f"\n优化方式（使用缓存）:")
    print(f"  - 缓存查询: {cache_lookup_time:.3f}秒")
    print(f"  - 增量扫描: {incremental_scan_time:.1f}秒（仅{changed_files/1000:.1f}k文件）")
    print(f"  - 总耗时: {optimized_total_cached:.2f}秒")
    print(f"  - 性能提升: {((original_total - optimized_total_cached) / original_total * 100):.1f}%")

if __name__ == "__main__":
    try:
        test_optimization_performance()
        simulate_performance_comparison()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc() 