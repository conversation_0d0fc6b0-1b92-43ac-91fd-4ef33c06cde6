#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
LightRek S3同步工具 - 缓存管理模块
提供文件元数据缓存功能，大幅提升同步分析效率
"""

import sqlite3
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import threading
import os

class CacheManager:
    """文件元数据缓存管理器"""
    
    def __init__(self, cache_db_path: str = "lightrek_cache.db"):
        self.cache_db_path = cache_db_path
        self.connection_pool = {}
        self.lock = threading.Lock()
        self._init_cache_db()
    
    def _init_cache_db(self):
        """初始化缓存数据库"""
        try:
            conn = sqlite3.connect(self.cache_db_path)
            conn.execute('''
                CREATE TABLE IF NOT EXISTS file_metadata_cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    bucket_key TEXT NOT NULL,           -- bucket名 + 文件key的组合
                    file_key TEXT NOT NULL,             -- 文件key
                    bucket_name TEXT NOT NULL,          -- bucket名称
                    file_size INTEGER NOT NULL,         -- 文件大小
                    etag TEXT NOT NULL,                 -- 文件ETag
                    last_modified TEXT NOT NULL,        -- 最后修改时间
                    cache_time TEXT NOT NULL,           -- 缓存时间
                    scan_type TEXT NOT NULL,            -- 扫描类型：source/target
                    task_prefix TEXT DEFAULT '',        -- 任务前缀
                    UNIQUE(bucket_key, scan_type)
                )
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_bucket_key 
                ON file_metadata_cache(bucket_key, scan_type)
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_cache_time 
                ON file_metadata_cache(cache_time)
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_bucket_prefix 
                ON file_metadata_cache(bucket_name, task_prefix, scan_type)
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"初始化缓存数据库失败: {e}")
    
    def _get_connection(self) -> sqlite3.Connection:
        """获取数据库连接（线程安全）"""
        thread_id = threading.get_ident()
        
        with self.lock:
            if thread_id not in self.connection_pool:
                self.connection_pool[thread_id] = sqlite3.connect(
                    self.cache_db_path, 
                    check_same_thread=False,
                    timeout=30.0
                )
                self.connection_pool[thread_id].row_factory = sqlite3.Row
            
            return self.connection_pool[thread_id]
    
    def _generate_bucket_key(self, bucket_name: str, file_key: str) -> str:
        """生成bucket+文件key的唯一标识"""
        return f"{bucket_name}::{file_key}"
    
    def get_cached_files(self, bucket_name: str, scan_type: str, 
                        task_prefix: str = "") -> Dict[str, Dict[str, Any]]:
        """获取缓存中的文件列表"""
        try:
            conn = self._get_connection()
            
            if task_prefix:
                cursor = conn.execute('''
                    SELECT file_key, file_size, etag, last_modified, cache_time
                    FROM file_metadata_cache 
                    WHERE bucket_name = ? AND scan_type = ? AND task_prefix = ?
                ''', (bucket_name, scan_type, task_prefix))
            else:
                cursor = conn.execute('''
                    SELECT file_key, file_size, etag, last_modified, cache_time  
                    FROM file_metadata_cache 
                    WHERE bucket_name = ? AND scan_type = ?
                ''', (bucket_name, scan_type))
            
            cached_files = {}
            for row in cursor.fetchall():
                cached_files[row['file_key']] = {
                    'Size': row['file_size'],
                    'ETag': row['etag'],
                    'LastModified': row['last_modified'],
                    'CacheTime': row['cache_time']
                }
            
            return cached_files
            
        except Exception as e:
            print(f"获取缓存文件失败: {e}")
            return {}
    
    def update_file_cache(self, bucket_name: str, scan_type: str, 
                         files: List[Dict[str, Any]], task_prefix: str = ""):
        """批量更新文件缓存"""
        try:
            conn = self._get_connection()
            cache_time = datetime.now().isoformat()
            
            # 准备批量插入数据
            insert_data = []
            for file_obj in files:
                bucket_key = self._generate_bucket_key(bucket_name, file_obj['Key'])
                insert_data.append((
                    bucket_key,
                    file_obj['Key'], 
                    bucket_name,
                    file_obj.get('Size', 0),
                    file_obj.get('ETag', ''),
                    file_obj.get('LastModified', ''),
                    cache_time,
                    scan_type,
                    task_prefix
                ))
            
            # 批量插入或更新
            conn.executemany('''
                INSERT OR REPLACE INTO file_metadata_cache 
                (bucket_key, file_key, bucket_name, file_size, etag, 
                 last_modified, cache_time, scan_type, task_prefix)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', insert_data)
            
            conn.commit()
            
        except Exception as e:
            print(f"更新文件缓存失败: {e}")
    
    def is_cache_valid(self, bucket_name: str, scan_type: str, 
                      max_age_minutes: int = 60) -> bool:
        """检查缓存是否有效"""
        try:
            conn = self._get_connection()
            cursor = conn.execute('''
                SELECT MAX(cache_time) as latest_cache_time
                FROM file_metadata_cache 
                WHERE bucket_name = ? AND scan_type = ?
            ''', (bucket_name, scan_type))
            
            row = cursor.fetchone()
            if not row or not row['latest_cache_time']:
                return False
            
            cache_time = datetime.fromisoformat(row['latest_cache_time'])
            now = datetime.now()
            
            return (now - cache_time).total_seconds() / 60 < max_age_minutes
            
        except Exception as e:
            print(f"检查缓存有效性失败: {e}")
            return False
    
    def get_cache_statistics(self, bucket_name: str) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            conn = self._get_connection()
            
            # 总文件数统计
            cursor = conn.execute('''
                SELECT scan_type, COUNT(*) as file_count, 
                       MAX(cache_time) as latest_cache_time
                FROM file_metadata_cache 
                WHERE bucket_name = ?
                GROUP BY scan_type
            ''', (bucket_name,))
            
            stats = {
                'bucket_name': bucket_name,
                'source_files': 0,
                'target_files': 0,
                'source_cache_time': None,
                'target_cache_time': None
            }
            
            for row in cursor.fetchall():
                if row['scan_type'] == 'source':
                    stats['source_files'] = row['file_count']
                    stats['source_cache_time'] = row['latest_cache_time']
                elif row['scan_type'] == 'target':
                    stats['target_files'] = row['file_count']
                    stats['target_cache_time'] = row['latest_cache_time']
            
            return stats
            
        except Exception as e:
            print(f"获取缓存统计失败: {e}")
            return {}
    
    def clean_expired_cache(self, max_age_days: int = 7):
        """清理过期缓存"""
        try:
            conn = self._get_connection()
            cutoff_time = (datetime.now() - timedelta(days=max_age_days)).isoformat()
            
            cursor = conn.execute('''
                DELETE FROM file_metadata_cache 
                WHERE cache_time < ?
            ''', (cutoff_time,))
            
            deleted_count = cursor.rowcount
            conn.commit()
            
            print(f"清理过期缓存完成，删除 {deleted_count} 条记录")
            return deleted_count
            
        except Exception as e:
            print(f"清理过期缓存失败: {e}")
            return 0
    
    def clear_cache(self, bucket_name: str = None, scan_type: str = None):
        """清除缓存"""
        try:
            conn = self._get_connection()
            
            if bucket_name and scan_type:
                conn.execute('''
                    DELETE FROM file_metadata_cache 
                    WHERE bucket_name = ? AND scan_type = ?
                ''', (bucket_name, scan_type))
            elif bucket_name:
                conn.execute('''
                    DELETE FROM file_metadata_cache 
                    WHERE bucket_name = ?
                ''', (bucket_name,))
            else:
                conn.execute('DELETE FROM file_metadata_cache')
            
            conn.commit()
            
        except Exception as e:
            print(f"清除缓存失败: {e}")
    
    def get_incremental_scan_keys(self, bucket_name: str, scan_type: str, 
                                 current_files: Dict[str, Any]) -> Tuple[List[str], List[str]]:
        """获取增量扫描的文件key列表"""
        try:
            cached_files = self.get_cached_files(bucket_name, scan_type)
            
            # 需要更新的文件（新文件或已修改的文件）
            files_to_update = []
            # 需要删除的文件（缓存中存在但当前扫描中不存在）
            files_to_delete = []
            
            # 检查当前文件中哪些需要更新
            for file_key, file_info in current_files.items():
                if file_key not in cached_files:
                    # 新文件
                    files_to_update.append(file_key)
                else:
                    cached_info = cached_files[file_key]
                    # 检查是否有变化
                    if (file_info.get('ETag') != cached_info.get('ETag') or
                        file_info.get('Size') != cached_info.get('Size') or
                        file_info.get('LastModified') != cached_info.get('LastModified')):
                        files_to_update.append(file_key)
            
            # 检查缓存中哪些文件已经不存在
            for cached_key in cached_files.keys():
                if cached_key not in current_files:
                    files_to_delete.append(cached_key)
            
            return files_to_update, files_to_delete
            
        except Exception as e:
            print(f"获取增量扫描文件列表失败: {e}")
            return [], []
    
    def close_connections(self):
        """关闭所有数据库连接"""
        with self.lock:
            for conn in self.connection_pool.values():
                try:
                    conn.close()
                except:
                    pass
            self.connection_pool.clear()

# 全局缓存管理器实例
_cache_manager = None

def get_cache_manager() -> CacheManager:
    """获取全局缓存管理器实例"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = CacheManager()
    return _cache_manager 