"""
本地文件系统存储适配器 - 支持本地磁盘和挂载的NAS设备
"""

from storage_abstraction import StorageAdapter, LocalStorageConfig, FileMetadata, ListResult, StorageType, StorageFactory
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import os
import shutil
import hashlib
import mimetypes
from pathlib import Path


class LocalStorageAdapter(StorageAdapter):
    """本地文件系统存储适配器"""
    
    def __init__(self, config: LocalStorageConfig):
        super().__init__(config)
        self.config: LocalStorageConfig = config
        
        # 确保根路径存在
        if not os.path.exists(self.config.root_path):
            raise FileNotFoundError(f"根路径不存在: {self.config.root_path}")
        
        if not os.path.isdir(self.config.root_path):
            raise NotADirectoryError(f"根路径不是目录: {self.config.root_path}")
    
    def _normalize_path(self, path: str) -> str:
        """规范化路径"""
        if not path:
            return self.config.root_path
        
        # 使用系统路径分隔符
        if not os.path.isabs(path):
            path = os.path.join(self.config.root_path, path)
        
        return os.path.normpath(path)
    
    def _get_relative_path(self, full_path: str) -> str:
        """获取相对于根路径的路径"""
        try:
            return os.path.relpath(full_path, self.config.root_path)
        except ValueError:
            # 处理不同驱动器的情况（Windows）
            return full_path
    
    def _calculate_file_hash(self, file_path: str) -> Optional[str]:
        """计算文件哈希值作为ETag"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return None
    
    def _get_content_type(self, file_path: str) -> str:
        """根据文件扩展名推断内容类型"""
        content_type, _ = mimetypes.guess_type(file_path)
        return content_type or 'binary/octet-stream'
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试连接"""
        try:
            # 检查根路径是否可访问
            if not os.path.exists(self.config.root_path):
                return False, f"根路径不存在: {self.config.root_path}"
            
            if not os.path.isdir(self.config.root_path):
                return False, f"根路径不是目录: {self.config.root_path}"
            
            # 检查读写权限
            if not os.access(self.config.root_path, os.R_OK):
                return False, f"无读权限: {self.config.root_path}"
            
            if not os.access(self.config.root_path, os.W_OK):
                return False, f"无写权限: {self.config.root_path}"
            
            # 统计文件数量
            file_count = 0
            for root, dirs, files in os.walk(self.config.root_path):
                file_count += len(files)
                if file_count > 1000:  # 避免扫描太多文件
                    file_count = "1000+"
                    break
            
            return True, f"本地存储连接成功，根路径: {self.config.root_path}，包含约 {file_count} 个文件"
        
        except Exception as e:
            return False, f"本地存储连接失败: {str(e)}"
    
    def list_files(self, prefix: str = "", max_keys: int = 1000, 
                   continuation_token: Optional[str] = None) -> ListResult:
        """列出文件"""
        try:
            files = []
            start_path = self._normalize_path(prefix) if prefix else self.config.root_path
            
            # 如果指定了continuation_token，从该位置开始
            skip_until = continuation_token
            found_start = continuation_token is None
            
            def scan_directory(dir_path: str):
                nonlocal files, found_start, skip_until
                
                if len(files) >= max_keys:
                    return
                
                try:
                    for root, dirs, filenames in os.walk(dir_path):
                        if len(files) >= max_keys:
                            break
                        
                        for filename in filenames:
                            if len(files) >= max_keys:
                                break
                            
                            file_path = os.path.join(root, filename)
                            relative_path = self._get_relative_path(file_path)
                            
                            # 统一使用正斜杠作为路径分隔符
                            relative_path = relative_path.replace(os.sep, '/')
                            
                            # 检查是否匹配前缀
                            if prefix and not relative_path.startswith(prefix):
                                continue
                            
                            # 处理分页
                            if not found_start:
                                if relative_path == skip_until:
                                    found_start = True
                                continue
                            
                            try:
                                # 获取文件信息
                                stat_info = os.stat(file_path)
                                last_modified = datetime.fromtimestamp(stat_info.st_mtime)
                                
                                # 计算文件哈希（优化：只在需要时计算）
                                etag = None
                                # 暂时跳过哈希计算以提高扫描速度
                                # if stat_info.st_size < 1024 * 1024:  # 只对小于1MB的文件计算哈希
                                #     etag = self._calculate_file_hash(file_path)
                                
                                file_meta = FileMetadata(
                                    key=relative_path,
                                    size=stat_info.st_size,
                                    last_modified=last_modified,
                                    etag=etag,
                                    content_type=self._get_content_type(file_path)
                                )
                                files.append(file_meta)
                            
                            except (OSError, IOError):
                                # 跳过无法访问的文件
                                continue
                
                except (OSError, IOError):
                    # 跳过无法访问的目录
                    pass
            
            # 开始扫描
            if os.path.isfile(start_path):
                # 如果prefix指向具体文件
                relative_path = self._get_relative_path(start_path).replace(os.sep, '/')
                try:
                    stat_info = os.stat(start_path)
                    last_modified = datetime.fromtimestamp(stat_info.st_mtime)
                    etag = None  # 暂时跳过哈希计算以提高速度
                    
                    file_meta = FileMetadata(
                        key=relative_path,
                        size=stat_info.st_size,
                        last_modified=last_modified,
                        etag=etag,
                        content_type=self._get_content_type(start_path)
                    )
                    files.append(file_meta)
                except (OSError, IOError):
                    pass
            elif os.path.isdir(start_path):
                scan_directory(start_path)
            
            # 确定是否还有更多文件
            is_truncated = len(files) >= max_keys
            next_token = files[-1].key if is_truncated and files else None
            
            return ListResult(
                files=files,
                is_truncated=is_truncated,
                next_token=next_token
            )
        
        except Exception as e:
            raise Exception(f"列出文件失败: {str(e)}")
    
    def get_file(self, key: str) -> Optional[bytes]:
        """下载文件"""
        try:
            file_path = self._normalize_path(key)
            
            if not os.path.isfile(file_path):
                return None
            
            with open(file_path, 'rb') as f:
                return f.read()
        
        except Exception:
            return None
    
    def put_file(self, key: str, data: bytes, 
                 content_type: str = 'binary/octet-stream',
                 metadata: Optional[Dict[str, str]] = None) -> bool:
        """上传文件"""
        try:
            file_path = self._normalize_path(key)
            
            # 确保目录存在
            dir_path = os.path.dirname(file_path)
            if dir_path and not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
            
            # 写入文件
            with open(file_path, 'wb') as f:
                f.write(data)
            
            # 设置文件权限（Unix/Linux）
            if hasattr(os, 'chmod'):
                try:
                    os.chmod(file_path, 0o644)
                except:
                    pass
            
            return True
        
        except Exception:
            return False
    
    def delete_file(self, key: str) -> bool:
        """删除文件"""
        try:
            file_path = self._normalize_path(key)
            
            if os.path.isfile(file_path):
                os.remove(file_path)
                return True
            return False
        
        except Exception:
            return False
    
    def get_file_metadata(self, key: str) -> Optional[FileMetadata]:
        """获取文件元数据"""
        try:
            file_path = self._normalize_path(key)
            
            if not os.path.isfile(file_path):
                return None
            
            stat_info = os.stat(file_path)
            last_modified = datetime.fromtimestamp(stat_info.st_mtime)
            
            # 计算文件哈希（优化：暂时跳过以提高速度）
            etag = None
            # if stat_info.st_size < 1024 * 1024:  # 只对小于1MB的文件计算哈希
            #     etag = self._calculate_file_hash(file_path)
            
            return FileMetadata(
                key=key,
                size=stat_info.st_size,
                last_modified=last_modified,
                etag=etag,
                content_type=self._get_content_type(file_path)
            )
        
        except Exception:
            return None
    
    def file_exists(self, key: str) -> bool:
        """检查文件是否存在"""
        try:
            file_path = self._normalize_path(key)
            return os.path.isfile(file_path)
        except Exception:
            return False
    
    def put_file_chunked(self, key: str, data: bytes, chunk_size_mb: int = 10,
                        content_type: str = 'binary/octet-stream',
                        metadata: Optional[Dict[str, str]] = None) -> bool:
        """分片上传大文件（对本地文件系统，直接写入即可）"""
        return self.put_file(key, data, content_type, metadata)
    
    def get_storage_info(self) -> Dict[str, Any]:
        """获取存储信息"""
        try:
            # 获取磁盘使用情况
            if hasattr(shutil, 'disk_usage'):
                total, used, free = shutil.disk_usage(self.config.root_path)
                disk_info = {
                    'total_space': total,
                    'used_space': used,
                    'free_space': free,
                    'usage_percent': (used / total * 100) if total > 0 else 0
                }
            else:
                disk_info = {}
            
            base_info = super().get_storage_info()
            base_info.update({
                'root_path': self.config.root_path,
                'disk_usage': disk_info
            })
            
            return base_info
        except Exception:
            return super().get_storage_info()


# 注册本地存储适配器
StorageFactory.register_adapter(StorageType.LOCAL, LocalStorageAdapter)
