#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
LightRek S3同步工具 - 并行扫描模块
实现高效的并行文件扫描和智能缓存机制
"""

import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timezone
from lightrek_cache_manager import get_cache_manager


class ParallelScanner:
    """并行文件扫描器"""
    
    def __init__(self, max_workers: int = 5, config: dict = None):
        self.max_workers = max_workers
        self.config = config or {}
        self.cache_manager = get_cache_manager()
        
        # 从配置获取缓存设置
        cache_config = self.config.get("cache_management", {})
        self.cache_validity_minutes = cache_config.get("cache_validity_minutes", 60)
    
    def scan_files_parallel(self, source_client, target_client, task) -> Tuple[List[Dict], Dict[str, Dict]]:
        """并行扫描源文件和目标文件"""
        
        scan_start_time = time.time()
        
        # 检查是否可以使用缓存
        use_cache = task.sync_mode in ['incremental', 'mirror']
        
        if use_cache:
            return self._scan_with_cache(source_client, target_client, task)
        else:
            return self._scan_without_cache(source_client, target_client, task)
    
    def _scan_with_cache(self, source_client, target_client, task) -> Tuple[List[Dict], Dict[str, Dict]]:
        """使用缓存的扫描方式"""
        
        source_bucket = source_client.config.bucket
        target_bucket = target_client.config.bucket
        
        # 检查缓存是否有效（使用配置的缓存有效期）
        source_cache_valid = self.cache_manager.is_cache_valid(source_bucket, 'source', self.cache_validity_minutes)
        target_cache_valid = self.cache_manager.is_cache_valid(target_bucket, 'target', self.cache_validity_minutes)
        
        print(f"缓存状态 - 源: {'有效' if source_cache_valid else '无效'}, 目标: {'有效' if target_cache_valid else '无效'}")
        
        # 决定扫描策略
        futures = []
        with ThreadPoolExecutor(max_workers=2) as executor:
            # 源文件扫描
            if source_cache_valid:
                futures.append(executor.submit(self._get_cached_files, source_bucket, 'source', task.prefix))
            else:
                futures.append(executor.submit(self._scan_bucket_incremental, source_client, task, 'source'))
            
            # 目标文件扫描
            if task.sync_mode in ['incremental', 'mirror']:
                if target_cache_valid:
                    futures.append(executor.submit(self._get_cached_files, target_bucket, 'target', task.prefix))
                else:
                    futures.append(executor.submit(self._scan_bucket_incremental, target_client, task, 'target'))
        
        # 获取扫描结果
        source_files = []
        target_files = {}
        
        for i, future in enumerate(as_completed(futures)):
            result = future.result()
            if i == 0:  # 源文件结果
                source_files = result if isinstance(result, list) else list(result.values())
                # 如果是从缓存获取的源文件，需要验证其有效性
                if source_cache_valid:
                    source_files = self._validate_cached_files(source_client, source_files, task, 'source')
            else:  # 目标文件结果  
                if isinstance(result, dict):
                    target_files_list = list(result.values())
                    target_files = result
                else:
                    target_files_list = result
                    target_files = {obj['Key']: obj for obj in result}
                
                # 如果是从缓存获取的目标文件，也需要验证其有效性
                if target_cache_valid and target_files_list:
                    validated_target_files = self._validate_cached_files(target_client, target_files_list, task, 'target')
                    target_files = {obj['Key']: obj for obj in validated_target_files}
        
        return source_files, target_files
    
    def _scan_without_cache(self, source_client, target_client, task) -> Tuple[List[Dict], Dict[str, Dict]]:
        """不使用缓存的并行扫描"""
        
        futures = []
        with ThreadPoolExecutor(max_workers=2) as executor:
            # 并行扫描源文件和目标文件
            futures.append(executor.submit(self._scan_bucket_full, source_client, task))
            
            if task.sync_mode in ['incremental', 'mirror']:
                futures.append(executor.submit(self._scan_bucket_full, target_client, task))
        
        # 获取结果
        source_files = []
        target_files = {}
        
        for i, future in enumerate(as_completed(futures)):
            result = future.result()
            if i == 0:  # 源文件
                source_files = result
                # 更新源文件缓存
                if result:
                    self.cache_manager.update_file_cache(
                        source_client.config.bucket, 'source', result, task.prefix
                    )
            else:  # 目标文件
                target_files = {obj['Key']: obj for obj in result}
                # 更新目标文件缓存
                if result:
                    self.cache_manager.update_file_cache(
                        target_client.config.bucket, 'target', result, task.prefix
                    )
        
        return source_files, target_files
    
    def _get_cached_files(self, bucket_name: str, scan_type: str, prefix: str) -> Dict[str, Dict]:
        """从缓存获取文件列表"""
        print(f"从缓存获取{scan_type}文件列表: {bucket_name}")
        
        cached_files = self.cache_manager.get_cached_files(bucket_name, scan_type, prefix)
        
        # 转换格式以匹配扫描结果
        result = {}
        for file_key, file_info in cached_files.items():
            if not prefix or file_key.startswith(prefix):
                result[file_key] = {
                    'Key': file_key,
                    'Size': file_info['Size'],
                    'ETag': file_info['ETag'],
                    'LastModified': file_info['LastModified']
                }
        
        print(f"缓存中获取到 {len(result)} 个{scan_type}文件")
        return result
    
    def _validate_cached_files(self, client, cached_files: List[Dict], task, scan_type: str = 'source') -> List[Dict]:
        """验证缓存文件的有效性，移除已不存在的文件"""
        if not cached_files:
            return cached_files
        
        print(f"验证{scan_type}缓存文件有效性，共 {len(cached_files)} 个文件...")
        
        # 获取当前实际的文件列表
        actual_files = self._scan_bucket_full(client, task)
        actual_files_set = {f['Key'] for f in actual_files}
        
        # 过滤出仍然存在的文件
        valid_files = []
        invalid_count = 0
        
        for cached_file in cached_files:
            file_key = cached_file['Key']
            if file_key in actual_files_set:
                valid_files.append(cached_file)
            else:
                invalid_count += 1
                print(f"  ⚠️  {scan_type}缓存文件已不存在: {file_key}")
        
        if invalid_count > 0:
            print(f"发现 {invalid_count} 个过时的{scan_type}缓存文件，已从结果中移除")
            # 更新缓存，移除无效文件
            self._clean_invalid_cache_files(client, cached_files, actual_files, task, scan_type)
        
        print(f"{scan_type}缓存验证完成，有效文件: {len(valid_files)} 个")
        return valid_files
    
    def _clean_invalid_cache_files(self, client, cached_files: List[Dict], actual_files: List[Dict], task, scan_type: str = 'source'):
        """清理无效的缓存文件"""
        try:
            # 用实际的文件列表更新缓存
            self.cache_manager.update_file_cache(
                client.config.bucket, scan_type, actual_files, task.prefix
            )
            print(f"已更新{scan_type}缓存，移除了无效文件")
        except Exception as e:
            print(f"清理{scan_type}缓存时出错: {e}")
    
    def _scan_bucket_incremental(self, client, task, scan_type: str) -> List[Dict]:
        """增量扫描存储桶"""
        print(f"增量扫描{scan_type}存储桶: {client.config.bucket}")
        
        # 获取缓存的文件列表
        cached_files = self.cache_manager.get_cached_files(
            client.config.bucket, scan_type, task.prefix
        )
        
        # 进行快速扫描（只获取可能有变化的文件）
        all_files = self._scan_bucket_full(client, task)
        
        # 比较并找出需要更新的文件
        files_to_update = []
        current_files_dict = {f['Key']: f for f in all_files}
        
        for file_key, file_info in current_files_dict.items():
            if file_key not in cached_files:
                # 新文件
                files_to_update.append(file_info)
            else:
                cached_info = cached_files[file_key]
                # 使用ETag和大小比较
                if self._is_file_modified(file_info, cached_info):
                    files_to_update.append(file_info)
        
        # 更新缓存
        if files_to_update:
            self.cache_manager.update_file_cache(
                client.config.bucket, scan_type, files_to_update, task.prefix
            )
        
        print(f"增量扫描完成，发现 {len(files_to_update)} 个更新的文件")
        return all_files
    
    def _scan_bucket_full(self, client, task) -> List[Dict]:
        """完整扫描存储桶"""
        print(f"完整扫描存储桶: {client.config.bucket}")
        
        all_files = []
        continuation_token = None
        
        while True:
            try:
                result = client.list_objects(
                    prefix=task.prefix,
                    max_keys=1000,
                    continuation_token=continuation_token
                )
                
                all_files.extend(result.get('Contents', []))
                
                if not result.get('IsTruncated', False):
                    break
                    
                continuation_token = result.get('NextContinuationToken')
                
            except Exception as e:
                print(f"扫描存储桶时出错: {e}")
                break
        
        print(f"扫描完成，发现 {len(all_files)} 个文件")
        return all_files
    
    def _is_file_modified(self, current_file: Dict, cached_file: Dict) -> bool:
        """检查文件是否被修改（支持原始元数据比较）"""
        
        # 首先检查是否有原始元数据
        current_original_etag = None
        current_original_size = None
        
        # 尝试从head_object获取原始元数据
        try:
            from lightrek_task_manager import S3Client
            # 这里需要client实例，暂时使用ETag和大小比较
            pass
        except:
            pass
        
        # 使用ETag和大小比较（最可靠的方法）
        etag_changed = current_file.get('ETag') != cached_file.get('ETag')
        size_changed = current_file.get('Size') != cached_file.get('Size')
        
        if etag_changed or size_changed:
            print(f"📋 ETag/大小检测到文件修改: {current_file.get('Key', '未知')}")
            print(f"   ETag变化: {cached_file.get('ETag', '无')} → {current_file.get('ETag', '无')}")
            print(f"   大小变化: {cached_file.get('Size', 0)} → {current_file.get('Size', 0)}")
            return True
        
        return False
    
    def _compare_files_with_metadata(self, source_file: Dict, target_file: Dict) -> Tuple[bool, str]:
        """
        智能文件比较：支持原始元数据比较
        返回 (should_sync, reason)
        """
        try:
            # 基本的ETag和大小比较
            source_etag = source_file.get('ETag', '')
            target_etag = target_file.get('ETag', '')
            source_size = source_file.get('Size', 0)
            target_size = target_file.get('Size', 0)
            
            # 如果ETag和大小都相同，文件内容肯定相同
            if source_etag == target_etag and source_size == target_size:
                return False, "文件未修改（ETag+大小验证）"
            
            # 检查目标文件是否有原始元数据
            target_original_etag = target_file.get('OriginalETag', '')
            target_original_size = target_file.get('OriginalSize', 0)
            
            if target_original_etag and target_original_size:
                # 目标文件有原始元数据，与源文件的原始属性比较
                if (source_etag == target_original_etag and 
                    source_size == target_original_size):
                    return False, "文件未修改（原始元数据验证）"
                else:
                    return True, f"文件已修改（原始元数据: ETag {target_original_etag} → {source_etag}, 大小 {target_original_size} → {source_size}）"
            else:
                # 目标文件没有原始元数据，使用当前ETag和大小比较
                return True, f"文件已修改（ETag: {target_etag} → {source_etag}, 大小: {target_size} → {source_size}）"
                
        except Exception as e:
            print(f"⚠️ 文件比较失败: {e}")
            # 出错时保守处理，认为文件已修改
            return True, f"文件比较失败，需要同步: {str(e)}"
    

    
    def compare_files_optimized(self, source_files: List[Dict], target_files: Dict[str, Dict], 
                               task) -> Tuple[List[Dict], int]:
        """优化的文件比较算法"""
        
        files_to_sync = []
        skipped_files = 0
        
        print(f"开始比较文件，同步模式: {task.sync_mode}")
        
        for file_obj in source_files:
            key = file_obj['Key']
            should_sync = False
            skip_reason = ""
            
            if task.sync_mode == 'incremental':
                # 增量同步：使用优化的比较逻辑
                if key in target_files:
                    target_obj = target_files[key]
                    
                    # 智能文件比较：支持原始元数据比较
                    should_sync, skip_reason = self._compare_files_with_metadata(file_obj, target_obj)
                else:
                    should_sync = True
                    skip_reason = "新文件"
                    
            elif task.sync_mode == 'full':
                # 全量同步
                should_sync = True
                skip_reason = "全量同步"
                
            elif task.sync_mode == 'mirror':
                # 镜像同步
                if key in target_files:
                    target_obj = target_files[key]
                    if self._is_file_modified(file_obj, target_obj):
                        should_sync = True
                        skip_reason = "镜像同步，文件不一致"
                    else:
                        skip_reason = "镜像同步，文件一致"
                else:
                    should_sync = True
                    skip_reason = "镜像同步，新文件"
            
            if should_sync:
                files_to_sync.append(file_obj)
            else:
                skipped_files += 1
        
        print(f"比较完成 - 需要同步: {len(files_to_sync)}, 跳过: {skipped_files}")
        return files_to_sync, skipped_files
    
    def get_scan_statistics(self) -> Dict[str, Any]:
        """获取扫描统计信息"""
        # 获取缓存统计
        # 这里可以扩展更多统计信息
        return {
            'cache_enabled': True,
            'parallel_scanning': True
        }


class StreamingFileProcessor:
    """流式文件处理器 - 用于处理大量文件时的内存优化"""
    
    def __init__(self, batch_size: int = 5000):
        self.batch_size = batch_size
    
    def process_files_in_batches(self, files: List[Dict], 
                                processor_func, *args, **kwargs) -> List[Any]:
        """分批处理文件列表"""
        results = []
        total_files = len(files)
        
        for i in range(0, total_files, self.batch_size):
            batch = files[i:i + self.batch_size]
            batch_results = processor_func(batch, *args, **kwargs)
            results.extend(batch_results)
            
            # 进度报告
            processed = min(i + self.batch_size, total_files)
            print(f"批处理进度: {processed}/{total_files} ({processed/total_files*100:.1f}%)")
        
        return results 