#!/usr/bin/env python3
"""
自动构建和测试脚本
一键完成编译、打包和测试流程
"""

import os
import sys
import subprocess
import time
from pathlib import Path


def run_command(cmd, cwd=None, timeout=300):
    """执行命令并返回结果"""
    print(f"🔧 执行命令: {cmd}")
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        
        if result.returncode != 0:
            print(f"❌ 命令执行失败 (返回码: {result.returncode})")
            print(f"错误输出: {result.stderr}")
            if result.stdout:
                print(f"标准输出: {result.stdout}")
            return False
        
        if result.stdout:
            print(f"✅ 命令执行成功")
            # 只显示最后几行输出，避免过多信息
            lines = result.stdout.strip().split('\n')
            if len(lines) > 5:
                print("...")
                for line in lines[-3:]:
                    print(line)
            else:
                print(result.stdout)
        
        return True
    
    except subprocess.TimeoutExpired:
        print(f"❌ 命令执行超时 ({timeout}秒)")
        return False
    except Exception as e:
        print(f"❌ 命令执行异常: {e}")
        return False


def check_prerequisites():
    """检查先决条件"""
    print("📋 检查先决条件...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必要文件
    required_files = [
        "storage_abstraction.py",
        "unified_config_manager.py",
        "unified_task_manager.py",
        "web_interface_updates.py",
        "build_unified.py",
        "test_executable.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必要文件存在")
    return True


def install_build_dependencies():
    """安装构建依赖"""
    print("📦 安装构建依赖...")
    
    # 升级pip
    if not run_command("python -m pip install --upgrade pip"):
        print("⚠️ pip升级失败，继续...")
    
    # 安装PyInstaller
    if not run_command("pip install pyinstaller"):
        print("❌ PyInstaller安装失败")
        return False
    
    # 安装requests用于测试
    if not run_command("pip install requests"):
        print("❌ requests安装失败")
        return False
    
    print("✅ 构建依赖安装完成")
    return True


def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    # 运行构建脚本
    if not run_command("python build_unified.py", timeout=600):
        print("❌ 构建失败")
        return False
    
    print("✅ 构建完成")
    return True


def test_executable():
    """测试可执行文件"""
    print("🧪 开始测试可执行文件...")
    
    # 运行测试脚本
    if not run_command("python test_executable.py", timeout=120):
        print("❌ 测试失败")
        return False
    
    print("✅ 测试完成")
    return True


def create_demo_data():
    """创建演示数据"""
    print("📝 创建演示数据...")
    
    try:
        import tempfile
        import json
        
        # 创建演示目录
        demo_source = Path("demo_data/source")
        demo_target = Path("demo_data/target")
        
        demo_source.mkdir(parents=True, exist_ok=True)
        demo_target.mkdir(parents=True, exist_ok=True)
        
        # 创建演示文件
        demo_files = {
            "readme.txt": "这是一个演示文件\n用于测试同步功能",
            "data/file1.txt": "演示数据文件1",
            "data/file2.txt": "演示数据文件2",
            "images/demo.txt": "演示图片文件夹（文本文件代替）"
        }
        
        for file_path, content in demo_files.items():
            full_path = demo_source / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        # 创建演示配置
        demo_config = {
            "version": "2.0",
            "sources": {
                "demo_source": {
                    "storage_type": "local",
                    "name": "演示数据源",
                    "description": "用于演示的本地数据源",
                    "root_path": str(demo_source.absolute())
                }
            },
            "targets": {
                "demo_target": {
                    "storage_type": "local",
                    "name": "演示目标",
                    "description": "用于演示的本地目标",
                    "root_path": str(demo_target.absolute())
                }
            },
            "tasks": {
                "demo_task": {
                    "task_id": "demo_task",
                    "name": "演示同步任务",
                    "description": "用于演示的本地到本地同步任务",
                    "source_id": "demo_source",
                    "target_id": "demo_target",
                    "prefix": "",
                    "max_workers": 5,
                    "retry_times": 3,
                    "retry_delay": 2,
                    "verify_integrity": True,
                    "incremental_sync": True,
                    "sync_mode": "incremental",
                    "delete_extra": False,
                    "file_filter": "",
                    "exclude_filter": "",
                    "bandwidth_limit": 0,
                    "chunk_threshold": 100,
                    "chunk_size": 10,
                    "schedule_type": "manual",
                    "schedule_interval": 1,
                    "schedule_time": "00:00",
                    "enabled": True,
                    "created_at": "2024-01-01T00:00:00",
                    "last_run": "",
                    "last_status": "未运行"
                }
            },
            "global_settings": {
                "default_max_workers": 20,
                "default_retry_times": 5,
                "default_retry_delay": 3,
                "default_chunk_size_mb": 10,
                "default_bandwidth_limit": 0
            }
        }
        
        with open("demo_config.json", 'w', encoding='utf-8') as f:
            json.dump(demo_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 演示数据创建完成")
        print(f"   源目录: {demo_source.absolute()}")
        print(f"   目标目录: {demo_target.absolute()}")
        print(f"   配置文件: demo_config.json")
        
        return True
    
    except Exception as e:
        print(f"❌ 演示数据创建失败: {e}")
        return False


def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("🎉 构建和测试完成！")
    print("=" * 60)
    
    print("\n📁 生成的文件:")
    print("  - releases/: 可执行文件发布包")
    print("  - demo_data/: 演示数据目录")
    print("  - demo_config.json: 演示配置文件")
    
    print("\n🚀 使用方法:")
    print("1. 进入发布目录:")
    print("   cd releases/lightrek-unified-*")
    
    print("\n2. 启动程序:")
    print("   ./start.sh        (Linux/Mac)")
    print("   start.bat         (Windows)")
    print("   ./lightrek-unified (直接运行)")
    
    print("\n3. 访问Web界面:")
    print("   http://localhost:8001")
    
    print("\n4. 测试功能:")
    print("   - 配置存储连接")
    print("   - 创建同步任务")
    print("   - 运行同步测试")
    
    print("\n💡 提示:")
    print("   - 查看 README_UNIFIED.md 了解详细使用说明")
    print("   - 使用 demo_config.json 作为配置模板")
    print("   - 检查日志文件排查问题")


def main():
    """主函数"""
    print("🚀 LightRek 统一存储同步工具 - 自动构建和测试")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # 步骤1: 检查先决条件
        print("\n📋 步骤1: 检查先决条件")
        if not check_prerequisites():
            sys.exit(1)
        
        # 步骤2: 安装构建依赖
        print("\n📦 步骤2: 安装构建依赖")
        if not install_build_dependencies():
            sys.exit(1)
        
        # 步骤3: 构建可执行文件
        print("\n🔨 步骤3: 构建可执行文件")
        if not build_executable():
            sys.exit(1)
        
        # 步骤4: 测试可执行文件
        print("\n🧪 步骤4: 测试可执行文件")
        if not test_executable():
            print("⚠️ 测试失败，但构建已完成")
        
        # 步骤5: 创建演示数据
        print("\n📝 步骤5: 创建演示数据")
        if not create_demo_data():
            print("⚠️ 演示数据创建失败，但不影响主要功能")
        
        # 显示使用说明
        show_usage_instructions()
        
        elapsed_time = time.time() - start_time
        print(f"\n⏱️ 总耗时: {elapsed_time:.1f} 秒")
        
    except KeyboardInterrupt:
        print("\n⚠️ 构建被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 构建过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
