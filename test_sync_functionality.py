#!/usr/bin/env python3
"""
同步功能测试脚本
测试统一存储系统的同步功能
"""

import os
import sys
import tempfile
import shutil
import time
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入统一存储系统模块
from unified_config_manager import UnifiedConfigManager
from unified_task_manager import UnifiedTaskManager
from local_storage_adapter import LocalStorageAdapter, LocalStorageConfig


def create_test_data(source_dir):
    """创建测试数据"""
    print("📝 创建测试数据...")
    
    test_files = {
        "readme.txt": "这是一个测试文件\n用于验证同步功能",
        "data/file1.txt": "测试数据文件1\n包含一些示例内容",
        "data/file2.txt": "测试数据文件2\n另一个示例文件",
        "images/demo.txt": "模拟图片文件\n实际是文本文件",
        "docs/manual.txt": "用户手册\n详细说明文档",
        "config/settings.txt": "配置文件\n系统设置参数"
    }
    
    for file_path, content in test_files.items():
        full_path = source_dir / file_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    print(f"✅ 创建了 {len(test_files)} 个测试文件")
    return test_files


def test_local_to_local_sync():
    """测试本地到本地同步"""
    print("\n🧪 测试本地到本地同步...")
    
    # 创建临时目录
    test_base = Path(tempfile.gettempdir()) / "lightrek_test"
    source_dir = test_base / "source"
    target_dir = test_base / "target"
    
    # 清理并创建目录
    if test_base.exists():
        shutil.rmtree(test_base)
    source_dir.mkdir(parents=True)
    target_dir.mkdir(parents=True)
    
    try:
        # 创建测试数据
        test_files = create_test_data(source_dir)
        
        # 创建配置管理器
        config_file = test_base / "test_config.json"
        config_manager = UnifiedConfigManager(str(config_file))
        
        # 配置源和目标存储
        source_config = {
            'name': '测试源存储',
            'description': '用于测试的源存储',
            'root_path': str(source_dir)
        }
        
        target_config = {
            'name': '测试目标存储',
            'description': '用于测试的目标存储',
            'root_path': str(target_dir)
        }
        
        config_manager.add_source('test_source', 'local', source_config)
        config_manager.add_target('test_target', 'local', target_config)
        
        print("✅ 存储配置完成")
        
        # 测试连接
        source_success, source_msg = config_manager.test_storage_connection('test_source', is_source=True)
        target_success, target_msg = config_manager.test_storage_connection('test_target', is_source=False)
        
        if source_success and target_success:
            print("✅ 存储连接测试成功")
        else:
            print(f"❌ 存储连接测试失败: 源={source_msg}, 目标={target_msg}")
            return False
        
        # 创建任务管理器
        task_manager = UnifiedTaskManager(config_manager)
        
        # 创建同步任务
        task_id = task_manager.create_task(
            name="本地同步测试",
            description="测试本地到本地的文件同步",
            source_id="test_source",
            target_id="test_target",
            sync_mode="full",
            max_workers=3
        )
        
        print(f"✅ 同步任务创建成功: {task_id}")
        
        # 启动同步任务
        if task_manager.start_task(task_id):
            print("✅ 同步任务启动成功")
            
            # 监控任务状态
            max_wait = 30  # 最多等待30秒
            waited = 0
            
            while waited < max_wait:
                status = task_manager.get_task_status(task_id)
                if status:
                    print(f"任务状态: {status['status']} - {status.get('message', '')} (进度: {status.get('progress', 0)}%)")
                    
                    if status['status'] in ['completed', 'failed']:
                        break
                
                time.sleep(2)
                waited += 2
            
            # 验证同步结果
            return verify_sync_result(source_dir, target_dir, test_files)
        else:
            print("❌ 同步任务启动失败")
            return False
    
    except Exception as e:
        print(f"❌ 同步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试目录
        if test_base.exists():
            shutil.rmtree(test_base, ignore_errors=True)


def verify_sync_result(source_dir, target_dir, expected_files):
    """验证同步结果"""
    print("\n🔍 验证同步结果...")
    
    success = True
    
    for file_path in expected_files.keys():
        source_file = source_dir / file_path
        target_file = target_dir / file_path
        
        if not target_file.exists():
            print(f"❌ 目标文件不存在: {file_path}")
            success = False
            continue
        
        # 比较文件内容
        try:
            with open(source_file, 'r', encoding='utf-8') as f:
                source_content = f.read()
            with open(target_file, 'r', encoding='utf-8') as f:
                target_content = f.read()
            
            if source_content == target_content:
                print(f"✅ 文件同步成功: {file_path}")
            else:
                print(f"❌ 文件内容不匹配: {file_path}")
                success = False
        
        except Exception as e:
            print(f"❌ 文件比较失败: {file_path} - {e}")
            success = False
    
    if success:
        print("🎉 所有文件同步成功！")
    else:
        print("⚠️ 部分文件同步失败")
    
    return success


def test_storage_adapters():
    """测试存储适配器"""
    print("\n🧪 测试存储适配器...")
    
    # 测试本地存储适配器
    temp_dir = tempfile.mkdtemp(prefix="lightrek_adapter_test_")
    
    try:
        config = LocalStorageConfig(root_path=temp_dir)
        adapter = LocalStorageAdapter(config)
        
        # 测试连接
        success, message = adapter.test_connection()
        if success:
            print("✅ 本地存储适配器连接测试成功")
        else:
            print(f"❌ 本地存储适配器连接测试失败: {message}")
            return False
        
        # 测试文件操作
        test_key = "test_file.txt"
        test_content = b"Hello, LightRek!"
        
        # 上传文件
        if adapter.put_file(test_key, test_content):
            print("✅ 文件上传成功")
        else:
            print("❌ 文件上传失败")
            return False
        
        # 检查文件存在
        if adapter.file_exists(test_key):
            print("✅ 文件存在检查成功")
        else:
            print("❌ 文件存在检查失败")
            return False
        
        # 下载文件
        downloaded_content = adapter.get_file(test_key)
        if downloaded_content == test_content:
            print("✅ 文件下载成功")
        else:
            print("❌ 文件下载失败或内容不匹配")
            return False
        
        # 获取文件元数据
        metadata = adapter.get_file_metadata(test_key)
        if metadata and metadata.size == len(test_content):
            print("✅ 文件元数据获取成功")
        else:
            print("❌ 文件元数据获取失败")
            return False
        
        # 列出文件
        result = adapter.list_files()
        if result.files and any(f.key == test_key for f in result.files):
            print("✅ 文件列表获取成功")
        else:
            print("❌ 文件列表获取失败")
            return False
        
        # 删除文件
        if adapter.delete_file(test_key):
            print("✅ 文件删除成功")
        else:
            print("❌ 文件删除失败")
            return False
        
        print("🎉 存储适配器测试全部通过！")
        return True
    
    except Exception as e:
        print(f"❌ 存储适配器测试失败: {e}")
        return False
    
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def main():
    """主函数"""
    print("🚀 LightRek 统一存储同步工具 - 功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 测试1: 存储适配器
    print("\n📋 测试1: 存储适配器功能")
    adapter_result = test_storage_adapters()
    test_results.append(("存储适配器", adapter_result))
    
    # 测试2: 本地到本地同步
    print("\n📋 测试2: 本地到本地同步")
    sync_result = test_local_to_local_sync()
    test_results.append(("本地同步", sync_result))
    
    # 打印测试总结
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！统一存储系统工作正常")
        print("\n💡 下一步:")
        print("1. 安装Flask启用Web界面: pip install flask")
        print("2. 安装其他存储支持: pip install paramiko smbprotocol")
        print("3. 配置不同类型的存储进行测试")
        return True
    else:
        print("⚠️ 部分测试失败，请检查问题")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
