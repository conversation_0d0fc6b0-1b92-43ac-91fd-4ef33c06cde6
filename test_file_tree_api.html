<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件树API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 文件树API测试工具</h1>
        <p>用于测试LightRek的文件树API功能</p>

        <div class="test-section">
            <h3>1. 获取存储列表</h3>
            <button class="btn" onclick="testStorageList()">📋 获取存储列表</button>
            <div id="storage-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 测试文件树API</h3>
            <div>
                <label>存储ID:</label>
                <input type="text" id="storage-id" placeholder="输入存储ID">
                <label>路径:</label>
                <input type="text" id="path" placeholder="可选，如: /root/project" value="">
                <button class="btn" onclick="testFileTree()">🌳 获取文件树</button>
            </div>
            <div id="file-tree-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 快速测试SFTP</h3>
            <p>如果您已经配置了SFTP存储，可以快速测试：</p>
            <button class="btn" onclick="quickTestSFTP()">⚡ 快速测试SFTP</button>
            <div id="quick-test-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. API状态检查</h3>
            <button class="btn" onclick="checkAPIStatus()">🔍 检查API状态</button>
            <div id="api-status-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = content;
        }

        function testStorageList() {
            fetch('/api/storages')
                .then(response => response.json())
                .then(data => {
                    showResult('storage-result', JSON.stringify(data, null, 2));
                })
                .catch(error => {
                    showResult('storage-result', `错误: ${error.message}`, true);
                });
        }

        function testFileTree() {
            const storageId = document.getElementById('storage-id').value;
            const path = document.getElementById('path').value;
            
            if (!storageId) {
                showResult('file-tree-result', '请输入存储ID', true);
                return;
            }

            const url = `/api/file-tree?storage_id=${encodeURIComponent(storageId)}&path=${encodeURIComponent(path)}`;
            
            fetch(url)
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);
                    return response.text();
                })
                .then(text => {
                    console.log('Response text:', text);
                    try {
                        const data = JSON.parse(text);
                        showResult('file-tree-result', JSON.stringify(data, null, 2));
                    } catch (e) {
                        showResult('file-tree-result', `JSON解析错误: ${e.message}\n原始响应: ${text}`, true);
                    }
                })
                .catch(error => {
                    showResult('file-tree-result', `网络错误: ${error.message}`, true);
                });
        }

        function quickTestSFTP() {
            // 先获取存储列表，找到SFTP存储
            fetch('/api/storages')
                .then(response => response.json())
                .then(data => {
                    const sources = data.sources || {};
                    const sftpSources = Object.entries(sources).filter(([id, config]) => 
                        config.storage_type === 'sftp'
                    );
                    
                    if (sftpSources.length === 0) {
                        showResult('quick-test-result', '未找到SFTP存储配置，请先配置SFTP存储', true);
                        return;
                    }
                    
                    const [sftpId, sftpConfig] = sftpSources[0];
                    showResult('quick-test-result', `找到SFTP存储: ${sftpConfig.name} (${sftpId})\n正在测试文件树API...`);
                    
                    // 测试文件树API
                    const url = `/api/file-tree?storage_id=${encodeURIComponent(sftpId)}&path=`;
                    
                    fetch(url)
                        .then(response => response.text())
                        .then(text => {
                            try {
                                const data = JSON.parse(text);
                                showResult('quick-test-result', 
                                    `SFTP存储: ${sftpConfig.name}\n` +
                                    `测试结果: ${data.success ? '成功' : '失败'}\n` +
                                    `详细信息:\n${JSON.stringify(data, null, 2)}`
                                );
                            } catch (e) {
                                showResult('quick-test-result', 
                                    `SFTP存储: ${sftpConfig.name}\n` +
                                    `JSON解析错误: ${e.message}\n` +
                                    `原始响应: ${text}`, true
                                );
                            }
                        })
                        .catch(error => {
                            showResult('quick-test-result', 
                                `SFTP存储: ${sftpConfig.name}\n` +
                                `网络错误: ${error.message}`, true
                            );
                        });
                })
                .catch(error => {
                    showResult('quick-test-result', `获取存储列表失败: ${error.message}`, true);
                });
        }

        function checkAPIStatus() {
            const apis = [
                '/api/storages',
                '/api/file-tree?storage_id=test&path=',
                '/api/compressed-transfer'
            ];
            
            let results = [];
            let completed = 0;
            
            apis.forEach(api => {
                fetch(api, { method: 'GET' })
                    .then(response => {
                        results.push(`${api}: ${response.status} ${response.statusText}`);
                    })
                    .catch(error => {
                        results.push(`${api}: 错误 - ${error.message}`);
                    })
                    .finally(() => {
                        completed++;
                        if (completed === apis.length) {
                            showResult('api-status-result', results.join('\n'));
                        }
                    });
            });
        }

        // 页面加载时自动检查API状态
        window.onload = function() {
            checkAPIStatus();
        };
    </script>
</body>
</html>
