#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRek S3 同步工具 - 数据库管理模块
"""

import sqlite3
import json
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_file: str = "lightrek_data.db"):
        self.db_file = db_file
        self.lock = threading.Lock()
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_file) as conn:
            cursor = conn.cursor()
            
            # 任务执行历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS task_executions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT NOT NULL,
                    task_name TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    status TEXT NOT NULL DEFAULT 'running',
                    total_files INTEGER DEFAULT 0,
                    success_files INTEGER DEFAULT 0,
                    failed_files INTEGER DEFAULT 0,
                    total_size INTEGER DEFAULT 0,
                    transferred_size INTEGER DEFAULT 0,
                    error_message TEXT,
                    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 任务日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS task_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    execution_id INTEGER NOT NULL,
                    task_id TEXT NOT NULL,
                    level TEXT NOT NULL,
                    message TEXT NOT NULL,
                    file_path TEXT,
                    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (execution_id) REFERENCES task_executions (id)
                )
            ''')
            
            # 文件同步记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS file_sync_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    execution_id INTEGER NOT NULL,
                    task_id TEXT NOT NULL,
                    file_key TEXT NOT NULL,
                    file_size INTEGER NOT NULL,
                    source_etag TEXT,
                    target_etag TEXT,
                    status TEXT NOT NULL,
                    sync_time TEXT NOT NULL,
                    error_message TEXT,
                    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (execution_id) REFERENCES task_executions (id)
                )
            ''')
            
            # 系统配置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_configs (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def create_task_execution(self, task_id: str, task_name: str) -> int:
        """创建任务执行记录"""
        with self.lock:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO task_executions (task_id, task_name, start_time, status)
                    VALUES (?, ?, ?, 'running')
                ''', (task_id, task_name, datetime.now().isoformat()))
                
                execution_id = cursor.lastrowid
                conn.commit()
                return execution_id
    
    def update_task_execution(self, execution_id: int, **kwargs):
        """更新任务执行记录"""
        with self.lock:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                
                # 构建更新语句
                fields = []
                values = []
                for key, value in kwargs.items():
                    if key in ['end_time', 'status', 'total_files', 'success_files', 
                              'failed_files', 'total_size', 'transferred_size', 'error_message']:
                        fields.append(f"{key} = ?")
                        values.append(value)
                
                if fields:
                    values.append(execution_id)
                    cursor.execute(f'''
                        UPDATE task_executions 
                        SET {', '.join(fields)}
                        WHERE id = ?
                    ''', values)
                    conn.commit()
    
    def add_task_log(self, execution_id: int, task_id: str, level: str, 
                     message: str, file_path: str = None):
        """添加任务日志"""
        with self.lock:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO task_logs (execution_id, task_id, level, message, file_path)
                    VALUES (?, ?, ?, ?, ?)
                ''', (execution_id, task_id, level, message, file_path))
                conn.commit()
    
    def add_file_sync_record(self, execution_id: int, task_id: str, file_key: str,
                           file_size: int, source_etag: str, target_etag: str,
                           status: str, error_message: str = None):
        """添加文件同步记录"""
        with self.lock:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO file_sync_records 
                    (execution_id, task_id, file_key, file_size, source_etag, 
                     target_etag, status, sync_time, error_message)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (execution_id, task_id, file_key, file_size, source_etag,
                      target_etag, status, datetime.now().isoformat(), error_message))
                conn.commit()
    
    def get_task_executions(self, task_id: str = None, limit: int = 100) -> List[Dict]:
        """获取任务执行历史"""
        with sqlite3.connect(self.db_file) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            if task_id:
                cursor.execute('''
                    SELECT * FROM task_executions 
                    WHERE task_id = ? 
                    ORDER BY start_time DESC 
                    LIMIT ?
                ''', (task_id, limit))
            else:
                cursor.execute('''
                    SELECT * FROM task_executions 
                    ORDER BY start_time DESC 
                    LIMIT ?
                ''', (limit,))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_task_logs(self, execution_id: int = None, task_id: str = None, 
                      limit: int = 1000) -> List[Dict]:
        """获取任务日志"""
        with sqlite3.connect(self.db_file) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            if execution_id:
                cursor.execute('''
                    SELECT * FROM task_logs 
                    WHERE execution_id = ? 
                    ORDER BY created_at DESC 
                    LIMIT ?
                ''', (execution_id, limit))
            elif task_id:
                cursor.execute('''
                    SELECT tl.* FROM task_logs tl
                    JOIN task_executions te ON tl.execution_id = te.id
                    WHERE te.task_id = ? 
                    ORDER BY tl.created_at DESC 
                    LIMIT ?
                ''', (task_id, limit))
            else:
                cursor.execute('''
                    SELECT * FROM task_logs 
                    ORDER BY created_at DESC 
                    LIMIT ?
                ''', (limit,))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_file_sync_records(self, execution_id: int, limit: int = 1000) -> List[Dict]:
        """获取文件同步记录"""
        with sqlite3.connect(self.db_file) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM file_sync_records 
                WHERE execution_id = ? 
                ORDER BY created_at DESC 
                LIMIT ?
            ''', (execution_id, limit))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_task_statistics(self, task_id: str = None, days: int = 30) -> Dict:
        """获取任务统计信息"""
        with sqlite3.connect(self.db_file) as conn:
            cursor = conn.cursor()
            
            # 基础统计
            if task_id:
                cursor.execute('''
                    SELECT 
                        COUNT(*) as total_executions,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_executions,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_executions,
                        SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running_executions,
                        SUM(success_files) as total_success_files,
                        SUM(failed_files) as total_failed_files,
                        SUM(transferred_size) as total_transferred_size
                    FROM task_executions 
                    WHERE task_id = ? AND start_time >= datetime('now', '-{} days')
                '''.format(days), (task_id,))
            else:
                cursor.execute('''
                    SELECT 
                        COUNT(*) as total_executions,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_executions,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_executions,
                        SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running_executions,
                        SUM(success_files) as total_success_files,
                        SUM(failed_files) as total_failed_files,
                        SUM(transferred_size) as total_transferred_size
                    FROM task_executions 
                    WHERE start_time >= datetime('now', '-{} days')
                '''.format(days))
            
            result = cursor.fetchone()
            
            return {
                'total_executions': result[0] or 0,
                'successful_executions': result[1] or 0,
                'failed_executions': result[2] or 0,
                'running_executions': result[3] or 0,
                'total_success_files': result[4] or 0,
                'total_failed_files': result[5] or 0,
                'total_transferred_size': result[6] or 0
            }
    
    def cleanup_old_records(self, days: int = 90):
        """清理旧记录"""
        with self.lock:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                
                # 删除旧的执行记录
                cursor.execute('''
                    DELETE FROM task_executions 
                    WHERE start_time < datetime('now', '-{} days')
                '''.format(days))
                
                # 删除孤立的日志记录
                cursor.execute('''
                    DELETE FROM task_logs 
                    WHERE execution_id NOT IN (SELECT id FROM task_executions)
                ''')
                
                # 删除孤立的文件同步记录
                cursor.execute('''
                    DELETE FROM file_sync_records 
                    WHERE execution_id NOT IN (SELECT id FROM task_executions)
                ''')
                
                conn.commit()
    
    def get_system_config(self, key: str, default_value: str = None) -> str:
        """获取系统配置"""
        with sqlite3.connect(self.db_file) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT value FROM system_configs WHERE key = ?', (key,))
            result = cursor.fetchone()
            return result[0] if result else default_value
    
    def set_system_config(self, key: str, value: str):
        """设置系统配置"""
        with self.lock:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO system_configs (key, value, updated_at)
                    VALUES (?, ?, ?)
                ''', (key, value, datetime.now().isoformat()))
                conn.commit()
    
    def get_statistics(self) -> Dict:
        """获取系统统计信息"""
        with sqlite3.connect(self.db_file) as conn:
            cursor = conn.cursor()
            
            # 总执行次数
            cursor.execute('SELECT COUNT(*) FROM task_executions')
            total_executions = cursor.fetchone()[0]
            
            # 今日执行次数
            cursor.execute('''
                SELECT COUNT(*) FROM task_executions 
                WHERE date(start_time) = date('now')
            ''')
            today_executions = cursor.fetchone()[0]
            
            # 总文件数和大小
            cursor.execute('''
                SELECT 
                    COALESCE(SUM(success_files), 0) as total_files,
                    COALESCE(SUM(transferred_size), 0) as total_size
                FROM task_executions
            ''')
            result = cursor.fetchone()
            total_files = result[0]
            total_size = result[1]
            
            # 成功率
            cursor.execute('''
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as success
                FROM task_executions
            ''')
            result = cursor.fetchone()
            success_rate = (result[1] / result[0] * 100) if result[0] > 0 else 0
            
            # 平均速度 (假设平均执行时间为1小时)
            avg_speed = total_size / max(total_executions, 1) / 3600 if total_executions > 0 else 0
            
            # 运行中任务
            cursor.execute('''
                SELECT COUNT(*) FROM task_executions 
                WHERE status = 'running'
            ''')
            running_tasks = cursor.fetchone()[0]
            
            # 今日失败任务
            cursor.execute('''
                SELECT COUNT(*) FROM task_executions 
                WHERE status = 'failed' AND date(start_time) = date('now')
            ''')
            failed_tasks_today = cursor.fetchone()[0]
            
            # 最大文件大小
            cursor.execute('''
                SELECT COALESCE(MAX(file_size), 0) FROM file_sync_records
            ''')
            largest_file_size = cursor.fetchone()[0]
            
            # 总错误数
            cursor.execute('''
                SELECT COUNT(*) FROM task_logs WHERE level = 'ERROR'
            ''')
            total_errors = cursor.fetchone()[0]
            
            # 文件类型分布
            cursor.execute('''
                SELECT 
                    CASE 
                        WHEN file_key LIKE '%.jpg' OR file_key LIKE '%.jpeg' OR file_key LIKE '%.png' THEN '图片'
                        WHEN file_key LIKE '%.mp4' OR file_key LIKE '%.avi' OR file_key LIKE '%.mov' THEN '视频'
                        WHEN file_key LIKE '%.pdf' OR file_key LIKE '%.doc' OR file_key LIKE '%.docx' THEN '文档'
                        WHEN file_key LIKE '%.zip' OR file_key LIKE '%.rar' OR file_key LIKE '%.tar' THEN '压缩包'
                        ELSE '其他'
                    END as file_type,
                    COUNT(*) as count
                FROM file_sync_records
                GROUP BY file_type
            ''')
            file_types = dict(cursor.fetchall())
            
            return {
                'total_executions': total_executions,
                'today_executions': today_executions,
                'total_files': total_files,
                'total_size': total_size,
                'success_rate': success_rate,
                'avg_speed': avg_speed,
                'running_tasks': running_tasks,
                'failed_tasks_today': failed_tasks_today,
                'peak_hour': '14:00',  # 默认值
                'largest_file_size': largest_file_size,
                'total_errors': total_errors,
                'file_types': file_types
            }
    
    def get_recent_executions(self, limit: int = 5) -> List[Dict]:
        """获取最近执行记录"""
        with sqlite3.connect(self.db_file) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM task_executions 
                ORDER BY start_time DESC 
                LIMIT ?
            ''', (limit,))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_execution_trend(self, days: int = 7) -> Dict:
        """获取执行趋势"""
        with sqlite3.connect(self.db_file) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    date(start_time) as date,
                    COUNT(*) as count
                FROM task_executions 
                WHERE start_time >= datetime('now', '-{} days')
                GROUP BY date(start_time)
                ORDER BY date
            '''.format(days))
            
            return dict(cursor.fetchall())
    
    def get_execution_logs(self, execution_id: int) -> List[Dict]:
        """获取执行日志"""
        return self.get_task_logs(execution_id=execution_id) 