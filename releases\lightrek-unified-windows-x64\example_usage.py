"""
统一存储系统使用示例
演示如何配置和使用不同类型的存储进行同步
"""

import os
import sys
import time
from unified_config_manager import UnifiedConfigManager
from unified_task_manager import UnifiedTaskManager

# 导入所有存储适配器以注册到工厂
import s3_storage_adapter
import sftp_storage_adapter
import smb_storage_adapter
import ftp_storage_adapter
import local_storage_adapter


def main():
    """主函数"""
    print("=== LightRek 统一存储同步系统示例 ===\n")
    
    # 创建配置管理器
    config_manager = UnifiedConfigManager("example_config.json")
    
    # 创建任务管理器
    task_manager = UnifiedTaskManager(config_manager)
    
    # 显示支持的存储类型
    print("支持的存储类型:")
    for storage_type in config_manager.get_supported_storage_types():
        print(f"  - {storage_type['name']}: {storage_type['description']}")
    print()
    
    # 示例1: 配置S3存储
    print("=== 示例1: 配置S3存储 ===")
    s3_config = {
        'name': '阿里云OSS测试',
        'description': '用于测试的阿里云OSS存储',
        'access_key': 'your_access_key',
        'secret_key': 'your_secret_key',
        'endpoint': 'https://oss-cn-hangzhou.aliyuncs.com',
        'region': 'cn-hangzhou',
        'bucket': 'test-bucket'
    }
    
    if config_manager.add_source('s3_source', 's3', s3_config):
        print("✓ S3源存储配置成功")
    else:
        print("✗ S3源存储配置失败")
    
    # 示例2: 配置本地存储
    print("\n=== 示例2: 配置本地存储 ===")
    local_config = {
        'name': '本地测试目录',
        'description': '用于测试的本地目录',
        'root_path': os.path.join(os.getcwd(), 'test_local_storage')
    }
    
    # 创建测试目录
    os.makedirs(local_config['root_path'], exist_ok=True)
    
    if config_manager.add_target('local_target', 'local', local_config):
        print("✓ 本地目标存储配置成功")
    else:
        print("✗ 本地目标存储配置失败")
    
    # 示例3: 配置SFTP存储（需要有SFTP服务器）
    print("\n=== 示例3: 配置SFTP存储 ===")
    sftp_config = {
        'name': 'SFTP测试服务器',
        'description': '用于测试的SFTP服务器',
        'hostname': 'sftp.example.com',
        'port': 22,
        'username': 'testuser',
        'password': 'testpass',
        'root_path': '/home/<USER>/data'
    }
    
    if config_manager.add_target('sftp_target', 'sftp', sftp_config):
        print("✓ SFTP目标存储配置成功")
    else:
        print("✗ SFTP目标存储配置失败")
    
    # 示例4: 测试存储连接
    print("\n=== 示例4: 测试存储连接 ===")
    
    # 测试本地存储连接
    success, message = config_manager.test_storage_connection('local_target', is_source=False)
    print(f"本地存储连接测试: {'✓' if success else '✗'} {message}")
    
    # 测试S3连接（如果配置了真实的凭据）
    # success, message = config_manager.test_storage_connection('s3_source', is_source=True)
    # print(f"S3存储连接测试: {'✓' if success else '✗'} {message}")
    
    # 示例5: 创建同步任务
    print("\n=== 示例5: 创建同步任务 ===")
    
    try:
        task_id = task_manager.create_task(
            name="本地到本地同步测试",
            description="测试本地文件系统之间的同步",
            source_id="local_target",  # 使用本地存储作为源
            target_id="local_target",  # 使用本地存储作为目标
            prefix="test/",
            sync_mode="incremental",
            max_workers=5
        )
        print(f"✓ 同步任务创建成功，任务ID: {task_id}")
        
        # 创建一些测试文件
        test_dir = os.path.join(local_config['root_path'], 'test')
        os.makedirs(test_dir, exist_ok=True)
        
        for i in range(3):
            test_file = os.path.join(test_dir, f'test_file_{i}.txt')
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(f'这是测试文件 {i}\n内容: {time.time()}')
        
        print("✓ 测试文件创建完成")
        
        # 启动同步任务
        print("\n=== 示例6: 运行同步任务 ===")
        if task_manager.start_task(task_id):
            print("✓ 同步任务启动成功")
            
            # 监控任务状态
            while True:
                status = task_manager.get_task_status(task_id)
                if status:
                    print(f"任务状态: {status['status']} - {status['message']} (进度: {status.get('progress', 0)}%)")
                    
                    if status['status'] in ['completed', 'failed', 'stopped']:
                        break
                
                time.sleep(2)
        else:
            print("✗ 同步任务启动失败")
    
    except Exception as e:
        print(f"✗ 创建同步任务失败: {e}")
    
    # 示例7: 查看所有配置
    print("\n=== 示例7: 查看所有配置 ===")
    
    print("数据源:")
    for source_id, source_config in config_manager.get_all_sources().items():
        print(f"  {source_id}: {source_config.get('name', '未命名')} ({source_config.get('storage_type', '未知类型')})")
    
    print("目标存储:")
    for target_id, target_config in config_manager.get_all_targets().items():
        print(f"  {target_id}: {target_config.get('name', '未命名')} ({target_config.get('storage_type', '未知类型')})")
    
    print("同步任务:")
    for task_id, task in task_manager.get_all_tasks().items():
        print(f"  {task_id}: {task.name} ({task.source_id} -> {task.target_id})")
    
    print("\n=== 示例完成 ===")


def demo_different_storage_types():
    """演示不同存储类型的配置"""
    print("\n=== 不同存储类型配置示例 ===")
    
    config_manager = UnifiedConfigManager("demo_config.json")
    
    # S3存储配置示例
    s3_configs = [
        {
            'id': 'aws_s3',
            'name': 'AWS S3',
            'config': {
                'name': 'AWS S3存储',
                'access_key': 'AKIAIOSFODNN7EXAMPLE',
                'secret_key': 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                'endpoint': 'https://s3.amazonaws.com',
                'region': 'us-east-1',
                'bucket': 'my-bucket'
            }
        },
        {
            'id': 'aliyun_oss',
            'name': '阿里云OSS',
            'config': {
                'name': '阿里云OSS存储',
                'access_key': 'LTAI4GxxxxxxxxxxxxxxxxxxxxG',
                'secret_key': 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
                'endpoint': 'https://oss-cn-hangzhou.aliyuncs.com',
                'region': 'cn-hangzhou',
                'bucket': 'my-oss-bucket'
            }
        }
    ]
    
    # SFTP存储配置示例
    sftp_configs = [
        {
            'id': 'sftp_server',
            'name': 'SFTP服务器',
            'config': {
                'name': 'Linux SFTP服务器',
                'hostname': '*************',
                'port': 22,
                'username': 'backup_user',
                'password': 'secure_password',
                'root_path': '/data/backup'
            }
        },
        {
            'id': 'sftp_key_auth',
            'name': 'SFTP密钥认证',
            'config': {
                'name': 'SFTP密钥认证服务器',
                'hostname': 'backup.example.com',
                'port': 22,
                'username': 'backup_user',
                'private_key_path': '/home/<USER>/.ssh/id_rsa',
                'private_key_passphrase': 'key_passphrase',
                'root_path': '/backup'
            }
        }
    ]
    
    # SMB存储配置示例
    smb_configs = [
        {
            'id': 'windows_share',
            'name': 'Windows共享',
            'config': {
                'name': 'Windows网络共享',
                'hostname': '*************',
                'port': 445,
                'username': 'domain\\backup_user',
                'password': 'windows_password',
                'domain': 'COMPANY',
                'share_name': 'BackupShare',
                'root_path': '/data'
            }
        }
    ]
    
    # FTP存储配置示例
    ftp_configs = [
        {
            'id': 'ftp_server',
            'name': 'FTP服务器',
            'config': {
                'name': 'FTP文件服务器',
                'hostname': 'ftp.example.com',
                'port': 21,
                'username': 'ftpuser',
                'password': 'ftppass',
                'use_tls': False,
                'passive_mode': True,
                'root_path': '/uploads'
            }
        },
        {
            'id': 'ftps_server',
            'name': 'FTPS服务器',
            'config': {
                'name': 'FTPS安全服务器',
                'hostname': 'ftps.example.com',
                'port': 990,
                'username': 'secure_user',
                'password': 'secure_pass',
                'use_tls': True,
                'passive_mode': True,
                'root_path': '/secure_data'
            }
        }
    ]
    
    # 本地存储配置示例
    local_configs = [
        {
            'id': 'local_disk',
            'name': '本地磁盘',
            'config': {
                'name': '本地数据目录',
                'root_path': '/data/local_storage'
            }
        },
        {
            'id': 'nas_mount',
            'name': '挂载的NAS',
            'config': {
                'name': '挂载的NAS设备',
                'root_path': '/mnt/nas_storage'
            }
        }
    ]
    
    # 打印配置示例
    all_configs = [
        ('S3存储', s3_configs),
        ('SFTP存储', sftp_configs),
        ('SMB存储', smb_configs),
        ('FTP存储', ftp_configs),
        ('本地存储', local_configs)
    ]
    
    for category, configs in all_configs:
        print(f"\n{category}配置示例:")
        for config in configs:
            print(f"  {config['name']}:")
            for key, value in config['config'].items():
                if 'password' in key.lower() or 'secret' in key.lower():
                    value = '*' * len(str(value))
                print(f"    {key}: {value}")


if __name__ == "__main__":
    try:
        main()
        demo_different_storage_types()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()
