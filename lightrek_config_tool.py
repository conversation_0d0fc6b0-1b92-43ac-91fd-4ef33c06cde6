#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
LightRek S3同步工具 - 配置管理工具
提供命令行界面来查看和修改优化配置
"""

import json
import sys
from lightrek_task_manager import ConfigManager


class OptimizationConfigTool:
    """优化配置管理工具"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
    
    def show_current_config(self):
        """显示当前配置"""
        print("🔧 当前优化配置")
        print("=" * 50)
        
        optimization_settings = self.config_manager.get_optimization_settings()
        
        if not optimization_settings:
            print("❌ 未找到优化配置，将使用默认设置")
            return
        
        print(f"总开关: {'启用' if optimization_settings.get('enabled', True) else '禁用'}")
        print()
        
        # 并行扫描配置
        parallel_config = optimization_settings.get("parallel_scanning", {})
        print("📊 并行扫描配置:")
        print(f"  - 启用状态: {'启用' if parallel_config.get('enabled', True) else '禁用'}")
        print(f"  - 最大工作线程: {parallel_config.get('max_workers', 5)}")
        print()
        
        # 缓存管理配置
        cache_config = optimization_settings.get("cache_management", {})
        print("💾 缓存管理配置:")
        print(f"  - 启用状态: {'启用' if cache_config.get('enabled', True) else '禁用'}")
        print(f"  - 缓存有效期: {cache_config.get('cache_validity_minutes', 60)} 分钟")
        print(f"  - 最大缓存保留: {cache_config.get('max_cache_age_days', 7)} 天")
        print(f"  - 缓存数据库: {cache_config.get('cache_database', 'lightrek_cache.db')}")
        print()
        

        
        # 流式处理配置
        stream_config = optimization_settings.get("streaming_processing", {})
        print("🔄 流式处理配置:")
        print(f"  - 启用状态: {'启用' if stream_config.get('enabled', True) else '禁用'}")
        print(f"  - 批处理大小: {stream_config.get('batch_size', 5000)} 个文件")
    
    def interactive_config(self):
        """交互式配置修改"""
        print("🔧 交互式优化配置修改")
        print("=" * 50)
        
        optimization_settings = self.config_manager.get_optimization_settings()
        
        # 如果没有配置，创建默认配置
        if not optimization_settings:
            optimization_settings = {
                "enabled": True,
                "parallel_scanning": {
                    "enabled": True,
                    "max_workers": 5
                },
                "cache_management": {
                    "enabled": True,
                    "cache_validity_minutes": 60,
                    "max_cache_age_days": 7,
                    "cache_database": "lightrek_cache.db"
                },

                "streaming_processing": {
                    "enabled": True,
                    "batch_size": 5000
                }
            }
        
        try:
            # 总开关配置
            current_enabled = optimization_settings.get("enabled", True)
            response = input(f"启用优化功能? (当前: {'是' if current_enabled else '否'}) [y/n/回车保持当前]: ").strip().lower()
            if response == 'y':
                optimization_settings["enabled"] = True
            elif response == 'n':
                optimization_settings["enabled"] = False
            
            # 并行扫描配置
            print("\n📊 并行扫描配置:")
            parallel_config = optimization_settings.setdefault("parallel_scanning", {})
            
            current_parallel_enabled = parallel_config.get("enabled", True)
            response = input(f"启用并行扫描? (当前: {'是' if current_parallel_enabled else '否'}) [y/n/回车保持当前]: ").strip().lower()
            if response == 'y':
                parallel_config["enabled"] = True
            elif response == 'n':
                parallel_config["enabled"] = False
            
            current_workers = parallel_config.get("max_workers", 5)
            response = input(f"并行工作线程数? (当前: {current_workers}) [回车保持当前]: ").strip()
            if response.isdigit():
                parallel_config["max_workers"] = int(response)
            
            # 缓存管理配置
            print("\n💾 缓存管理配置:")
            cache_config = optimization_settings.setdefault("cache_management", {})
            
            current_cache_enabled = cache_config.get("enabled", True)
            response = input(f"启用缓存管理? (当前: {'是' if current_cache_enabled else '否'}) [y/n/回车保持当前]: ").strip().lower()
            if response == 'y':
                cache_config["enabled"] = True
            elif response == 'n':
                cache_config["enabled"] = False
            
            current_validity = cache_config.get("cache_validity_minutes", 60)
            response = input(f"缓存有效期(分钟)? (当前: {current_validity}) [回车保持当前]: ").strip()
            if response.isdigit():
                cache_config["cache_validity_minutes"] = int(response)
            
            current_max_age = cache_config.get("max_cache_age_days", 7)
            response = input(f"最大缓存保留天数? (当前: {current_max_age}) [回车保持当前]: ").strip()
            if response.isdigit():
                cache_config["max_cache_age_days"] = int(response)
            

            
            # 流式处理配置
            print("\n🔄 流式处理配置:")
            stream_config = optimization_settings.setdefault("streaming_processing", {})
            
            current_stream_enabled = stream_config.get("enabled", True)
            response = input(f"启用流式处理? (当前: {'是' if current_stream_enabled else '否'}) [y/n/回车保持当前]: ").strip().lower()
            if response == 'y':
                stream_config["enabled"] = True
            elif response == 'n':
                stream_config["enabled"] = False
            
            current_batch_size = stream_config.get("batch_size", 5000)
            response = input(f"批处理大小? (当前: {current_batch_size}) [回车保持当前]: ").strip()
            if response.isdigit():
                stream_config["batch_size"] = int(response)
            
            # 保存配置
            if self.config_manager.update_optimization_settings(optimization_settings):
                print("\n✅ 配置保存成功！")
                print("📝 重启程序后新配置将生效")
            else:
                print("\n❌ 配置保存失败！")
                
        except KeyboardInterrupt:
            print("\n\n❌ 配置修改被取消")
        except Exception as e:
            print(f"\n❌ 配置修改出错: {e}")
    
    def preset_configs(self):
        """预设配置方案"""
        print("🎯 预设配置方案")
        print("=" * 50)
        
        presets = {
            "1": {
                "name": "高性能模式",
                "description": "适合大文件量场景，启用所有优化功能",
                "config": {
                    "enabled": True,
                    "parallel_scanning": {"enabled": True, "max_workers": 8},
                    "cache_management": {"enabled": True, "cache_validity_minutes": 120, "max_cache_age_days": 14, "cache_database": "lightrek_cache.db"},

                    "streaming_processing": {"enabled": True, "batch_size": 10000}
                }
            },
            "2": {
                "name": "平衡模式",
                "description": "默认配置，适合大多数场景",
                "config": {
                    "enabled": True,
                    "parallel_scanning": {"enabled": True, "max_workers": 5},
                    "cache_management": {"enabled": True, "cache_validity_minutes": 60, "max_cache_age_days": 7, "cache_database": "lightrek_cache.db"},

                    "streaming_processing": {"enabled": True, "batch_size": 5000}
                }
            },
            "3": {
                "name": "兼容模式",
                "description": "仅启用基础优化，适合资源受限环境",
                "config": {
                    "enabled": True,
                    "parallel_scanning": {"enabled": True, "max_workers": 2},
                    "cache_management": {"enabled": False, "cache_validity_minutes": 30, "max_cache_age_days": 3, "cache_database": "lightrek_cache.db"},

                    "streaming_processing": {"enabled": True, "batch_size": 2000}
                }
            },
            "4": {
                "name": "禁用优化",
                "description": "关闭所有优化功能，使用原始方式",
                "config": {
                    "enabled": False,
                    "parallel_scanning": {"enabled": False, "max_workers": 1},
                    "cache_management": {"enabled": False, "cache_validity_minutes": 0, "max_cache_age_days": 0, "cache_database": "lightrek_cache.db"},

                    "streaming_processing": {"enabled": False, "batch_size": 1000}
                }
            }
        }
        
        for key, preset in presets.items():
            print(f"{key}. {preset['name']}")
            print(f"   {preset['description']}")
        
        print()
        choice = input("请选择预设配置 [1-4]: ").strip()
        
        if choice in presets:
            if self.config_manager.update_optimization_settings(presets[choice]["config"]):
                print(f"\n✅ 已应用 '{presets[choice]['name']}' 配置！")
                print("📝 重启程序后新配置将生效")
            else:
                print("\n❌ 配置保存失败！")
        else:
            print("❌ 无效选择")
    
    def reset_config(self):
        """重置为默认配置"""
        print("🔄 重置优化配置")
        print("=" * 50)
        
        response = input("确定要重置为默认配置吗？这将覆盖所有当前设置 [y/N]: ").strip().lower()
        
        if response == 'y':
            default_config = {
                "enabled": True,
                "parallel_scanning": {"enabled": True, "max_workers": 5},
                "cache_management": {"enabled": True, "cache_validity_minutes": 60, "max_cache_age_days": 7, "cache_database": "lightrek_cache.db"},
                "streaming_processing": {"enabled": True, "batch_size": 5000}
            }
            
            if self.config_manager.update_optimization_settings(default_config):
                print("✅ 配置已重置为默认值！")
                print("📝 重启程序后新配置将生效")
            else:
                print("❌ 配置重置失败！")
        else:
            print("❌ 重置操作已取消")


def main():
    """主函数"""
    tool = OptimizationConfigTool()
    
    while True:
        print("\n🔧 LightRek S3同步优化配置工具")
        print("=" * 50)
        print("1. 查看当前配置")
        print("2. 交互式修改配置")
        print("3. 应用预设配置")
        print("4. 重置为默认配置")
        print("5. 退出")
        print()
        
        choice = input("请选择操作 [1-5]: ").strip()
        
        if choice == "1":
            tool.show_current_config()
        elif choice == "2":
            tool.interactive_config()
        elif choice == "3":
            tool.preset_configs()
        elif choice == "4":
            tool.reset_config()
        elif choice == "5":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请输入 1-5")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，再见！")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        import traceback
        traceback.print_exc() 