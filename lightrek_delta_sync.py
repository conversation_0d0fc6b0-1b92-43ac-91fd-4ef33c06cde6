#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
LightRek S3同步工具 - 差异同步模块
实现字节级增量同步，只传输文件的变化部分
"""

import hashlib
import json
import sqlite3
import io
import threading
from typing import Dict, List, Tuple, Optional, Any, BinaryIO
from datetime import datetime
import os
import tempfile

class DeltaSyncManager:
    """差异同步管理器"""
    
    def __init__(self, delta_db_path: str = "lightrek_delta.db", chunk_size: int = 64*1024):
        self.delta_db_path = delta_db_path
        self.chunk_size = chunk_size  # 64KB块大小
        self.lock = threading.Lock()
        self._init_delta_db()
    
    def _init_delta_db(self):
        """初始化差异同步数据库"""
        with sqlite3.connect(self.delta_db_path) as conn:
            cursor = conn.cursor()
            
            # 文件签名表 - 存储文件的块签名信息
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS file_signatures (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_key TEXT NOT NULL,
                    bucket_name TEXT NOT NULL,
                    file_size INTEGER NOT NULL,
                    total_chunks INTEGER NOT NULL,
                    chunk_size INTEGER NOT NULL,
                    file_hash TEXT NOT NULL,
                    last_modified TEXT NOT NULL,
                    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(file_key, bucket_name)
                )
            ''')
            
            # 块签名表 - 存储每个块的签名
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS chunk_signatures (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    signature_id INTEGER NOT NULL,
                    chunk_index INTEGER NOT NULL,
                    chunk_offset INTEGER NOT NULL,
                    chunk_size INTEGER NOT NULL,
                    chunk_hash TEXT NOT NULL,
                    weak_checksum INTEGER NOT NULL,
                    FOREIGN KEY (signature_id) REFERENCES file_signatures (id),
                    UNIQUE(signature_id, chunk_index)
                )
            ''')
            
            # 索引优化
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_file_key ON file_signatures(file_key, bucket_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_chunk_hash ON chunk_signatures(chunk_hash)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_weak_checksum ON chunk_signatures(weak_checksum)')
            
            conn.commit()
    
    def _calculate_weak_checksum(self, data: bytes) -> int:
        """计算弱校验和（类似rsync的rolling hash）"""
        if not data:
            return 0
        
        # 简化的Adler-32算法
        a, b = 1, 0
        for byte in data:
            a = (a + byte) % 65521
            b = (b + a) % 65521
        return (b << 16) | a
    
    def _calculate_strong_checksum(self, data: bytes) -> str:
        """计算强校验和（SHA-256）"""
        return hashlib.sha256(data).hexdigest()
    
    def _calculate_file_hash(self, data: bytes) -> str:
        """计算文件的整体hash"""
        return hashlib.md5(data).hexdigest()
    
    def generate_file_signature(self, file_key: str, bucket_name: str, file_data: bytes) -> int:
        """为文件生成块签名"""
        file_size = len(file_data)
        file_hash = self._calculate_file_hash(file_data)
        total_chunks = (file_size + self.chunk_size - 1) // self.chunk_size
        
        with self.lock:
            with sqlite3.connect(self.delta_db_path) as conn:
                cursor = conn.cursor()
                
                # 插入文件签名记录
                cursor.execute('''
                    INSERT OR REPLACE INTO file_signatures 
                    (file_key, bucket_name, file_size, total_chunks, chunk_size, file_hash, last_modified)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (file_key, bucket_name, file_size, total_chunks, self.chunk_size, 
                      file_hash, datetime.now().isoformat()))
                
                signature_id = cursor.lastrowid
                
                # 删除旧的块签名
                cursor.execute('DELETE FROM chunk_signatures WHERE signature_id = ?', (signature_id,))
                
                # 生成块签名
                for chunk_index in range(total_chunks):
                    offset = chunk_index * self.chunk_size
                    chunk_data = file_data[offset:offset + self.chunk_size]
                    
                    if chunk_data:  # 跳过空块
                        weak_checksum = self._calculate_weak_checksum(chunk_data)
                        strong_checksum = self._calculate_strong_checksum(chunk_data)
                        
                        cursor.execute('''
                            INSERT INTO chunk_signatures 
                            (signature_id, chunk_index, chunk_offset, chunk_size, chunk_hash, weak_checksum)
                            VALUES (?, ?, ?, ?, ?, ?)
                        ''', (signature_id, chunk_index, offset, len(chunk_data), 
                              strong_checksum, weak_checksum))
                
                conn.commit()
                return signature_id
    
    def get_file_signature(self, file_key: str, bucket_name: str) -> Optional[Dict[str, Any]]:
        """获取文件的签名信息"""
        with sqlite3.connect(self.delta_db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM file_signatures 
                WHERE file_key = ? AND bucket_name = ?
            ''', (file_key, bucket_name))
            
            signature = cursor.fetchone()
            if not signature:
                return None
            
            # 获取块签名
            cursor.execute('''
                SELECT * FROM chunk_signatures 
                WHERE signature_id = ? 
                ORDER BY chunk_index
            ''', (signature['id'],))
            
            chunks = [dict(row) for row in cursor.fetchall()]
            
            return {
                'signature_id': signature['id'],
                'file_key': file_key,
                'bucket_name': bucket_name,
                'file_size': signature['file_size'],
                'total_chunks': signature['total_chunks'],
                'chunk_size': signature['chunk_size'],
                'file_hash': signature['file_hash'],
                'last_modified': signature['last_modified'],
                'chunks': chunks
            }
    
    def calculate_delta(self, source_data: bytes, target_signature: Dict[str, Any]) -> List[Dict[str, Any]]:
        """计算源文件与目标文件的差异"""
        if not target_signature:
            # 目标文件不存在，全量上传
            return [{'operation': 'upload_all', 'data': source_data}]
        
        # 检查文件hash是否相同
        source_hash = self._calculate_file_hash(source_data)
        if source_hash == target_signature['file_hash']:
            return [{'operation': 'no_change'}]
        
        # 构建目标文件的块hash查找表
        target_chunks = {}
        target_weak_checksums = {}
        
        for chunk in target_signature['chunks']:
            chunk_hash = chunk['chunk_hash']
            weak_checksum = chunk['weak_checksum']
            chunk_index = chunk['chunk_index']
            
            target_chunks[chunk_hash] = chunk
            if weak_checksum not in target_weak_checksums:
                target_weak_checksums[weak_checksum] = []
            target_weak_checksums[weak_checksum].append(chunk)
        
        delta_operations = []
        source_size = len(source_data)
        source_offset = 0
        
        # 滑动窗口算法检测匹配的块
        while source_offset < source_size:
            chunk_data = source_data[source_offset:source_offset + self.chunk_size]
            
            if not chunk_data:
                break
            
            # 计算当前块的校验和
            weak_checksum = self._calculate_weak_checksum(chunk_data)
            strong_checksum = self._calculate_strong_checksum(chunk_data)
            
            # 检查是否存在匹配的块
            matched_chunk = None
            if weak_checksum in target_weak_checksums:
                for candidate in target_weak_checksums[weak_checksum]:
                    if candidate['chunk_hash'] == strong_checksum:
                        matched_chunk = candidate
                        break
            
            if matched_chunk:
                # 找到匹配块，记录复制操作
                delta_operations.append({
                    'operation': 'copy',
                    'source_offset': source_offset,
                    'target_chunk_index': matched_chunk['chunk_index'],
                    'size': len(chunk_data)
                })
                source_offset += len(chunk_data)
            else:
                # 没有匹配，需要上传新数据
                delta_operations.append({
                    'operation': 'upload',
                    'offset': source_offset,
                    'size': len(chunk_data),
                    'data': chunk_data
                })
                source_offset += len(chunk_data)
        
        return delta_operations
    
    def apply_delta_sync(self, source_client, target_client, file_key: str, 
                        source_data: bytes) -> Dict[str, Any]:
        """应用差异同步"""
        try:
            # 获取目标文件的签名
            target_signature = self.get_file_signature(file_key, target_client.config.bucket)
            
            # 计算差异
            delta_operations = self.calculate_delta(source_data, target_signature)
            
            # 统计信息
            total_operations = len(delta_operations)
            upload_operations = sum(1 for op in delta_operations if op['operation'] == 'upload')
            copy_operations = sum(1 for op in delta_operations if op['operation'] == 'copy')
            upload_bytes = sum(op.get('size', 0) for op in delta_operations if op['operation'] == 'upload')
            
            # 执行同步操作
            if len(delta_operations) == 1 and delta_operations[0]['operation'] == 'no_change':
                # 文件未改变
                return {
                    'success': True,
                    'message': '文件未改变，跳过同步',
                    'bytes_transferred': 0,
                    'operations': 0,
                    'optimization_ratio': 100.0
                }
            
            elif len(delta_operations) == 1 and delta_operations[0]['operation'] == 'upload_all':
                # 全量上传（目标文件不存在或首次同步）
                success = target_client.put_object(file_key, source_data)
                if success:
                    # 生成新的签名
                    self.generate_file_signature(file_key, target_client.config.bucket, source_data)
                
                return {
                    'success': success,
                    'message': '全量上传（首次同步）',
                    'bytes_transferred': len(source_data),
                    'operations': 1,
                    'optimization_ratio': 0.0
                }
            
            else:
                # 执行真正的差异同步
                print(f"🔄 执行差异同步: {upload_operations}个上传操作, {copy_operations}个复制操作")
                
                success = self._execute_delta_operations(
                    target_client, file_key, source_data, delta_operations
                )
                
                if success:
                    # 更新签名
                    self.generate_file_signature(file_key, target_client.config.bucket, source_data)
                    print(f"✅ 差异同步成功，已更新文件签名")
                
                optimization_ratio = (1.0 - upload_bytes / len(source_data)) * 100 if len(source_data) > 0 else 0
                
                return {
                    'success': success,
                    'message': f'差异同步: {upload_operations}上传/{copy_operations}复制, 优化{optimization_ratio:.1f}%',
                    'bytes_transferred': upload_bytes,
                    'operations': total_operations,
                    'optimization_ratio': optimization_ratio
                }
        
        except Exception as e:
            return {
                'success': False,
                'message': f'差异同步失败: {str(e)}',
                'bytes_transferred': 0,
                'operations': 0,
                'optimization_ratio': 0.0
            }
    
    def _execute_delta_operations(self, target_client, file_key: str, source_data: bytes,
                                 delta_operations: List[Dict[str, Any]]) -> bool:
        """执行真正的差异同步操作"""
        try:
            # 方案1: 如果变化的数据量小于阈值，使用差异重构
            upload_bytes = sum(op.get('size', 0) for op in delta_operations if op['operation'] == 'upload')
            optimization_ratio = (1.0 - upload_bytes / len(source_data)) * 100 if len(source_data) > 0 else 0
            
            # 如果优化比例低于30%，回退到全量上传
            if optimization_ratio < 30:
                return target_client.put_object(file_key, source_data)
            
            # 执行真正的差异同步
            return self._apply_real_delta_sync(target_client, file_key, source_data, delta_operations)
            
        except Exception as e:
            print(f"执行差异操作失败: {e}")
            # 出错时回退到全量上传
            return target_client.put_object(file_key, source_data)
    
    def _apply_real_delta_sync(self, target_client, file_key: str, source_data: bytes,
                              delta_operations: List[Dict[str, Any]]) -> bool:
        """应用真正的差异同步逻辑"""
        try:
            # 方案1: 分段上传差异同步
            if hasattr(target_client, 'put_object_chunked') and len(source_data) > self.chunk_size:
                return self._multipart_delta_sync(target_client, file_key, source_data, delta_operations)
            
            # 方案2: 重构文件后上传
            return self._reconstruct_and_upload(target_client, file_key, source_data, delta_operations)
            
        except Exception as e:
            print(f"差异同步执行失败: {e}")
            return False
    
    def _multipart_delta_sync(self, target_client, file_key: str, source_data: bytes,
                             delta_operations: List[Dict[str, Any]]) -> bool:
        """使用分段上传实现差异同步"""
        try:
            # 下载目标文件的必要部分
            target_data = target_client.get_object(file_key)
            if not target_data:
                return target_client.put_object(file_key, source_data)
            
            # 重构文件内容
            reconstructed_data = bytearray()
            
            for operation in delta_operations:
                if operation['operation'] == 'upload':
                    # 添加新上传的数据
                    reconstructed_data.extend(operation['data'])
                elif operation['operation'] == 'copy':
                    # 从目标文件复制数据
                    source_offset = operation['source_offset']
                    size = operation['size']
                    chunk_data = source_data[source_offset:source_offset + size]
                    reconstructed_data.extend(chunk_data)
            
            # 使用分段上传
            return target_client.put_object_chunked(file_key, bytes(reconstructed_data), 
                                                   chunk_size=self.chunk_size)
            
        except Exception as e:
            print(f"分段差异同步失败: {e}")
            return False
    
    def _reconstruct_and_upload(self, target_client, file_key: str, source_data: bytes,
                               delta_operations: List[Dict[str, Any]]) -> bool:
        """重构文件并上传（真正的差异同步实现）"""
        try:
            # 获取目标文件内容
            target_data = target_client.get_object(file_key)
            if not target_data:
                # 目标文件不存在，直接上传
                return target_client.put_object(file_key, source_data)
            
            # 按照差异操作重构文件
            reconstructed_chunks = []
            
            # 按操作顺序重构文件
            current_source_offset = 0
            
            for operation in delta_operations:
                if operation['operation'] == 'upload':
                    # 上传新数据块
                    upload_data = operation['data']
                    reconstructed_chunks.append(upload_data)
                    print(f"  📤 上传新数据: {len(upload_data)} 字节 (偏移: {operation['offset']})")
                    
                elif operation['operation'] == 'copy':
                    # 从目标文件复制数据块
                    target_chunk_index = operation['target_chunk_index']
                    chunk_offset = target_chunk_index * self.chunk_size
                    chunk_size = operation['size']
                    
                    # 从目标文件提取对应的块
                    if chunk_offset + chunk_size <= len(target_data):
                        copy_data = target_data[chunk_offset:chunk_offset + chunk_size]
                        reconstructed_chunks.append(copy_data)
                        print(f"  📋 复制数据块: {len(copy_data)} 字节 (目标块: {target_chunk_index})")
                    else:
                        # 如果超出范围，从源数据获取
                        source_offset = operation['source_offset']
                        copy_data = source_data[source_offset:source_offset + chunk_size]
                        reconstructed_chunks.append(copy_data)
                        print(f"  📋 从源复制数据: {len(copy_data)} 字节")
            
            # 重构完整文件
            reconstructed_file = b''.join(reconstructed_chunks)
            
            # 验证重构的文件是否正确
            if len(reconstructed_file) != len(source_data):
                print(f"⚠️ 重构文件大小不匹配: 期望 {len(source_data)}, 实际 {len(reconstructed_file)}")
                # 大小不匹配，直接上传源文件
                return target_client.put_object(file_key, source_data)
            
            # 验证重构文件的哈希
            source_hash = self._calculate_file_hash(source_data)
            reconstructed_hash = self._calculate_file_hash(reconstructed_file)
            
            if source_hash != reconstructed_hash:
                print(f"⚠️ 重构文件哈希不匹配，回退到全量上传")
                return target_client.put_object(file_key, source_data)
            
            # 上传重构的文件
            success = target_client.put_object(file_key, reconstructed_file)
            
            if success:
                # 计算实际节省的传输量
                upload_bytes = sum(len(op['data']) for op in delta_operations if op['operation'] == 'upload')
                total_bytes = len(source_data)
                savings_ratio = (1.0 - upload_bytes / total_bytes) * 100 if total_bytes > 0 else 0
                print(f"✅ 差异同步完成: 节省 {savings_ratio:.1f}% 传输量 ({upload_bytes}/{total_bytes} 字节)")
            
            return success
            
        except Exception as e:
            print(f"重构和上传失败: {e}")
            return False
    
    def get_delta_statistics(self) -> Dict[str, Any]:
        """获取差异同步统计信息"""
        with sqlite3.connect(self.delta_db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT COUNT(*) as total_files,
                       AVG(file_size) as avg_file_size,
                       SUM(file_size) as total_size,
                       AVG(total_chunks) as avg_chunks
                FROM file_signatures
            ''')
            
            result = cursor.fetchone()
            
            return {
                'total_files': result[0] or 0,
                'average_file_size': result[1] or 0,
                'total_size': result[2] or 0,
                'average_chunks': result[3] or 0,
                'chunk_size': self.chunk_size
            }


# 测试代码
if __name__ == "__main__":
    # 创建测试实例
    delta_manager = DeltaSyncManager()
    
    # 模拟文件数据
    original_data = b"Hello World! This is a test file with some content."
    modified_data = b"Hello Universe! This is a test file with modified content and more text."
    
    # 生成原始文件签名
    signature_id = delta_manager.generate_file_signature("test.txt", "test-bucket", original_data)
    print(f"✅ 生成签名ID: {signature_id}")
    
    # 获取签名
    signature = delta_manager.get_file_signature("test.txt", "test-bucket")
    print(f"📊 文件签名: {signature['total_chunks']} 个块, 文件大小: {signature['file_size']} 字节")
    
    # 计算差异
    delta_ops = delta_manager.calculate_delta(modified_data, signature)
    print(f"🔄 差异操作数量: {len(delta_ops)}")
    
    upload_size = 0
    for i, op in enumerate(delta_ops):
        print(f"  操作 {i+1}: {op['operation']}")
        if 'size' in op:
            print(f"    大小: {op['size']} 字节")
            if op['operation'] == 'upload':
                upload_size += op['size']
    
    # 计算优化比例
    if len(modified_data) > 0:
        optimization = (1.0 - upload_size / len(modified_data)) * 100
        print(f"📈 优化比例: {optimization:.1f}% (节省 {len(modified_data) - upload_size} 字节)")
    
    # 获取统计信息
    stats = delta_manager.get_delta_statistics()
    print(f"📋 统计信息: {stats}") 