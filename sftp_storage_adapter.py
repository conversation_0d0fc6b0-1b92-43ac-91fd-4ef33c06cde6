"""
SFTP存储适配器 - 基于paramiko库实现
"""

from storage_abstraction import StorageAdapter, SFTPStorageConfig, FileMetadata, ListResult, StorageType, StorageFactory
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import os
import stat
import posixpath
import time
try:
    import paramiko
    PARAMIKO_AVAILABLE = True
except ImportError:
    PARAMIKO_AVAILABLE = False
    paramiko = None


class SFTPStorageAdapter(StorageAdapter):
    """SFTP存储适配器"""
    
    def __init__(self, config: SFTPStorageConfig):
        if not PARAMIKO_AVAILABLE:
            raise ImportError("paramiko库未安装，请运行: pip install paramiko")
        
        super().__init__(config)
        self.config: SFTPStorageConfig = config
        self._ssh_client = None
        self._sftp_client = None
        self._connection_time = None
        self._max_connection_age = 300  # 5分钟后重新连接
        import time
        self._time = time
    
    def _connect(self) -> bool:
        """建立SFTP连接"""
        try:
            # 检查现有连接是否仍然有效
            if self._ssh_client is not None and self._sftp_client is not None:
                try:
                    # 检查连接年龄
                    if self._connection_time and (self._time.time() - self._connection_time) > self._max_connection_age:
                        print("SFTP连接超时，重新连接...")
                        self._disconnect()
                    else:
                        # 测试连接是否仍然活跃
                        transport = self._ssh_client.get_transport()
                        if transport and transport.is_active():
                            return True
                        else:
                            # 连接已断开，需要重新连接
                            print("SFTP连接已断开，重新连接...")
                            self._disconnect()
                except:
                    # 连接检查失败，重新连接
                    print("SFTP连接检查失败，重新连接...")
                    self._disconnect()
            
            self._ssh_client = paramiko.SSHClient()
            self._ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 准备连接参数
            connect_kwargs = {
                'hostname': self.config.hostname,
                'port': self.config.port,
                'username': self.config.username,
                'timeout': 60,          # 增加连接超时
                'banner_timeout': 60,   # 增加banner超时
                'auth_timeout': 60      # 增加认证超时
            }
            
            # 认证方式
            if self.config.private_key_path:
                # 使用私钥认证
                if os.path.exists(self.config.private_key_path):
                    private_key = paramiko.RSAKey.from_private_key_file(
                        self.config.private_key_path,
                        password=self.config.private_key_passphrase
                    )
                    connect_kwargs['pkey'] = private_key
                else:
                    raise FileNotFoundError(f"私钥文件不存在: {self.config.private_key_path}")
            elif self.config.password:
                # 使用密码认证
                connect_kwargs['password'] = self.config.password
            else:
                raise ValueError("必须提供密码或私钥文件")
            
            # 建立SSH连接
            self._ssh_client.connect(**connect_kwargs)
            
            # 建立SFTP连接
            self._sftp_client = self._ssh_client.open_sftp()

            # 设置SFTP客户端超时
            if hasattr(self._sftp_client, 'get_channel'):
                channel = self._sftp_client.get_channel()
                if channel:
                    channel.settimeout(120)  # 2分钟超时

            # 记录连接时间
            self._connection_time = self._time.time()

            return True
        except Exception as e:
            self._disconnect()
            raise Exception(f"SFTP连接失败: {str(e)}")
    
    def _disconnect(self):
        """断开SFTP连接"""
        if self._sftp_client:
            try:
                self._sftp_client.close()
            except:
                pass
            self._sftp_client = None

        if self._ssh_client:
            try:
                self._ssh_client.close()
            except:
                pass
            self._ssh_client = None

        # 清理连接时间
        self._connection_time = None
    
    def _normalize_path(self, path: str) -> str:
        """规范化路径"""
        if not path:
            return self.config.root_path
        
        # 使用POSIX路径（SFTP服务器通常是Unix/Linux）
        if not path.startswith('/'):
            path = posixpath.join(self.config.root_path, path)
        
        return posixpath.normpath(path)
    
    def _get_relative_path(self, full_path: str) -> str:
        """获取相对于根路径的路径"""
        root = self.config.root_path.rstrip('/')
        if full_path.startswith(root):
            relative = full_path[len(root):].lstrip('/')
            return relative if relative else ""
        return full_path
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试连接"""
        try:
            self._connect()
            
            # 测试访问根目录
            root_path = self._normalize_path("")
            try:
                attrs = self._sftp_client.stat(root_path)
                if stat.S_ISDIR(attrs.st_mode):
                    return True, f"SFTP连接成功，根目录: {root_path}"
                else:
                    return False, f"根路径不是目录: {root_path}"
            except FileNotFoundError:
                return False, f"根目录不存在: {root_path}"
            except PermissionError:
                return False, f"无权限访问根目录: {root_path}"
        except Exception as e:
            return False, f"SFTP连接失败: {str(e)}"
        finally:
            self._disconnect()
    
    def list_files(self, prefix: str = "", max_keys: int = None,
                   continuation_token: Optional[str] = None) -> ListResult:
        """列出文件"""
        try:
            self._connect()
            
            files = []
            start_path = self._normalize_path(prefix)
            
            # 如果指定了continuation_token，从该位置开始
            skip_until = continuation_token
            found_start = continuation_token is None
            
            def scan_directory(dir_path: str, current_prefix: str = ""):
                nonlocal files, found_start, skip_until

                # 移除文件数量限制，扫描所有文件
                try:
                    items = self._sftp_client.listdir_attr(dir_path)

                    for item in items:

                        item_path = posixpath.join(dir_path, item.filename)
                        relative_path = self._get_relative_path(item_path)

                        # 检查是否匹配前缀
                        if prefix and not relative_path.startswith(prefix):
                            continue

                        # 处理分页
                        if not found_start:
                            if relative_path == skip_until:
                                found_start = True
                            continue

                        # 检查文件类型，跳过特殊文件类型
                        if hasattr(item, 'st_mode') and item.st_mode is not None:
                            if stat.S_ISREG(item.st_mode):
                                # 常规文件
                                try:
                                    last_modified = datetime.fromtimestamp(item.st_mtime) if item.st_mtime else datetime.now()

                                    file_meta = FileMetadata(
                                        key=relative_path,
                                        size=item.st_size or 0,
                                        last_modified=last_modified,
                                        etag=None,  # SFTP不支持ETag
                                        content_type='binary/octet-stream'
                                    )
                                    files.append(file_meta)
                                except Exception as e:
                                    print(f"SFTP跳过文件 {relative_path}: {e}")

                            elif stat.S_ISDIR(item.st_mode):
                                # 目录，递归扫描
                                try:
                                    scan_directory(item_path, relative_path + "/")
                                except Exception as e:
                                    print(f"SFTP跳过目录 {item_path}: {e}")
                            else:
                                # 跳过其他类型的文件（符号链接、设备文件等）
                                print(f"SFTP跳过特殊文件类型: {relative_path} (mode: {oct(item.st_mode)})")
                        else:
                            # 无法确定文件类型，跳过
                            print(f"SFTP跳过未知类型文件: {relative_path}")

                except PermissionError:
                    # 跳过无权限的目录
                    pass
                except Exception:
                    # 跳过其他错误的目录
                    pass
            
            # 开始扫描
            try:
                # 检查路径是否存在以及类型
                attrs = self._sftp_client.stat(start_path)

                if stat.S_ISDIR(attrs.st_mode):
                    # 是目录，开始递归扫描
                    scan_directory(start_path)
                elif stat.S_ISREG(attrs.st_mode):
                    # 是文件，直接添加
                    relative_path = self._get_relative_path(start_path)
                    last_modified = datetime.fromtimestamp(attrs.st_mtime) if attrs.st_mtime else datetime.now()

                    file_meta = FileMetadata(
                        key=relative_path,
                        size=attrs.st_size or 0,
                        last_modified=last_modified,
                        etag=None,
                        content_type='binary/octet-stream'
                    )
                    files.append(file_meta)

            except FileNotFoundError:
                pass  # 路径不存在
            except Exception:
                pass  # 其他错误
            
            # 扫描完成，不截断
            return ListResult(
                files=files,
                is_truncated=False,  # 不截断，扫描所有文件
                next_token=None
            )
        
        except Exception as e:
            raise Exception(f"列出文件失败: {str(e)}")
        finally:
            self._disconnect()
    
    def get_file(self, key: str) -> Optional[bytes]:
        """下载文件，带重试和连接恢复"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                self._connect()

                file_path = self._normalize_path(key)
                if attempt == 0:  # 只在第一次尝试时打印调试信息
                    print(f"SFTP下载文件: {key} -> {file_path}")

                # 首先检查文件是否存在
                try:
                    attrs = self._sftp_client.stat(file_path)
                    if not stat.S_ISREG(attrs.st_mode):
                        print(f"SFTP错误: {file_path} 不是常规文件")
                        return None
                    if attempt == 0:
                        print(f"SFTP文件信息: 大小={attrs.st_size}, 权限={oct(attrs.st_mode)}")
                except FileNotFoundError:
                    print(f"SFTP错误: 文件不存在 {file_path}")
                    return None
                except Exception as e:
                    if attempt < max_retries - 1:
                        print(f"SFTP状态检查失败，重试 {attempt + 1}/{max_retries}: {e}")
                        self._disconnect()
                        time.sleep(1)
                        continue
                    else:
                        print(f"SFTP错误: 无法获取文件状态 {file_path}: {e}")
                        return None

                # 使用BytesIO来读取文件内容
                from io import BytesIO
                file_obj = BytesIO()

                try:
                    self._sftp_client.getfo(file_path, file_obj)
                    file_obj.seek(0)
                    data = file_obj.read()
                    if attempt == 0:
                        print(f"SFTP下载成功: {key}, 实际大小={len(data)}")
                    return data
                except Exception as e:
                    if attempt < max_retries - 1:
                        print(f"SFTP下载失败，重试 {attempt + 1}/{max_retries}: {key} - {e}")
                        self._disconnect()
                        time.sleep(2)  # 增加等待时间
                        continue
                    else:
                        print(f"SFTP下载失败: {key} - {e}")
                        return None

            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"SFTP连接失败，重试 {attempt + 1}/{max_retries}: {key} - {e}")
                    self._disconnect()
                    time.sleep(2)
                    continue
                else:
                    print(f"SFTP连接或其他错误: {key} - {e}")
                    return None
            finally:
                if attempt == max_retries - 1:  # 只在最后一次尝试时断开连接
                    self._disconnect()

        return None
    
    def put_file(self, key: str, data: bytes, 
                 content_type: str = 'binary/octet-stream',
                 metadata: Optional[Dict[str, str]] = None) -> bool:
        """上传文件"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 确保目录存在
            dir_path = posixpath.dirname(file_path)
            if dir_path and dir_path != '/':
                self._ensure_directory_exists(dir_path)
            
            # 使用BytesIO来上传文件内容
            from io import BytesIO
            file_obj = BytesIO(data)
            
            self._sftp_client.putfo(file_obj, file_path)
            
            # SFTP不支持自定义元数据，但可以设置文件权限
            self._sftp_client.chmod(file_path, 0o644)
            
            return True
        
        except Exception:
            return False
        finally:
            self._disconnect()
    
    def _ensure_directory_exists(self, dir_path: str):
        """确保目录存在"""
        try:
            self._sftp_client.stat(dir_path)
        except FileNotFoundError:
            # 目录不存在，递归创建
            parent_dir = posixpath.dirname(dir_path)
            if parent_dir and parent_dir != '/' and parent_dir != dir_path:
                self._ensure_directory_exists(parent_dir)
            
            self._sftp_client.mkdir(dir_path)
    
    def delete_file(self, key: str) -> bool:
        """删除文件"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            self._sftp_client.remove(file_path)
            return True
        
        except Exception:
            return False
        finally:
            self._disconnect()
    
    def get_file_metadata(self, key: str) -> Optional[FileMetadata]:
        """获取文件元数据"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            attrs = self._sftp_client.stat(file_path)
            
            if stat.S_ISREG(attrs.st_mode):
                last_modified = datetime.fromtimestamp(attrs.st_mtime) if attrs.st_mtime else datetime.now()
                
                return FileMetadata(
                    key=key,
                    size=attrs.st_size or 0,
                    last_modified=last_modified,
                    etag=None,  # SFTP不支持ETag
                    content_type='binary/octet-stream'
                )
            return None
        
        except Exception:
            return None
        finally:
            self._disconnect()
    
    def file_exists(self, key: str) -> bool:
        """检查文件是否存在"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            attrs = self._sftp_client.stat(file_path)
            return stat.S_ISREG(attrs.st_mode)
        
        except Exception:
            return False
        finally:
            self._disconnect()


# 注册SFTP适配器
if PARAMIKO_AVAILABLE:
    StorageFactory.register_adapter(StorageType.SFTP, SFTPStorageAdapter)
