"""
SMB存储适配器 - 基于smbprotocol库实现
"""

from storage_abstraction import StorageAdapter, SMBStorageConfig, FileMetadata, ListResult, StorageType, StorageFactory
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import os
import ntpath
import uuid

try:
    from smbprotocol.connection import Connection
    from smbprotocol.session import Session
    from smbprotocol.tree import TreeConnect
    from smbprotocol.file import File, CreateDisposition, CreateOptions, FileAttributes, FileAccessMask
    from smbprotocol.open import Open
    from smbprotocol import exceptions as smb_exceptions
    SMBPROTOCOL_AVAILABLE = True
except ImportError:
    SMBPROTOCOL_AVAILABLE = False


class SMBStorageAdapter(StorageAdapter):
    """SMB存储适配器"""
    
    def __init__(self, config: SMBStorageConfig):
        if not SMBPROTOCOL_AVAILABLE:
            raise ImportError("smbprotocol库未安装，请运行: pip install smbprotocol")
        
        super().__init__(config)
        self.config: SMBStorageConfig = config
        self._connection = None
        self._session = None
        self._tree = None
    
    def _connect(self) -> bool:
        """建立SMB连接"""
        try:
            if self._connection is not None and self._connection.connected:
                return True
            
            # 建立连接
            self._connection = Connection(
                uuid.uuid4(),
                self.config.hostname,
                self.config.port
            )
            self._connection.connect()
            
            # 建立会话
            self._session = Session(self._connection, self.config.username, self.config.password)
            self._session.connect()
            
            # 连接到共享
            share_path = f"\\\\{self.config.hostname}\\{self.config.share_name}"
            self._tree = TreeConnect(self._session, share_path)
            self._tree.connect()
            
            return True
        except Exception as e:
            self._disconnect()
            raise Exception(f"SMB连接失败: {str(e)}")
    
    def _disconnect(self):
        """断开SMB连接"""
        if self._tree:
            try:
                self._tree.disconnect()
            except:
                pass
            self._tree = None
        
        if self._session:
            try:
                self._session.disconnect()
            except:
                pass
            self._session = None
        
        if self._connection:
            try:
                self._connection.disconnect()
            except:
                pass
            self._connection = None
    
    def _normalize_path(self, path: str) -> str:
        """规范化路径"""
        if not path:
            return self.config.root_path
        
        # 使用Windows路径分隔符
        if not path.startswith('\\') and not path.startswith('/'):
            path = ntpath.join(self.config.root_path, path)
        
        # 转换为Windows路径格式
        path = path.replace('/', '\\')
        return ntpath.normpath(path)
    
    def _get_relative_path(self, full_path: str) -> str:
        """获取相对于根路径的路径"""
        root = self.config.root_path.rstrip('\\').rstrip('/')
        if full_path.startswith(root):
            relative = full_path[len(root):].lstrip('\\').lstrip('/')
            return relative.replace('\\', '/') if relative else ""
        return full_path.replace('\\', '/')
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试连接"""
        try:
            self._connect()
            
            # 测试访问根目录
            root_path = self._normalize_path("")
            try:
                file_obj = File(self._tree, root_path)
                file_obj.open(
                    desired_access=FileAccessMask.GENERIC_READ,
                    create_disposition=CreateDisposition.FILE_OPEN
                )
                file_info = file_obj.query_info()
                file_obj.close()
                
                if file_info['file_attributes'] & FileAttributes.FILE_ATTRIBUTE_DIRECTORY:
                    return True, f"SMB连接成功，共享: \\\\{self.config.hostname}\\{self.config.share_name}"
                else:
                    return False, f"根路径不是目录: {root_path}"
            except smb_exceptions.SMBResponseException as e:
                if e.status == 0xC0000034:  # STATUS_OBJECT_NAME_NOT_FOUND
                    return False, f"根目录不存在: {root_path}"
                elif e.status == 0xC0000022:  # STATUS_ACCESS_DENIED
                    return False, f"无权限访问根目录: {root_path}"
                else:
                    return False, f"访问根目录失败: {str(e)}"
        except Exception as e:
            return False, f"SMB连接失败: {str(e)}"
        finally:
            self._disconnect()
    
    def list_files(self, prefix: str = "", max_keys: int = 1000, 
                   continuation_token: Optional[str] = None) -> ListResult:
        """列出文件"""
        try:
            self._connect()
            
            files = []
            start_path = self._normalize_path(prefix)
            
            # 如果指定了continuation_token，从该位置开始
            skip_until = continuation_token
            found_start = continuation_token is None
            
            def scan_directory(dir_path: str):
                nonlocal files, found_start, skip_until
                
                if len(files) >= max_keys:
                    return
                
                try:
                    # 打开目录
                    dir_obj = File(self._tree, dir_path)
                    dir_obj.open(
                        desired_access=FileAccessMask.GENERIC_READ,
                        create_disposition=CreateDisposition.FILE_OPEN
                    )
                    
                    # 查询目录内容
                    for item in dir_obj.query_directory():
                        if len(files) >= max_keys:
                            break
                        
                        # 跳过 . 和 ..
                        if item['file_name'] in ['.', '..']:
                            continue
                        
                        item_path = ntpath.join(dir_path, item['file_name'])
                        relative_path = self._get_relative_path(item_path)
                        
                        # 检查是否匹配前缀
                        if prefix and not relative_path.startswith(prefix.replace('\\', '/')):
                            continue
                        
                        # 处理分页
                        if not found_start:
                            if relative_path == skip_until:
                                found_start = True
                            continue
                        
                        if not (item['file_attributes'] & FileAttributes.FILE_ATTRIBUTE_DIRECTORY):
                            # 文件
                            last_modified = datetime.fromtimestamp(item['last_write_time'] / 10000000 - 11644473600)
                            
                            file_meta = FileMetadata(
                                key=relative_path,
                                size=item['end_of_file'],
                                last_modified=last_modified,
                                etag=None,  # SMB不支持ETag
                                content_type='binary/octet-stream'
                            )
                            files.append(file_meta)
                        else:
                            # 递归扫描子目录
                            scan_directory(item_path)
                    
                    dir_obj.close()
                
                except smb_exceptions.SMBResponseException:
                    # 跳过无权限或其他错误的目录
                    pass
                except Exception:
                    # 跳过其他错误的目录
                    pass
            
            # 开始扫描
            scan_directory(start_path)
            
            # 确定是否还有更多文件
            is_truncated = len(files) >= max_keys
            next_token = files[-1].key if is_truncated and files else None
            
            return ListResult(
                files=files,
                is_truncated=is_truncated,
                next_token=next_token
            )
        
        except Exception as e:
            raise Exception(f"列出文件失败: {str(e)}")
        finally:
            self._disconnect()
    
    def get_file(self, key: str) -> Optional[bytes]:
        """下载文件"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 打开文件
            file_obj = File(self._tree, file_path)
            file_obj.open(
                desired_access=FileAccessMask.GENERIC_READ,
                create_disposition=CreateDisposition.FILE_OPEN
            )
            
            # 读取文件内容
            file_size = file_obj.query_info()['end_of_file']
            data = file_obj.read(0, file_size)
            
            file_obj.close()
            return data
        
        except Exception:
            return None
        finally:
            self._disconnect()
    
    def put_file(self, key: str, data: bytes, 
                 content_type: str = 'binary/octet-stream',
                 metadata: Optional[Dict[str, str]] = None) -> bool:
        """上传文件"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 确保目录存在
            dir_path = ntpath.dirname(file_path)
            if dir_path and dir_path != '\\':
                self._ensure_directory_exists(dir_path)
            
            # 创建/打开文件
            file_obj = File(self._tree, file_path)
            file_obj.open(
                desired_access=FileAccessMask.GENERIC_WRITE,
                create_disposition=CreateDisposition.FILE_OVERWRITE_IF
            )
            
            # 写入文件内容
            file_obj.write(data, 0)
            file_obj.close()
            
            return True
        
        except Exception:
            return False
        finally:
            self._disconnect()
    
    def _ensure_directory_exists(self, dir_path: str):
        """确保目录存在"""
        try:
            # 尝试打开目录
            dir_obj = File(self._tree, dir_path)
            dir_obj.open(
                desired_access=FileAccessMask.GENERIC_READ,
                create_disposition=CreateDisposition.FILE_OPEN
            )
            dir_obj.close()
        except smb_exceptions.SMBResponseException as e:
            if e.status == 0xC0000034:  # STATUS_OBJECT_NAME_NOT_FOUND
                # 目录不存在，递归创建
                parent_dir = ntpath.dirname(dir_path)
                if parent_dir and parent_dir != '\\' and parent_dir != dir_path:
                    self._ensure_directory_exists(parent_dir)
                
                # 创建目录
                dir_obj = File(self._tree, dir_path)
                dir_obj.open(
                    desired_access=FileAccessMask.GENERIC_WRITE,
                    create_disposition=CreateDisposition.FILE_CREATE,
                    create_options=CreateOptions.FILE_DIRECTORY_FILE
                )
                dir_obj.close()
    
    def delete_file(self, key: str) -> bool:
        """删除文件"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 打开文件并删除
            file_obj = File(self._tree, file_path)
            file_obj.open(
                desired_access=FileAccessMask.DELETE,
                create_disposition=CreateDisposition.FILE_OPEN
            )
            file_obj.delete()
            file_obj.close()
            
            return True
        
        except Exception:
            return False
        finally:
            self._disconnect()
    
    def get_file_metadata(self, key: str) -> Optional[FileMetadata]:
        """获取文件元数据"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 打开文件
            file_obj = File(self._tree, file_path)
            file_obj.open(
                desired_access=FileAccessMask.GENERIC_READ,
                create_disposition=CreateDisposition.FILE_OPEN
            )
            
            # 查询文件信息
            file_info = file_obj.query_info()
            file_obj.close()
            
            if not (file_info['file_attributes'] & FileAttributes.FILE_ATTRIBUTE_DIRECTORY):
                last_modified = datetime.fromtimestamp(file_info['last_write_time'] / 10000000 - 11644473600)
                
                return FileMetadata(
                    key=key,
                    size=file_info['end_of_file'],
                    last_modified=last_modified,
                    etag=None,  # SMB不支持ETag
                    content_type='binary/octet-stream'
                )
            return None
        
        except Exception:
            return None
        finally:
            self._disconnect()
    
    def file_exists(self, key: str) -> bool:
        """检查文件是否存在"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 尝试打开文件
            file_obj = File(self._tree, file_path)
            file_obj.open(
                desired_access=FileAccessMask.GENERIC_READ,
                create_disposition=CreateDisposition.FILE_OPEN
            )
            file_info = file_obj.query_info()
            file_obj.close()
            
            # 检查是否为文件（非目录）
            return not (file_info['file_attributes'] & FileAttributes.FILE_ATTRIBUTE_DIRECTORY)
        
        except Exception:
            return False
        finally:
            self._disconnect()


# 注册SMB适配器
if SMBPROTOCOL_AVAILABLE:
    StorageFactory.register_adapter(StorageType.SMB, SMBStorageAdapter)
