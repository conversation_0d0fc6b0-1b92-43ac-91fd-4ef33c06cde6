"""
统一存储系统测试
"""

import os
import tempfile
import shutil
import unittest
from unittest.mock import Mock, patch
from datetime import datetime

# 导入要测试的模块
from storage_abstraction import StorageType, StorageFactory, FileMetadata, ListResult
from local_storage_adapter import LocalStorageAdapter, LocalStorageConfig
from unified_config_manager import UnifiedConfigManager
from unified_task_manager import UnifiedTaskManager


class TestLocalStorageAdapter(unittest.TestCase):
    """本地存储适配器测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = tempfile.mkdtemp()
        self.config = LocalStorageConfig(
            root_path=self.test_dir,
            name="测试本地存储",
            description="用于单元测试的本地存储"
        )
        self.adapter = LocalStorageAdapter(self.config)
        
        # 创建测试文件
        self.test_files = {
            'test1.txt': b'Hello World',
            'subdir/test2.txt': b'Test file 2',
            'subdir/test3.txt': b'Test file 3'
        }
        
        for file_path, content in self.test_files.items():
            full_path = os.path.join(self.test_dir, file_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            with open(full_path, 'wb') as f:
                f.write(content)
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_connection(self):
        """测试连接"""
        success, message = self.adapter.test_connection()
        self.assertTrue(success)
        self.assertIn("连接成功", message)
    
    def test_list_files(self):
        """测试文件列表"""
        result = self.adapter.list_files()
        self.assertIsInstance(result, ListResult)
        self.assertEqual(len(result.files), 3)
        
        # 检查文件名
        file_keys = [f.key for f in result.files]
        self.assertIn('test1.txt', file_keys)
        self.assertIn('subdir/test2.txt', file_keys)
        self.assertIn('subdir/test3.txt', file_keys)
    
    def test_get_file(self):
        """测试文件下载"""
        content = self.adapter.get_file('test1.txt')
        self.assertEqual(content, b'Hello World')
        
        # 测试不存在的文件
        content = self.adapter.get_file('nonexistent.txt')
        self.assertIsNone(content)
    
    def test_put_file(self):
        """测试文件上传"""
        test_content = b'New test file'
        success = self.adapter.put_file('new_file.txt', test_content)
        self.assertTrue(success)
        
        # 验证文件是否创建
        self.assertTrue(self.adapter.file_exists('new_file.txt'))
        
        # 验证文件内容
        content = self.adapter.get_file('new_file.txt')
        self.assertEqual(content, test_content)
    
    def test_delete_file(self):
        """测试文件删除"""
        # 确保文件存在
        self.assertTrue(self.adapter.file_exists('test1.txt'))
        
        # 删除文件
        success = self.adapter.delete_file('test1.txt')
        self.assertTrue(success)
        
        # 验证文件已删除
        self.assertFalse(self.adapter.file_exists('test1.txt'))
    
    def test_get_file_metadata(self):
        """测试获取文件元数据"""
        metadata = self.adapter.get_file_metadata('test1.txt')
        self.assertIsInstance(metadata, FileMetadata)
        self.assertEqual(metadata.key, 'test1.txt')
        self.assertEqual(metadata.size, 11)  # "Hello World" 的长度
        self.assertIsInstance(metadata.last_modified, datetime)
    
    def test_file_exists(self):
        """测试文件存在检查"""
        self.assertTrue(self.adapter.file_exists('test1.txt'))
        self.assertFalse(self.adapter.file_exists('nonexistent.txt'))


class TestUnifiedConfigManager(unittest.TestCase):
    """统一配置管理器测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_config_file = tempfile.mktemp(suffix='.json')
        self.config_manager = UnifiedConfigManager(self.test_config_file)
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.test_config_file):
            os.remove(self.test_config_file)
    
    def test_add_local_source(self):
        """测试添加本地数据源"""
        config_data = {
            'name': '测试本地源',
            'description': '测试用本地数据源',
            'root_path': '/tmp/test_source'
        }
        
        success = self.config_manager.add_source('test_local', 'local', config_data)
        self.assertTrue(success)
        
        # 验证配置已保存
        sources = self.config_manager.get_all_sources()
        self.assertIn('test_local', sources)
        self.assertEqual(sources['test_local']['storage_type'], 'local')
    
    def test_add_s3_target(self):
        """测试添加S3目标存储"""
        config_data = {
            'name': '测试S3目标',
            'description': '测试用S3目标存储',
            'access_key': 'test_key',
            'secret_key': 'test_secret',
            'endpoint': 'https://s3.amazonaws.com',
            'region': 'us-east-1',
            'bucket': 'test-bucket'
        }
        
        success = self.config_manager.add_target('test_s3', 's3', config_data)
        self.assertTrue(success)
        
        # 验证配置已保存
        targets = self.config_manager.get_all_targets()
        self.assertIn('test_s3', targets)
        self.assertEqual(targets['test_s3']['storage_type'], 's3')
    
    def test_get_supported_storage_types(self):
        """测试获取支持的存储类型"""
        storage_types = self.config_manager.get_supported_storage_types()
        self.assertIsInstance(storage_types, list)
        self.assertGreater(len(storage_types), 0)
        
        # 检查是否包含基本存储类型
        type_names = [st['type'] for st in storage_types]
        self.assertIn('s3', type_names)
        self.assertIn('local', type_names)


class TestUnifiedTaskManager(unittest.TestCase):
    """统一任务管理器测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_config_file = tempfile.mktemp(suffix='.json')
        self.config_manager = UnifiedConfigManager(self.test_config_file)
        self.task_manager = UnifiedTaskManager(self.config_manager)
        
        # 创建测试存储配置
        self.test_dir = tempfile.mkdtemp()
        local_config = {
            'name': '测试本地存储',
            'root_path': self.test_dir
        }
        
        self.config_manager.add_source('test_source', 'local', local_config)
        self.config_manager.add_target('test_target', 'local', local_config)
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.test_config_file):
            os.remove(self.test_config_file)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_create_task(self):
        """测试创建任务"""
        task_id = self.task_manager.create_task(
            name="测试同步任务",
            description="用于单元测试的同步任务",
            source_id="test_source",
            target_id="test_target"
        )
        
        self.assertIsInstance(task_id, str)
        
        # 验证任务已创建
        task = self.task_manager.get_task(task_id)
        self.assertIsNotNone(task)
        self.assertEqual(task.name, "测试同步任务")
    
    def test_get_all_tasks(self):
        """测试获取所有任务"""
        # 创建几个测试任务
        task_id1 = self.task_manager.create_task(
            name="任务1", description="测试任务1",
            source_id="test_source", target_id="test_target"
        )
        task_id2 = self.task_manager.create_task(
            name="任务2", description="测试任务2",
            source_id="test_source", target_id="test_target"
        )
        
        tasks = self.task_manager.get_all_tasks()
        self.assertEqual(len(tasks), 2)
        self.assertIn(task_id1, tasks)
        self.assertIn(task_id2, tasks)


class TestStorageFactory(unittest.TestCase):
    """存储工厂测试"""
    
    def test_create_local_adapter(self):
        """测试创建本地存储适配器"""
        config = LocalStorageConfig(
            root_path=tempfile.gettempdir(),
            name="测试本地存储"
        )
        
        adapter = StorageFactory.create_adapter(config)
        self.assertIsInstance(adapter, LocalStorageAdapter)
    
    def test_get_supported_types(self):
        """测试获取支持的存储类型"""
        supported_types = StorageFactory.get_supported_types()
        self.assertIsInstance(supported_types, list)
        self.assertIn(StorageType.LOCAL, supported_types)


class TestFileMetadata(unittest.TestCase):
    """文件元数据测试"""
    
    def test_to_dict(self):
        """测试转换为字典"""
        metadata = FileMetadata(
            key="test/file.txt",
            size=1024,
            last_modified=datetime.now(),
            etag="abc123",
            content_type="text/plain"
        )
        
        result = metadata.to_dict()
        self.assertIsInstance(result, dict)
        self.assertEqual(result['Key'], "test/file.txt")
        self.assertEqual(result['Size'], 1024)
        self.assertEqual(result['ETag'], "abc123")
        self.assertEqual(result['ContentType'], "text/plain")


def run_integration_test():
    """集成测试 - 测试完整的同步流程"""
    print("开始集成测试...")
    
    # 创建临时目录
    source_dir = tempfile.mkdtemp(prefix='source_')
    target_dir = tempfile.mkdtemp(prefix='target_')
    
    try:
        # 在源目录创建测试文件
        test_files = {
            'file1.txt': b'Content of file 1',
            'dir1/file2.txt': b'Content of file 2',
            'dir1/file3.txt': b'Content of file 3'
        }
        
        for file_path, content in test_files.items():
            full_path = os.path.join(source_dir, file_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            with open(full_path, 'wb') as f:
                f.write(content)
        
        # 创建配置管理器
        config_manager = UnifiedConfigManager(tempfile.mktemp(suffix='.json'))
        
        # 配置源和目标存储
        source_config = {'name': '源存储', 'root_path': source_dir}
        target_config = {'name': '目标存储', 'root_path': target_dir}
        
        config_manager.add_source('source', 'local', source_config)
        config_manager.add_target('target', 'local', target_config)
        
        # 创建任务管理器
        task_manager = UnifiedTaskManager(config_manager)
        
        # 创建同步任务
        task_id = task_manager.create_task(
            name="集成测试同步",
            description="集成测试用同步任务",
            source_id="source",
            target_id="target"
        )
        
        # 启动同步任务
        success = task_manager.start_task(task_id)
        if success:
            print("✓ 同步任务启动成功")
            
            # 等待任务完成
            import time
            max_wait = 30  # 最多等待30秒
            waited = 0
            
            while waited < max_wait:
                status = task_manager.get_task_status(task_id)
                if status and status['status'] in ['completed', 'failed']:
                    break
                time.sleep(1)
                waited += 1
            
            # 检查同步结果
            target_adapter = LocalStorageAdapter(LocalStorageConfig(root_path=target_dir))
            result = target_adapter.list_files()
            
            if len(result.files) == len(test_files):
                print("✓ 集成测试成功：所有文件已同步")
            else:
                print(f"✗ 集成测试失败：期望 {len(test_files)} 个文件，实际 {len(result.files)} 个")
        else:
            print("✗ 同步任务启动失败")
    
    except Exception as e:
        print(f"✗ 集成测试出错: {e}")
    
    finally:
        # 清理临时目录
        shutil.rmtree(source_dir, ignore_errors=True)
        shutil.rmtree(target_dir, ignore_errors=True)


if __name__ == '__main__':
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(verbosity=2, exit=False)
    
    # 运行集成测试
    print("\n" + "="*50)
    run_integration_test()
