#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRek S3 同步工具 - 任务管理系统
支持多来源/目标注册、任务管理、定时同步等功能
"""

import os
import sys
import json
import time
import hashlib
import hmac
import base64
import urllib.parse
import threading
import logging
import mimetypes
import uuid
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from typing import Dict, List, Any, Optional, Tuple
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import schedule

try:
    from lightrek_cache_manager import get_cache_manager
    from lightrek_parallel_scanner import ParallelScanner, StreamingFileProcessor
    from lightrek_delta_sync import DeltaSyncManager
    OPTIMIZATION_ENABLED = True
    DELTA_SYNC_ENABLED = True
except ImportError as e:
    print(f"优化模块导入失败，使用原始扫描方式: {e}")
    OPTIMIZATION_ENABLED = False
    DELTA_SYNC_ENABLED = False

# 配置管理类
class ConfigManager:
    def __init__(self, config_file: str = "lightrek_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
        
        # 返回默认配置
        return {
            "sources": {},
            "targets": {},
            "tasks": {},
            "global_settings": {
                "max_workers": 20,
                "retry_times": 5,
                "retry_delay": 3,
                "log_level": "INFO"
            },
            "optimization_settings": {
                "enabled": True,
                "parallel_scanning": {
                    "enabled": True,
                    "max_workers": 5
                },
                "cache_management": {
                    "enabled": True,
                    "cache_validity_minutes": 60,
                    "max_cache_age_days": 7,
                    "cache_database": "lightrek_cache.db"
                },

                "streaming_processing": {
                    "enabled": True,
                    "batch_size": 5000
                },
                "delta_sync": {
                    "enabled": False,
                    "chunk_size": 65536,
                    "database": "lightrek_delta.db"
                }
            }
        }
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def add_source(self, source_id: str, source_config: Dict[str, Any]) -> bool:
        """添加数据源"""
        self.config["sources"][source_id] = source_config
        return self.save_config()
    
    def add_target(self, target_id: str, target_config: Dict[str, Any]) -> bool:
        """添加目标"""
        self.config["targets"][target_id] = target_config
        return self.save_config()
    
    def add_task(self, task_id: str, task_config: Dict[str, Any]) -> bool:
        """添加任务"""
        self.config["tasks"][task_id] = task_config
        return self.save_config()
    
    def get_sources(self) -> Dict[str, Any]:
        """获取所有数据源"""
        return self.config.get("sources", {})
    
    def get_targets(self) -> Dict[str, Any]:
        """获取所有目标"""
        return self.config.get("targets", {})
    
    def get_tasks(self) -> Dict[str, Any]:
        """获取所有任务"""
        return self.config.get("tasks", {})
    
    def delete_source(self, source_id: str) -> bool:
        """删除数据源"""
        if source_id in self.config["sources"]:
            del self.config["sources"][source_id]
            return self.save_config()
        return False
    
    def delete_target(self, target_id: str) -> bool:
        """删除目标"""
        if target_id in self.config["targets"]:
            del self.config["targets"][target_id]
            return self.save_config()
        return False
    
    def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        if task_id in self.config["tasks"]:
            del self.config["tasks"][task_id]
            return self.save_config()
        return False
    
    def get_optimization_settings(self) -> Dict[str, Any]:
        """获取优化设置"""
        return self.config.get("optimization_settings", {})
    
    def update_optimization_settings(self, settings: Dict[str, Any]) -> bool:
        """更新优化设置"""
        if "optimization_settings" not in self.config:
            self.config["optimization_settings"] = {}
        
        self.config["optimization_settings"].update(settings)
        return self.save_config()
    
    def get_global_settings(self) -> Dict[str, Any]:
        """获取全局设置"""
        return self.config.get("global_settings", {})

@dataclass
class S3Config:
    """S3配置类"""
    access_key: str
    secret_key: str
    endpoint: str
    region: str
    bucket: str
    name: str = ""
    description: str = ""
    
    def __post_init__(self):
        if 'aliyuncs.com' in self.endpoint:
            if not self.endpoint.startswith('http'):
                self.endpoint = f'https://{self.endpoint}'
        elif not self.endpoint.startswith('http'):
            self.endpoint = f'https://{self.endpoint}'

@dataclass
class SyncTask:
    """同步任务类"""
    task_id: str
    name: str
    description: str
    source_id: str
    target_id: str
    prefix: str = ""
    max_workers: int = 20
    retry_times: int = 5
    retry_delay: int = 3
    verify_integrity: bool = True
    incremental_sync: bool = True
    sync_mode: str = "incremental"  # incremental, full, mirror
    delete_extra: bool = False  # 是否删除目标中多余的文件
    file_filter: str = ""  # 文件过滤规则，支持通配符
    exclude_filter: str = ""  # 排除文件规则
    bandwidth_limit: int = 0  # 带宽限制 MB/s，0表示无限制
    chunk_threshold: int = 100  # 大文件切片阈值 MB
    chunk_size: int = 10  # 切片大小 MB
    schedule_type: str = "manual"  # manual, minutely, hourly, daily, weekly
    schedule_interval: int = 1
    schedule_time: str = "00:00"
    enabled: bool = True
    created_at: str = ""
    last_run: str = ""
    status: str = "idle"  # idle, running, completed, failed
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()

class S3Client:
    """S3客户端类"""
    
    def __init__(self, config: S3Config):
        self.config = config
    
    def _get_host_and_path(self, key: str = "") -> Tuple[str, str]:
        """获取主机和路径"""
        import urllib.parse
        parsed = urlparse(self.config.endpoint)
        host = parsed.netloc
        
        # 对key进行URL编码，但保留路径分隔符
        encoded_key = urllib.parse.quote(key, safe='/') if key else ""
        
        if 'aliyuncs.com' in host:
            if self.config.bucket:
                host = f"{self.config.bucket}.{host}"
                path = f"/{encoded_key}" if encoded_key else "/"
            else:
                path = f"/{encoded_key}" if encoded_key else "/"
        else:
            if self.config.bucket:
                path = f"/{self.config.bucket}/{encoded_key}" if encoded_key else f"/{self.config.bucket}/"
            else:
                path = f"/{encoded_key}" if encoded_key else "/"
        
        return host, path
    
    def _aws_signature_v4(self, method: str, path: str, query_params: Dict[str, str] = None, 
                         headers: Dict[str, str] = None, payload: bytes = b'') -> Dict[str, str]:
        """AWS签名V4算法"""
        if headers is None:
            headers = {}
        if query_params is None:
            query_params = {}
        
        t = datetime.utcnow()
        amz_date = t.strftime('%Y%m%dT%H%M%SZ')
        date_stamp = t.strftime('%Y%m%d')
        
        headers['Host'] = self._get_host_and_path()[0]
        headers['X-Amz-Date'] = amz_date
        
        if payload:
            headers['X-Amz-Content-Sha256'] = hashlib.sha256(payload).hexdigest()
        else:
            headers['X-Amz-Content-Sha256'] = hashlib.sha256(b'').hexdigest()
        
        canonical_headers = '\n'.join([f"{k.lower()}:{v}" for k, v in sorted(headers.items())]) + '\n'
        signed_headers = ';'.join([k.lower() for k in sorted(headers.keys())])
        
        canonical_querystring = '&'.join([f"{k}={urllib.parse.quote_plus(str(v))}" for k, v in sorted(query_params.items())])
        
        canonical_request = f"{method}\n{path}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{headers['X-Amz-Content-Sha256']}"
        
        algorithm = 'AWS4-HMAC-SHA256'
        credential_scope = f"{date_stamp}/{self.config.region}/s3/aws4_request"
        string_to_sign = f"{algorithm}\n{amz_date}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode()).hexdigest()}"
        
        def sign(key: bytes, msg: str) -> bytes:
            return hmac.new(key, msg.encode(), hashlib.sha256).digest()
        
        signing_key = sign(sign(sign(sign(f"AWS4{self.config.secret_key}".encode(), date_stamp), self.config.region), "s3"), "aws4_request")
        signature = hmac.new(signing_key, string_to_sign.encode(), hashlib.sha256).hexdigest()
        
        authorization_header = f"{algorithm} Credential={self.config.access_key}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}"
        headers['Authorization'] = authorization_header
        
        return headers
    
    def _aliyun_oss_signature(self, method: str, path: str, query_params: Dict[str, str] = None, 
                             headers: Dict[str, str] = None, payload: bytes = b'') -> Dict[str, str]:
        """阿里云OSS签名算法"""
        if headers is None:
            headers = {}
        if query_params is None:
            query_params = {}
        
        import time
        gmt_time = time.strftime('%a, %d %b %Y %H:%M:%S GMT', time.gmtime())
        headers['Date'] = gmt_time
        headers['Host'] = self._get_host_and_path()[0]
        
        if payload:
            headers['Content-MD5'] = base64.b64encode(hashlib.md5(payload).digest()).decode()
        
        content_md5 = headers.get('Content-MD5', '')
        content_type = headers.get('Content-Type', '')
        date = headers.get('Date', '')
        
        oss_headers = {}
        for key, value in headers.items():
            if key.lower().startswith('x-oss-'):
                oss_headers[key.lower()] = value
        
        oss_header_str = ''
        if oss_headers:
            oss_header_str = '\n'.join([f"{k}:{v}" for k, v in sorted(oss_headers.items())]) + '\n'
        
        # 构建规范化资源 - 阿里云OSS虚拟主机格式需要包含bucket
        # 注意：签名用的是原始路径，不是URL编码后的路径
        if 'aliyuncs.com' in self.config.endpoint:
            # 阿里云OSS虚拟主机格式：/bucket/object
            if path == '/':
                resource = f"/{self.config.bucket}/"
            else:
                resource = f"/{self.config.bucket}{path}"
        else:
            resource = path
            
        if query_params:
            oss_params = {}
            for key in ['acl', 'uploads', 'location', 'cors', 'logging', 'website', 'referer', 'lifecycle', 'delete', 'append', 'tagging', 'objectMeta', 'uploadId', 'partNumber', 'security-token', 'position', 'img', 'style', 'styleName', 'replication', 'replicationProgress', 'replicationLocation', 'cname', 'bucketInfo', 'comp', 'qos', 'live', 'status', 'vod', 'startTime', 'endTime', 'symlink', 'x-oss-process', 'response-content-type', 'response-content-language', 'response-expires', 'response-cache-control', 'response-content-disposition', 'response-content-encoding']:
                if key in query_params:
                    oss_params[key] = query_params[key]
            
            if oss_params:
                resource += '?' + '&'.join([f"{k}={v}" if v else k for k, v in sorted(oss_params.items())])
        
        string_to_sign = f"{method}\n{content_md5}\n{content_type}\n{date}\n{oss_header_str}{resource}"
        
        signature = base64.b64encode(hmac.new(self.config.secret_key.encode(), string_to_sign.encode(), hashlib.sha1).digest()).decode()
        
        headers['Authorization'] = f"OSS {self.config.access_key}:{signature}"
        
        return headers
    
    def _make_request(self, method: str, key: str = "", query_params: Dict[str, str] = None, 
                     headers: Dict[str, str] = None, data: bytes = b'') -> Tuple[int, Dict[str, str], bytes]:
        """发送HTTP请求"""
        if headers is None:
            headers = {}
        if query_params is None:
            query_params = {}
        
        host, path = self._get_host_and_path(key)
        
        if 'aliyuncs.com' in self.config.endpoint:
            # 对于阿里云OSS，GET请求且无数据时不应该有Content-Type
            if method == 'GET' and not data:
                headers.pop('Content-Type', None)
            # 对于阿里云OSS，签名需要使用原始key，不是编码后的path
            headers = self._aliyun_oss_signature(method, f"/{key}" if key else "/", query_params, headers, data)
            
            # 对于阿里云OSS，使用http.client来避免urllib自动添加Content-Type
            return self._make_aliyun_request(host, path, method, query_params, headers, data)
        else:
            headers = self._aws_signature_v4(method, path, query_params, headers, data)
            
            # 标准S3使用urllib.request
            import urllib.request
            import urllib.error
            
            url = f"https://{host}{path}"
            if query_params:
                url += '?' + '&'.join([f"{k}={urllib.parse.quote_plus(str(v))}" for k, v in query_params.items()])
            
            req = urllib.request.Request(url, data=data, method=method)
            for k, v in headers.items():
                req.add_header(k, v)
            
            try:
                with urllib.request.urlopen(req, timeout=30) as response:
                    return response.status, dict(response.headers), response.read()
            except urllib.error.HTTPError as e:
                return e.code, dict(e.headers), e.read()
            except Exception as e:
                raise Exception(f"请求失败: {e}")
    
    def _make_aliyun_request(self, host: str, path: str, method: str, 
                           query_params: Dict[str, str], headers: Dict[str, str], 
                           data: bytes) -> Tuple[int, Dict[str, str], bytes]:
        """专门为阿里云OSS发送HTTP请求，避免urllib自动添加Content-Type"""
        import http.client
        import ssl
        import urllib.parse
        
        # 构建完整URL路径
        full_path = path
        if query_params:
            full_path += '?' + '&'.join([f"{k}={urllib.parse.quote_plus(str(v))}" for k, v in query_params.items()])
        
        # 创建HTTPS连接
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        conn = http.client.HTTPSConnection(host, context=context, timeout=30)
        
        try:
            # 发送请求，不会自动添加Content-Type
            conn.request(method, full_path, body=data, headers=headers)
            response = conn.getresponse()
            
            # 读取响应
            response_headers = dict(response.getheaders())
            response_body = response.read()
            
            return response.status, response_headers, response_body
            
        except Exception as e:
            raise Exception(f"阿里云OSS请求失败: {e}")
        finally:
            conn.close()
    
    def list_buckets(self) -> List[str]:
        """列出所有存储桶"""
        try:
            # 对于阿里云OSS，需要特殊处理
            if 'aliyuncs.com' in self.config.endpoint:
                # 阿里云OSS列出存储桶需要使用专门的签名方法
                return self._list_aliyun_buckets()
            else:
                # 标准S3 API
                status, headers, body = self._make_request('GET', '')
                if status == 200:
                    import xml.etree.ElementTree as ET
                    root = ET.fromstring(body)
                    buckets = []
                    for bucket in root.findall('.//Bucket'):
                        name_elem = bucket.find('Name')
                        if name_elem is not None:
                            buckets.append(name_elem.text)
                    return buckets
        except Exception as e:
            print(f"列出存储桶失败: {e}")
        
        return []
    
    def _list_aliyun_buckets(self) -> List[str]:
        """阿里云OSS专用的存储桶列表获取方法"""
        try:
            import urllib.parse
            import http.client
            import ssl
            
            # 解析endpoint获取主机名
            parsed_url = urllib.parse.urlparse(self.config.endpoint)
            host = parsed_url.netloc
            
            # 构建请求头
            import time
            headers = {
                'Host': host,
                'Date': time.strftime('%a, %d %b %Y %H:%M:%S GMT', time.gmtime())
            }
            
            # 使用阿里云OSS专用签名方法
            signed_headers = self._aliyun_oss_list_buckets_signature('GET', '/', {}, headers, b'')
            headers.update(signed_headers)
            
            # 创建HTTPS连接
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            conn = http.client.HTTPSConnection(host, context=context, timeout=30)
            
            try:
                conn.request('GET', '/', headers=headers)
                response = conn.getresponse()
                
                if response.status == 200:
                    content = response.read().decode('utf-8')
                    
                    # 解析XML响应
                    import xml.etree.ElementTree as ET
                    root = ET.fromstring(content)
                    
                    buckets = []
                    # 阿里云OSS的XML结构：ListAllMyBucketsResult/Buckets/Bucket/Name
                    buckets_container = root.find('Buckets')
                    if buckets_container is not None:
                        for bucket in buckets_container.findall('Bucket'):
                            name_elem = bucket.find('Name')
                            if name_elem is not None and name_elem.text:
                                buckets.append(name_elem.text)
                    
                    return buckets
                else:
                    response_content = response.read().decode('utf-8', errors='ignore')
                    raise Exception(f"HTTP {response.status}, {response_content}")
            finally:
                conn.close()
                
        except Exception as e:
            raise Exception(f"获取阿里云OSS存储桶列表失败: {e}")
    
    def _aliyun_oss_list_buckets_signature(self, method: str, path: str, query_params: Dict[str, str] = None, 
                                          headers: Dict[str, str] = None, payload: bytes = b'') -> Dict[str, str]:
        """阿里云OSS获取存储桶列表的专用签名算法"""
        if headers is None:
            headers = {}
        if query_params is None:
            query_params = {}
            
        # 阿里云OSS使用GMT时间
        import time
        gmt_time = time.strftime('%a, %d %b %Y %H:%M:%S GMT', time.gmtime())
        headers['Date'] = gmt_time
        
        # 移除AWS特有的头部
        headers.pop('X-Amz-Date', None)
        headers.pop('X-Amz-Content-Sha256', None)
        
        # 构建签名字符串
        content_md5 = ""
        content_type = headers.get('Content-Type', "")
        
        # 对于list buckets操作，规范化资源就是根路径
        canonicalized_resource = "/"
        
        # 构建规范化OSS头部
        canonicalized_oss_headers = ""
        oss_headers = {}
        for key, value in headers.items():
            if key.lower().startswith('x-oss-'):
                oss_headers[key.lower()] = value.strip()
        
        if oss_headers:
            canonicalized_oss_headers = '\n'.join([f"{k}:{v}" for k, v in sorted(oss_headers.items())]) + '\n'
        
        # 构建签名字符串
        string_to_sign = f"{method}\n{content_md5}\n{content_type}\n{gmt_time}\n{canonicalized_oss_headers}{canonicalized_resource}"
        
        # 计算签名
        import hmac
        import hashlib
        import base64
        signature = base64.b64encode(
            hmac.new(self.config.secret_key.encode('utf-8'), string_to_sign.encode('utf-8'), hashlib.sha1).digest()
        ).decode()
        
        # 添加授权头
        headers['Authorization'] = f"OSS {self.config.access_key}:{signature}"
        
        return headers
    
    def list_objects(self, prefix: str = "", max_keys: int = 1000, 
                    continuation_token: str = None) -> Dict[str, Any]:
        """列出对象"""
        query_params = {
            'list-type': '2',
            'max-keys': str(max_keys)
        }
        
        if prefix:
            query_params['prefix'] = prefix
        if continuation_token:
            query_params['continuation-token'] = continuation_token
        
        status, headers, body = self._make_request('GET', '', query_params)
        
        if status != 200:
            raise Exception(f"列出对象失败: HTTP {status}, {body.decode('utf-8', errors='ignore')}")
        
        import xml.etree.ElementTree as ET
        root = ET.fromstring(body)
        
        result = {
            'Contents': [],
            'IsTruncated': False,
            'NextContinuationToken': None
        }
        
        for content in root.findall('.//Contents'):
            key_elem = content.find('Key')
            size_elem = content.find('Size')
            etag_elem = content.find('ETag')
            modified_elem = content.find('LastModified')
            
            if key_elem is not None:
                result['Contents'].append({
                    'Key': key_elem.text,
                    'Size': int(size_elem.text) if size_elem is not None else 0,
                    'ETag': etag_elem.text.strip('"') if etag_elem is not None else '',
                    'LastModified': modified_elem.text if modified_elem is not None else ''
                })
        
        truncated_elem = root.find('.//IsTruncated')
        if truncated_elem is not None:
            result['IsTruncated'] = truncated_elem.text.lower() == 'true'
        
        token_elem = root.find('.//NextContinuationToken')
        if token_elem is not None:
            result['NextContinuationToken'] = token_elem.text
        
        return result
    
    def get_object(self, key: str) -> bytes:
        """获取对象"""
        status, headers, body = self._make_request('GET', key)
        
        if status != 200:
            raise Exception(f"获取对象失败: HTTP {status}")
        
        return body
    
    def put_object(self, key: str, data: bytes, content_type: str = 'binary/octet-stream', 
                  original_metadata: Dict[str, Any] = None) -> bool:
        """上传对象，支持保持原始文件属性"""
        headers = {'Content-Type': content_type}
        
        # 添加原始文件属性到用户定义的元数据
        if original_metadata:
            if 'LastModified' in original_metadata:
                headers['x-amz-meta-original-lastmodified'] = original_metadata['LastModified']
            if 'Size' in original_metadata:
                headers['x-amz-meta-original-size'] = str(original_metadata['Size'])
            if 'ETag' in original_metadata:
                headers['x-amz-meta-original-etag'] = original_metadata['ETag']
            # 添加同步时间戳
            from datetime import datetime
            headers['x-amz-meta-sync-time'] = datetime.now().isoformat()
        
        status, response_headers, body = self._make_request('PUT', key, headers=headers, data=data)
        
        return status == 200
    
    def delete_object(self, key: str) -> bool:
        """删除对象"""
        status, response_headers, body = self._make_request('DELETE', key)
        return status == 204
    
    def put_object_chunked(self, key: str, data: bytes, chunk_size_mb: int = 10, 
                          content_type: str = 'binary/octet-stream', 
                          original_metadata: Dict[str, Any] = None) -> bool:
        """分片上传大文件，支持保持原始文件属性"""
        try:
            chunk_size = chunk_size_mb * 1024 * 1024  # 转换为字节
            total_size = len(data)
            
            if total_size <= chunk_size:
                # 文件小于切片大小，直接上传
                return self.put_object(key, data, content_type, original_metadata)
            
            # 初始化分片上传
            upload_id = self._initiate_multipart_upload(key, content_type, original_metadata)
            if not upload_id:
                return False
            
            # 分片上传
            parts = []
            part_number = 1
            offset = 0
            
            while offset < total_size:
                end_offset = min(offset + chunk_size, total_size)
                chunk_data = data[offset:end_offset]
                
                etag = self._upload_part(key, upload_id, part_number, chunk_data)
                if not etag:
                    # 上传失败，取消分片上传
                    self._abort_multipart_upload(key, upload_id)
                    return False
                
                parts.append({'PartNumber': part_number, 'ETag': etag})
                part_number += 1
                offset = end_offset
            
            # 完成分片上传
            return self._complete_multipart_upload(key, upload_id, parts)
            
        except Exception as e:
            print(f"分片上传失败: {e}")
            return False
    
    def _initiate_multipart_upload(self, key: str, content_type: str, 
                                  original_metadata: Dict[str, Any] = None) -> str:
        """初始化分片上传，支持保持原始文件属性"""
        try:
            query_params = {'uploads': ''}
            headers = {'Content-Type': content_type}
            
            # 添加原始文件属性到用户定义的元数据
            if original_metadata:
                if 'LastModified' in original_metadata:
                    headers['x-amz-meta-original-lastmodified'] = original_metadata['LastModified']
                if 'Size' in original_metadata:
                    headers['x-amz-meta-original-size'] = str(original_metadata['Size'])
                if 'ETag' in original_metadata:
                    headers['x-amz-meta-original-etag'] = original_metadata['ETag']
                # 添加同步时间戳
                from datetime import datetime
                headers['x-amz-meta-sync-time'] = datetime.now().isoformat()
            
            status, response_headers, body = self._make_request('POST', key, query_params, headers)
            
            if status == 200:
                import xml.etree.ElementTree as ET
                root = ET.fromstring(body)
                upload_id_elem = root.find('.//UploadId')
                return upload_id_elem.text if upload_id_elem is not None else None
            
            return None
        except Exception as e:
            print(f"初始化分片上传失败: {e}")
            return None
    
    def _upload_part(self, key: str, upload_id: str, part_number: int, data: bytes) -> str:
        """上传分片"""
        try:
            query_params = {
                'partNumber': str(part_number),
                'uploadId': upload_id
            }
            
            status, response_headers, body = self._make_request('PUT', key, query_params, data=data)
            
            if status == 200:
                return response_headers.get('ETag', '').strip('"')
            
            return None
        except Exception as e:
            print(f"上传分片失败: {e}")
            return None
    
    def _complete_multipart_upload(self, key: str, upload_id: str, parts: list) -> bool:
        """完成分片上传"""
        try:
            query_params = {'uploadId': upload_id}
            
            # 构建完成上传的XML
            xml_parts = []
            for part in parts:
                xml_parts.append(f'<Part><PartNumber>{part["PartNumber"]}</PartNumber><ETag>"{part["ETag"]}"</ETag></Part>')
            
            xml_data = f'<CompleteMultipartUpload>{"".join(xml_parts)}</CompleteMultipartUpload>'
            data = xml_data.encode('utf-8')
            
            headers = {'Content-Type': 'application/xml'}
            status, response_headers, body = self._make_request('POST', key, query_params, headers, data)
            
            return status == 200
        except Exception as e:
            print(f"完成分片上传失败: {e}")
            return False
    
    def _abort_multipart_upload(self, key: str, upload_id: str) -> bool:
        """取消分片上传"""
        try:
            query_params = {'uploadId': upload_id}
            status, response_headers, body = self._make_request('DELETE', key, query_params)
            return status == 204
        except Exception as e:
            print(f"取消分片上传失败: {e}")
            return False
    
    def head_object(self, key: str) -> Dict[str, Any]:
        """获取对象元数据（包括原始文件属性）"""
        try:
            status, headers, body = self._make_request('HEAD', key)
            
            if status == 200:
                result = {
                    'ContentLength': int(headers.get('Content-Length', 0)),
                    'ETag': headers.get('ETag', '').strip('"'),
                    'LastModified': headers.get('Last-Modified', ''),
                    'ContentType': headers.get('Content-Type', '')
                }
                
                # 提取原始文件属性（如果存在）
                if 'x-amz-meta-original-lastmodified' in headers:
                    result['OriginalLastModified'] = headers['x-amz-meta-original-lastmodified']
                if 'x-amz-meta-original-size' in headers:
                    result['OriginalSize'] = int(headers['x-amz-meta-original-size'])
                if 'x-amz-meta-original-etag' in headers:
                    result['OriginalETag'] = headers['x-amz-meta-original-etag']
                if 'x-amz-meta-sync-time' in headers:
                    result['SyncTime'] = headers['x-amz-meta-sync-time']
                
                return result
            elif status == 404:
                return None
            else:
                raise Exception(f"获取对象元数据失败: HTTP {status}")
        except Exception:
            return None
    
    def bucket_exists(self) -> bool:
        """检查存储桶是否存在"""
        try:
            # 对于阿里云OSS，使用list_objects来检查存储桶是否存在
            if 'aliyuncs.com' in self.config.endpoint:
                try:
                    self.list_objects(max_keys=1)
                    return True
                except Exception as e:
                    error_msg = str(e)
                    if "NoSuchBucket" in error_msg or "404" in error_msg:
                        return False
                    # 其他错误（如权限问题）也认为存储桶存在，只是无法访问
                    return True
            else:
                # 对于其他S3兼容服务，使用HEAD请求
                status, headers, body = self._make_request('HEAD', '')
                return status == 200
        except Exception:
            return False
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试连接"""
        try:
            # 首先测试基本连接
            if not self.config.bucket:
                # 如果没有指定bucket，尝试列出所有bucket
                try:
                    buckets = self.list_buckets()
                    if buckets:
                        return True, f"连接成功，发现 {len(buckets)} 个存储桶: {', '.join(buckets[:5])}"
                    else:
                        return False, "连接成功但无法列出存储桶，请检查权限或手动指定存储桶"
                except Exception as e:
                    return False, f"无法列出存储桶: {str(e)}"
            else:
                # 测试指定的bucket
                try:
                    if self.bucket_exists():
                        # 尝试列出对象以进一步验证权限
                        try:
                            result = self.list_objects(max_keys=1)
                            file_count = len(result.get('Contents', []))
                            return True, f"连接成功，存储桶 '{self.config.bucket}' 可访问，包含 {file_count} 个对象（仅显示前1个）"
                        except Exception as e:
                            return True, f"连接成功，存储桶 '{self.config.bucket}' 存在但可能权限受限: {str(e)}"
                    else:
                        # 尝试列出所有bucket看是否有权限问题
                        try:
                            buckets = self.list_buckets()
                            if buckets:
                                available_buckets = ', '.join(buckets[:5])
                                return False, f"指定的存储桶 '{self.config.bucket}' 不存在。可用存储桶: {available_buckets}"
                            else:
                                return False, f"存储桶 '{self.config.bucket}' 不存在，且无法列出其他存储桶"
                        except Exception:
                            return False, f"存储桶 '{self.config.bucket}' 不存在或无权限访问"
                except Exception as e:
                    return False, f"测试存储桶访问失败: {str(e)}"
        except Exception as e:
            return False, f"连接失败: {str(e)}"

class TaskManager:
    """任务管理器"""
    
    def __init__(self, config_manager: ConfigManager, db_manager=None):
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.running_tasks = {}
        self.scheduler_thread = None
        self.scheduler_running = False
        self._setup_logging()
        self._start_scheduler()
        
        # 初始化优化模块
        self.optimization_config = self.config_manager.get_optimization_settings()
        optimization_enabled = OPTIMIZATION_ENABLED and self.optimization_config.get("enabled", True)
        
        if optimization_enabled:
            # 从配置获取参数
            parallel_config = self.optimization_config.get("parallel_scanning", {})
            cache_config = self.optimization_config.get("cache_management", {})
            stream_config = self.optimization_config.get("streaming_processing", {})
            delta_config = self.optimization_config.get("delta_sync", {})
            
            self.parallel_scanner = ParallelScanner(
                max_workers=parallel_config.get("max_workers", 5),
                config=self.optimization_config
            ) if parallel_config.get("enabled", True) else None
            
            self.cache_manager = get_cache_manager() if cache_config.get("enabled", True) else None
            
            self.stream_processor = StreamingFileProcessor(
                batch_size=stream_config.get("batch_size", 5000)
            ) if stream_config.get("enabled", True) else None
            
            # 初始化差异同步管理器
            if DELTA_SYNC_ENABLED and delta_config.get("enabled", False):
                delta_db_path = delta_config.get("database", "lightrek_delta.db")
                chunk_size = delta_config.get("chunk_size", 65536)
                self.delta_sync_manager = DeltaSyncManager(delta_db_path, chunk_size)
            else:
                self.delta_sync_manager = None
            
            enabled_features = []
            if self.parallel_scanner: enabled_features.append("并行扫描")
            if self.cache_manager: enabled_features.append("元数据缓存")
            if self.stream_processor: enabled_features.append("流式处理")
            if self.delta_sync_manager: enabled_features.append("差异同步")
            
            self.logger.info(f"已启用同步优化功能：{' + '.join(enabled_features)}")
        else:
            self.parallel_scanner = None
            self.cache_manager = None  
            self.stream_processor = None
            self.delta_sync_manager = None
            self.logger.info("使用原始同步方式")
        
        # 加载现有任务的调度  
        self._load_existing_schedules()
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('lightrek_sync.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _start_scheduler(self):
        """启动调度器"""
        if not self.scheduler_running:
            self.scheduler_running = True
            self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
            self.scheduler_thread.start()
    
    def _scheduler_loop(self):
        """调度器循环"""
        self.logger.info("定时调度器开始运行...")
        while self.scheduler_running:
            try:
                # 检查是否有待执行的任务
                pending_jobs = schedule.jobs
                if pending_jobs:
                    self.logger.debug(f"当前有 {len(pending_jobs)} 个调度任务")
                    for job in pending_jobs:
                        self.logger.debug(f"任务: {job}, 下次运行: {job.next_run}")
                
                # 运行待执行的任务
                schedule.run_pending()
                time.sleep(30)  # 每30秒检查一次，支持更精确的分钟级调度
            except Exception as e:
                self.logger.error(f"调度器错误: {e}")
                time.sleep(30)  # 出错后等待30秒再继续
    
    def _load_existing_schedules(self):
        """加载现有任务的调度"""
        try:
            tasks = self.config_manager.get_tasks()
            for task_id, task_config in tasks.items():
                task = SyncTask(**task_config)
                self._schedule_task(task)
                self.logger.info(f"重新加载任务调度: {task.name}")
        except Exception as e:
            self.logger.error(f"加载现有调度失败: {e}")
    
    def create_task(self, task_config: Dict[str, Any]) -> str:
        """创建任务"""
        task_id = str(uuid.uuid4())
        task = SyncTask(
            task_id=task_id,
            **task_config
        )
        
        self.config_manager.add_task(task_id, asdict(task))
        self._schedule_task(task)
        
        self.logger.info(f"创建任务: {task.name} ({task_id})")
        return task_id
    
    def _schedule_task(self, task: SyncTask):
        """设置定时任务"""
        if not task.enabled or task.schedule_type == "manual":
            self.logger.info(f"任务 {task.name} 不需要调度: enabled={task.enabled}, schedule_type={task.schedule_type}")
            return
        
        job_tag = f"task_{task.task_id}"
        schedule.clear(job_tag)
        
        self.logger.info(f"设置定时任务: {task.name}, 类型: {task.schedule_type}, 间隔: {task.schedule_interval}, 时间: {task.schedule_time}")
        
        if task.schedule_type == "minutely":
            job = schedule.every(task.schedule_interval).minutes.do(
                self._run_scheduled_task, task.task_id
            ).tag(job_tag)
            self.logger.info(f"每分钟任务已设置: {job}, 间隔: {task.schedule_interval}分钟")
        elif task.schedule_type == "hourly":
            job = schedule.every(task.schedule_interval).hours.do(
                self._run_scheduled_task, task.task_id
            ).tag(job_tag)
            self.logger.info(f"每小时任务已设置: {job}")
        elif task.schedule_type == "daily":
            job = schedule.every(task.schedule_interval).days.at(task.schedule_time).do(
                self._run_scheduled_task, task.task_id
            ).tag(job_tag)
            self.logger.info(f"每日任务已设置: {job}, 执行时间: {task.schedule_time}")
        elif task.schedule_type == "weekly":
            job = schedule.every(task.schedule_interval).weeks.do(
                self._run_scheduled_task, task.task_id
            ).tag(job_tag)
            self.logger.info(f"每周任务已设置: {job}")
        
        # 显示所有已调度的任务
        jobs = schedule.get_jobs(job_tag)
        self.logger.info(f"任务 {task.name} 的调度作业: {jobs}")
    
    def _run_scheduled_task(self, task_id: str):
        """运行定时任务"""
        self.logger.info(f"定时任务触发: {task_id}")
        if task_id not in self.running_tasks:
            self.logger.info(f"开始执行定时任务: {task_id}")
            self.run_task(task_id)
        else:
            self.logger.warning(f"任务 {task_id} 已在运行中，跳过此次调度")
    
    def run_task(self, task_id: str) -> bool:
        """运行任务"""
        if task_id in self.running_tasks:
            return False
        
        tasks = self.config_manager.get_tasks()
        if task_id not in tasks:
            return False
        
        task_config = tasks[task_id]
        task = SyncTask(**task_config)
        
        sources = self.config_manager.get_sources()
        targets = self.config_manager.get_targets()
        
        if task.source_id not in sources or task.target_id not in targets:
            self.logger.error(f"任务 {task.name} 的源或目标配置不存在")
            return False
        
        source_config = S3Config(**sources[task.source_id])
        target_config = S3Config(**targets[task.target_id])
        
        sync_thread = threading.Thread(
            target=self._sync_task,
            args=(task, source_config, target_config),
            daemon=True
        )
        
        self.running_tasks[task_id] = {
            'thread': sync_thread,
            'task': task,
            'start_time': datetime.now(),
            'status': 'running'
        }
        
        task_config['status'] = 'running'
        task_config['last_run'] = datetime.now().isoformat()
        self.config_manager.add_task(task_id, task_config)
        
        sync_thread.start()
        self.logger.info(f"启动任务: {task.name}")
        return True
    
    def _match_filter(self, filename: str, filter_pattern: str) -> bool:
        """检查文件是否匹配过滤规则"""
        if not filter_pattern:
            return True
        
        import fnmatch
        patterns = [p.strip() for p in filter_pattern.split(',') if p.strip()]
        return any(fnmatch.fnmatch(filename, pattern) for pattern in patterns)
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"
    
    def _sync_task(self, task: SyncTask, source_config: S3Config, target_config: S3Config):
        """执行同步任务"""
        task_id = task.task_id
        execution_id = None
        
        try:
            # 创建执行记录
            if self.db_manager:
                execution_id = self.db_manager.create_task_execution(task_id, task.name)
                self.db_manager.add_task_log(execution_id, task_id, 'INFO', f"任务 {task.name} 开始执行")
            
            source_client = S3Client(source_config)
            target_client = S3Client(target_config)
            
            # 测试连接
            source_ok, source_msg = source_client.test_connection()
            if not source_ok:
                if self.db_manager:
                    self.db_manager.add_task_log(execution_id, task_id, 'ERROR', f"源连接失败: {source_msg}")
                raise Exception(f"源连接失败: {source_msg}")
            
            target_ok, target_msg = target_client.test_connection()
            if not target_ok:
                if self.db_manager:
                    self.db_manager.add_task_log(execution_id, task_id, 'ERROR', f"目标连接失败: {target_msg}")
                raise Exception(f"目标连接失败: {target_msg}")
            
            self.logger.info(f"任务 {task.name} 开始同步 - 模式: {task.sync_mode}")
            if self.db_manager:
                self.db_manager.add_task_log(execution_id, task_id, 'INFO', f"连接测试成功，开始扫描文件 - 同步模式: {task.sync_mode}")
            
            # 使用优化的扫描和比较逻辑
            scan_start_time = time.time()
            
            if OPTIMIZATION_ENABLED and self.parallel_scanner:
                # 方案1+2：并行扫描 + 缓存
                if self.db_manager:
                    self.db_manager.add_task_log(execution_id, task_id, 'INFO', "使用优化扫描：并行+缓存")
                
                source_files, target_files = self.parallel_scanner.scan_files_parallel(
                    source_client, target_client, task
                )
                
                # 应用文件过滤
                if task.file_filter or task.exclude_filter:
                    filtered_files = []
                    for file_obj in source_files:
                        filename = file_obj['Key'].split('/')[-1]
                        
                        if task.file_filter and not self._match_filter(filename, task.file_filter):
                            continue
                        if task.exclude_filter and self._match_filter(filename, task.exclude_filter):
                            continue
                        
                        filtered_files.append(file_obj)
                    
                    source_files = filtered_files
                    if self.db_manager:
                        self.db_manager.add_task_log(execution_id, task_id, 'INFO', f"过滤后剩余 {len(source_files)} 个文件")
                
                # 将target_files列表转换为字典格式以兼容比较算法
                if isinstance(target_files, list):
                    target_files_dict = {obj['Key']: obj for obj in target_files}
                else:
                    target_files_dict = target_files
                
                # 使用优化的比较算法
                files_to_sync, skipped_files = self.parallel_scanner.compare_files_optimized(
                    source_files, target_files_dict, task
                )
                
            else:
                # 原始扫描方式（后备方案）
                if self.db_manager:
                    self.db_manager.add_task_log(execution_id, task_id, 'INFO', "使用原始扫描方式")
                
                # 获取源文件列表
                source_files = []
                continuation_token = None
                
                while True:
                    result = source_client.list_objects(
                        prefix=task.prefix,
                        max_keys=1000,
                        continuation_token=continuation_token
                    )
                    
                    source_files.extend(result['Contents'])
                    
                    if not result['IsTruncated']:
                        break
                    
                    continuation_token = result.get('NextContinuationToken')
                
                if self.db_manager:
                    self.db_manager.add_task_log(execution_id, task_id, 'INFO', f"发现 {len(source_files)} 个源文件")
                
                # 应用文件过滤
                if task.file_filter or task.exclude_filter:
                    filtered_files = []
                    for file_obj in source_files:
                        filename = file_obj['Key'].split('/')[-1]
                        
                        if task.file_filter and not self._match_filter(filename, task.file_filter):
                            continue
                        if task.exclude_filter and self._match_filter(filename, task.exclude_filter):
                            continue
                        
                        filtered_files.append(file_obj)
                    
                    source_files = filtered_files
                    if self.db_manager:
                        self.db_manager.add_task_log(execution_id, task_id, 'INFO', f"过滤后剩余 {len(source_files)} 个文件")
                
                # 获取目标文件列表
                target_files = {}
                if task.sync_mode in ['incremental', 'mirror']:
                    continuation_token = None
                    while True:
                        result = target_client.list_objects(
                            prefix=task.prefix,
                            max_keys=1000,
                            continuation_token=continuation_token
                        )
                        
                        for obj in result.get('Contents', []):
                            target_files[obj['Key']] = obj
                        
                        if not result.get('IsTruncated', False):
                            break
                            
                        continuation_token = result.get('NextContinuationToken')
                    
                    if self.db_manager:
                        self.db_manager.add_task_log(execution_id, task_id, 'INFO', f"目标已有 {len(target_files)} 个文件")
                
                # 原始比较逻辑
                files_to_sync = []
                skipped_files = 0
                
                for file_obj in source_files:
                    key = file_obj['Key']
                    should_sync = False
                    
                    if task.sync_mode == 'incremental':
                        if key in target_files:
                            target_obj = target_files[key]
                            if (file_obj.get('ETag') != target_obj.get('ETag') or 
                                file_obj.get('Size') != target_obj.get('Size')):
                                should_sync = True
                        else:
                            should_sync = True
                    elif task.sync_mode == 'full':
                        should_sync = True
                    elif task.sync_mode == 'mirror':
                        if key in target_files:
                            target_obj = target_files[key]
                            if (file_obj.get('ETag') != target_obj.get('ETag') or 
                                file_obj.get('Size') != target_obj.get('Size')):
                                should_sync = True
                        else:
                            should_sync = True
                    
                    if should_sync:
                        files_to_sync.append(file_obj)
                    else:
                        skipped_files += 1
            
            scan_end_time = time.time()
            scan_duration = scan_end_time - scan_start_time
            
            total_files = len(files_to_sync)
            total_size = sum(f['Size'] for f in files_to_sync)
            
            # 记录扫描性能统计
            target_files_count = len(target_files_dict) if 'target_files_dict' in locals() else len(target_files)
            if self.db_manager:
                self.db_manager.add_task_log(execution_id, task_id, 'INFO', 
                    f"扫描完成 - 耗时: {scan_duration:.2f}秒, 源文件: {len(source_files)}, "
                    f"目标文件: {target_files_count}, 需要同步: {total_files}, 跳过: {skipped_files}")
            
            self.logger.info(f"任务 {task.name} 扫描完成 - 耗时: {scan_duration:.2f}秒, "
                          f"需要同步: {total_files}, 跳过: {skipped_files}, 总大小: {self._format_size(total_size)}")
            
            if self.db_manager:
                self.db_manager.update_task_execution(execution_id, total_files=total_files, total_size=total_size)
            
            # 镜像模式：删除目标中多余的文件
            if task.sync_mode == 'mirror' and task.delete_extra:
                source_keys = {obj['Key'] for obj in source_files}
                # 使用正确的target_files引用
                target_keys = target_files_dict.keys() if 'target_files_dict' in locals() else target_files.keys()
                files_to_delete = [key for key in target_keys if key not in source_keys]
                
                if files_to_delete:
                    if self.db_manager:
                        self.db_manager.add_task_log(execution_id, task_id, 'INFO', f"镜像模式：需要删除 {len(files_to_delete)} 个多余文件")
                    for key in files_to_delete:
                        try:
                            if target_client.delete_object(key):
                                if self.db_manager:
                                    self.db_manager.add_task_log(execution_id, task_id, 'INFO', f"删除文件: {key}")
                            else:
                                if self.db_manager:
                                    self.db_manager.add_task_log(execution_id, task_id, 'ERROR', f"删除文件失败: {key}")
                        except Exception as e:
                            if self.db_manager:
                                self.db_manager.add_task_log(execution_id, task_id, 'ERROR', f"删除文件失败 {key}: {str(e)}")
            
            # 同步文件
            success_count = 0
            failed_count = 0
            transferred_size = 0
            
            # 使用线程池进行并发同步
            from concurrent.futures import ThreadPoolExecutor, as_completed
            
            def sync_single_file(file_obj):
                """同步单个文件（支持差异同步）"""
                try:
                    key = file_obj['Key']
                    file_size = file_obj['Size']
                    source_etag = file_obj['ETag']
                    
                    # 带宽限制
                    if task.bandwidth_limit > 0:
                        # 简单的带宽限制实现
                        time.sleep(file_size / (task.bandwidth_limit * 1024 * 1024))
                    
                    # 重试机制
                    for attempt in range(task.retry_times + 1):
                        try:
                            # 下载文件
                            data = source_client.get_object(key)
                            
                            # 检查是否启用差异同步
                            if self.delta_sync_manager:
                                # 使用差异同步
                                if self.db_manager:
                                    self.db_manager.add_task_log(execution_id, task_id, 'INFO', 
                                                              f"差异同步: {key} ({self._format_size(file_size)})")
                                
                                delta_result = self.delta_sync_manager.apply_delta_sync(
                                    source_client, target_client, key, data
                                )
                                
                                if delta_result['success']:
                                    # 记录差异同步统计信息
                                    optimization_ratio = delta_result.get('optimization_ratio', 0)
                                    bytes_transferred = delta_result.get('bytes_transferred', file_size)
                                    
                                    if self.db_manager:
                                        self.db_manager.add_task_log(execution_id, task_id, 'INFO', 
                                            f"差异同步完成: {key}, 优化比例: {optimization_ratio:.1f}%, "
                                            f"传输: {self._format_size(bytes_transferred)}/{self._format_size(file_size)}")
                                    
                                    return {
                                        'success': True, 
                                        'key': key, 
                                        'size': file_size, 
                                        'etag': source_etag,
                                        'delta_sync': True,
                                        'bytes_transferred': bytes_transferred,
                                        'optimization_ratio': optimization_ratio
                                    }
                                else:
                                    # 差异同步失败，降级到传统同步
                                    if self.db_manager:
                                        self.db_manager.add_task_log(execution_id, task_id, 'WARN', 
                                            f"差异同步失败，降级到传统同步: {key}, 错误: {delta_result.get('message', '未知错误')}")
                            
                            # 传统同步方式
                            content_type = self._guess_content_type(key)
                            threshold_bytes = task.chunk_threshold * 1024 * 1024
                            
                            # 准备原始文件元数据
                            original_metadata = {
                                'LastModified': file_obj.get('LastModified', ''),
                                'Size': file_obj.get('Size', 0),
                                'ETag': file_obj.get('ETag', ''),
                                'ContentType': content_type
                            }
                            
                            if file_size > threshold_bytes:
                                # 大文件使用分片上传
                                if self.db_manager:
                                    self.db_manager.add_task_log(execution_id, task_id, 'INFO', 
                                                              f"大文件分片上传: {key} ({self._format_size(file_size)})")
                                upload_success = target_client.put_object_chunked(key, data, task.chunk_size, content_type, original_metadata)
                            else:
                                # 小文件直接上传
                                upload_success = target_client.put_object(key, data, content_type, original_metadata)
                            
                            if upload_success:
                                # 完整性验证
                                if task.verify_integrity:
                                    # 改进的验证：使用实际下载大小，并添加容错机制
                                    actual_size = len(data)
                                    if actual_size != file_size:
                                        # 记录大小不匹配，但不一定失败
                                        if self.db_manager:
                                            self.db_manager.add_task_log(execution_id, task_id, 'WARN', 
                                                f"文件大小不匹配警告: {key}, 扫描大小: {file_size}, 实际大小: {actual_size}")
                                        
                                        # 如果差异较大（超过1KB），则认为有问题
                                        size_diff = abs(actual_size - file_size)
                                        if size_diff > 1024:  # 1KB容错
                                            raise Exception(f"文件大小差异过大: 期望 {file_size}, 实际 {actual_size}, 差异 {size_diff} 字节")
                                        else:
                                            # 小差异可能是缓存问题，继续处理但更新file_size
                                            if self.db_manager:
                                                self.db_manager.add_task_log(execution_id, task_id, 'INFO', 
                                                    f"文件大小轻微差异已忽略: {key}, 差异 {size_diff} 字节")
                                            file_size = actual_size  # 使用实际大小
                                
                                # 如果差异同步管理器存在，生成文件签名供下次使用
                                if self.delta_sync_manager:
                                    try:
                                        self.delta_sync_manager.generate_file_signature(key, target_client.config.bucket, data)
                                    except Exception as e:
                                        if self.db_manager:
                                            self.db_manager.add_task_log(execution_id, task_id, 'WARN', 
                                                f"生成文件签名失败: {key}, 错误: {str(e)}")
                                
                                return {
                                    'success': True, 
                                    'key': key, 
                                    'size': file_size, 
                                    'etag': source_etag,
                                    'delta_sync': False,
                                    'bytes_transferred': file_size,
                                    'optimization_ratio': 0.0
                                }
                            else:
                                raise Exception("上传失败")
                                
                        except Exception as e:
                            if attempt < task.retry_times:
                                if self.db_manager:
                                    self.db_manager.add_task_log(execution_id, task_id, 'WARN', 
                                                              f"同步文件重试 {key} (第{attempt+1}次): {str(e)}")
                                time.sleep(task.retry_delay)
                            else:
                                raise e
                                
                except Exception as e:
                    return {'success': False, 'key': file_obj['Key'], 'size': file_obj.get('Size', 0), 'error': str(e)}
            
            # 并发同步
            with ThreadPoolExecutor(max_workers=task.max_workers) as executor:
                future_to_obj = {executor.submit(sync_single_file, obj): obj for obj in files_to_sync}
                
                for i, future in enumerate(as_completed(future_to_obj)):
                    result = future.result()
                    
                    if result['success']:
                        success_count += 1
                        transferred_size += result['size']
                        
                        if self.db_manager:
                            self.db_manager.add_file_sync_record(
                                execution_id, task_id, result['key'], result['size'], 
                                result['etag'], result['etag'], 'success'
                            )
                    else:
                        failed_count += 1
                        if self.db_manager:
                            self.db_manager.add_task_log(execution_id, task_id, 'ERROR', f"同步文件失败 {result['key']}: {result['error']}")
                            self.db_manager.add_file_sync_record(
                                execution_id, task_id, result['key'], result['size'], 
                                '', '', 'failed', result['error']
                            )
                    
                    # 更新进度
                    progress = int((i + 1) / len(files_to_sync) * 100)
                    if (i + 1) % 10 == 0 or i == len(files_to_sync) - 1:  # 每10个文件或最后一个文件更新进度
                        if self.db_manager:
                            self.db_manager.add_task_log(execution_id, task_id, 'INFO', 
                                                      f"同步进度: {progress}% ({i+1}/{len(files_to_sync)})")
                            self.db_manager.update_task_execution(
                                execution_id, 
                                success_files=success_count, 
                                failed_files=failed_count,
                                transferred_size=transferred_size
                            )
            
            # 更新任务状态
            tasks = self.config_manager.get_tasks()
            task_config = tasks[task_id]
            task_config['status'] = 'completed'
            task_config['last_run'] = datetime.now().isoformat()
            self.config_manager.add_task(task_id, task_config)
            
            completion_msg = f"任务 {task.name} 完成: 成功 {success_count}, 失败 {failed_count}, 传输 {self._format_size(transferred_size)}"
            self.logger.info(completion_msg)
            
            if self.db_manager:
                self.db_manager.add_task_log(execution_id, task_id, 'INFO', completion_msg)
                self.db_manager.update_task_execution(
                    execution_id,
                    end_time=datetime.now().isoformat(),
                    status='completed',
                    success_files=success_count,
                    failed_files=failed_count,
                    transferred_size=transferred_size
                )
            
        except Exception as e:
            # 更新任务状态为失败
            tasks = self.config_manager.get_tasks()
            task_config = tasks[task_id]
            task_config['status'] = 'failed'
            task_config['last_run'] = datetime.now().isoformat()
            self.config_manager.add_task(task_id, task_config)
            
            error_msg = f"任务 {task.name} 失败: {e}"
            self.logger.error(error_msg)
            
            if self.db_manager:
                self.db_manager.add_task_log(execution_id, task_id, 'ERROR', error_msg)
                self.db_manager.update_task_execution(
                    execution_id,
                    end_time=datetime.now().isoformat(),
                    status='failed',
                    error_message=str(e)
                )
        
        finally:
            # 清理运行状态
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
    
    def _guess_content_type(self, key: str) -> str:
        """猜测文件的Content-Type"""
        content_type, _ = mimetypes.guess_type(key)
        return content_type or 'binary/octet-stream'
    
    def stop_task(self, task_id: str) -> bool:
        """停止任务"""
        if task_id in self.running_tasks:
            self.running_tasks[task_id]['status'] = 'stopping'
            return True
        return False
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        if task_id in self.running_tasks:
            return self.running_tasks[task_id]
        
        tasks = self.config_manager.get_tasks()
        if task_id in tasks:
            return {
                'status': tasks[task_id].get('status', 'idle'),
                'last_run': tasks[task_id].get('last_run', ''),
            }
        
        return {'status': 'not_found'}
    
    def get_all_task_status(self) -> Dict[str, Any]:
        """获取所有任务状态"""
        result = {}
        tasks = self.config_manager.get_tasks()
        
        for task_id, task_config in tasks.items():
            if task_id in self.running_tasks:
                result[task_id] = self.running_tasks[task_id]
            else:
                result[task_id] = {
                    'status': task_config.get('status', 'idle'),
                    'last_run': task_config.get('last_run', ''),
                }
        
        return result
    
    def get_optimization_statistics(self) -> Dict[str, Any]:
        """获取优化功能统计信息"""
        stats = {
            'optimization_enabled': OPTIMIZATION_ENABLED,
            'features': []
        }
        
        if OPTIMIZATION_ENABLED:
            stats['features'] = [
                'parallel_scanning',
         
                'metadata_cache'
            ]
            
            if self.cache_manager:
                # 获取缓存统计信息 - 示例
                stats['cache_info'] = {
                    'cache_enabled': True,
                    'cache_database': 'lightrek_cache.db'
                }
        else:
            stats['features'] = ['original_scanning']
            
        return stats

def main():
    """主函数"""
    print("🚀 LightRek S3 同步工具 - 任务管理系统启动中...")
    
    # 初始化组件
    config_manager = ConfigManager()
    task_manager = TaskManager(config_manager)
    
    # 启动Web界面
    from lightrek_web_interface import WebInterface
    web_interface = WebInterface(config_manager, task_manager)
    web_interface.start()
    
    print(f"🌐 Web管理界面: http://localhost:8001")
    print("📊 功能特性:")
    print("  - 多来源/目标注册管理")
    print("  - 任务创建和调度")
    print("  - 定时同步支持")
    print("  - 实时状态监控")
    print("按 Ctrl+C 退出程序")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在停止程序...")
        web_interface.stop()
        print("程序已退出")

if __name__ == "__main__":
    main() 