#!/usr/bin/env python3
"""
修正版构建脚本 - 使用原来的Web服务器架构
"""

import os
import sys
import platform
import subprocess
import shutil
from pathlib import Path


def run_command(cmd, cwd=None, timeout=300):
    """执行命令并返回结果"""
    print(f"执行命令: {cmd}")
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            encoding='utf-8',
            errors='replace'
        )
        
        if result.returncode != 0:
            print(f"命令执行失败 (返回码: {result.returncode})")
            print(f"错误输出: {result.stderr}")
            return False
        
        print("命令执行成功")
        return True
    
    except subprocess.TimeoutExpired:
        print(f"命令执行超时 ({timeout}秒)")
        return False
    except Exception as e:
        print(f"命令执行异常: {e}")
        return False


def create_spec_file():
    """创建PyInstaller规格文件"""
    print("创建PyInstaller规格文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main_corrected.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('*.json', '.'),
        ('*.md', '.'),
        ('*.txt', '.'),
    ],
    hiddenimports=[
        'sqlite3',
        'threading',
        'concurrent.futures',
        'xml.etree.ElementTree',
        'mimetypes',
        'urllib.request',
        'urllib.parse',
        'urllib.error',
        'hashlib',
        'hmac',
        'base64',
        'time',
        'datetime',
        'json',
        'uuid',
        'logging',
        'dataclasses',
        'tempfile',
        'shutil',
        'posixpath',
        'ntpath',
        'http.server',
        'socketserver',
        'ftplib',
        'ssl',
        'storage_abstraction',
        's3_storage_adapter',
        'sftp_storage_adapter',
        'smb_storage_adapter',
        'ftp_storage_adapter',
        'local_storage_adapter',
        'unified_config_manager',
        'unified_task_manager',
        'unified_web_interface',
        'legacy_adapter',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='lightrek-unified',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('lightrek_unified_corrected.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("规格文件创建成功")
    return True


def build_executable():
    """编译可执行文件"""
    print("开始编译可执行文件...")
    
    # 获取系统信息
    system = platform.system().lower()
    arch = platform.machine().lower()
    
    print(f"系统: {system}")
    print(f"架构: {arch}")
    
    # 编译命令
    cmd = "python -m PyInstaller --clean lightrek_unified_corrected.spec"
    
    if not run_command(cmd, timeout=600):
        print("编译失败")
        return False
    
    print("编译成功")
    return True


def package_release():
    """打包发布文件"""
    print("打包发布文件...")
    
    system = platform.system().lower()
    arch = platform.machine().lower()
    
    # 确定平台名称
    if system == "darwin":
        platform_name = "macos"
    elif system == "linux":
        platform_name = "linux"
    elif system == "windows":
        platform_name = "windows"
    else:
        platform_name = system
    
    # 确定架构名称
    if arch in ["x86_64", "amd64"]:
        arch_name = "x64"
    elif arch in ["arm64", "aarch64"]:
        arch_name = "arm64"
    else:
        arch_name = arch
    
    # 创建发布目录
    release_name = f"lightrek-unified-corrected-{platform_name}-{arch_name}"
    release_dir = Path("releases") / release_name
    
    if release_dir.exists():
        shutil.rmtree(release_dir)
    
    release_dir.mkdir(parents=True, exist_ok=True)
    
    # 复制可执行文件
    exe_name = "lightrek-unified.exe" if system == "windows" else "lightrek-unified"
    exe_path = Path("dist") / exe_name
    
    if exe_path.exists():
        shutil.copy2(exe_path, release_dir / exe_name)
        
        # 在Unix系统上设置执行权限
        if system in ["darwin", "linux"]:
            os.chmod(release_dir / exe_name, 0o755)
    else:
        print(f"可执行文件不存在: {exe_path}")
        return False
    
    # 复制配置文件和文档
    config_files = [
        "README_UNIFIED.md",
        "PROJECT_SUMMARY.md",
        "requirements_unified.txt"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            shutil.copy2(config_file, release_dir / config_file)
            print(f"复制文件: {config_file}")
    
    # 创建默认配置文件
    default_config = {
        "version": "2.0",
        "sources": {},
        "targets": {},
        "tasks": {},
        "global_settings": {
            "default_max_workers": 20,
            "default_retry_times": 5,
            "default_retry_delay": 3,
            "default_chunk_size_mb": 10,
            "default_bandwidth_limit": 0
        }
    }
    
    import json
    with open(release_dir / "lightrek_unified_config.json", 'w', encoding='utf-8') as f:
        json.dump(default_config, f, indent=2, ensure_ascii=False)
    
    # 创建启动脚本
    if system in ["darwin", "linux"]:
        start_script = release_dir / "start.sh"
        with open(start_script, 'w', encoding='utf-8') as f:
            f.write(f'''#!/bin/bash
# LightRek 统一存储同步工具启动脚本

echo "启动 LightRek 统一存储同步工具..."
echo "平台: {platform_name}-{arch_name}"
echo "版本: 2.0.0 (修正版)"
echo ""

# 检查权限
if [ ! -x "./{exe_name}" ]; then
    echo "设置执行权限..."
    chmod +x ./{exe_name}
fi

# 启动程序
./{exe_name}
''')
        os.chmod(start_script, 0o755)
    elif system == "windows":
        start_script = release_dir / "start.bat"
        with open(start_script, 'w', encoding='utf-8') as f:
            f.write(f'''@echo off
chcp 65001 >nul
echo 启动 LightRek 统一存储同步工具...
echo 平台: {platform_name}-{arch_name}
echo 版本: 2.0.0 (修正版)
echo.

REM 启动程序
{exe_name}
pause
''')
    
    # 创建README
    from datetime import datetime
    readme_content = f"""# LightRek 统一存储同步工具 (修正版)

## 版本信息
- 版本: 2.0.0 (修正版)
- 平台: {platform_name}-{arch_name}
- 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 使用说明

### 启动程序
- Windows: 双击 `start.bat` 或直接运行 `{exe_name}`
- Linux/Mac: 运行 `./start.sh` 或 `./{exe_name}`

### 功能特性
- ✅ 多种存储类型支持 (S3, SFTP, SMB, FTP, 本地)
- ✅ 统一配置管理
- ✅ 任务管理和监控
- ✅ Web管理界面 (不依赖Flask)
- ✅ 内置HTTP服务器

### 访问地址
程序启动后访问: http://localhost:8001

### 注意事项
- 这是修正版，使用内置HTTP服务器，不依赖Flask
- 支持所有存储类型的基本功能
- 可选依赖: paramiko (SFTP), smbprotocol (SMB)

### 安装可选依赖
```bash
pip install paramiko smbprotocol
```
"""
    
    from datetime import datetime
    with open(release_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"发布包创建成功: {release_dir}")
    return True


def clean_build():
    """清理构建文件"""
    print("清理构建文件...")
    
    clean_dirs = ["build", "dist", "__pycache__"]
    clean_files = ["lightrek_unified_corrected.spec"]
    
    for clean_dir in clean_dirs:
        if Path(clean_dir).exists():
            shutil.rmtree(clean_dir)
            print(f"删除目录: {clean_dir}")
    
    for clean_file in clean_files:
        if Path(clean_file).exists():
            os.remove(clean_file)
            print(f"删除文件: {clean_file}")
    
    print("清理完成")


def main():
    """主函数"""
    # 设置控制台编码
    if sys.platform == "win32":
        os.system("chcp 65001 >nul")
    
    print("LightRek 统一存储同步工具编译脚本 (修正版)")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 检查必要文件
    required_files = [
        "storage_abstraction.py",
        "unified_config_manager.py", 
        "unified_task_manager.py",
        "unified_web_interface.py",
        "main_corrected.py"
    ]
    
    for file in required_files:
        if not Path(file).exists():
            print(f"缺少必要文件: {file}")
            sys.exit(1)
    
    try:
        # 步骤1: 创建规格文件
        if not create_spec_file():
            sys.exit(1)
        
        # 步骤2: 编译可执行文件
        if not build_executable():
            sys.exit(1)
        
        # 步骤3: 打包发布
        if not package_release():
            sys.exit(1)
        
        print("\n编译完成！")
        print("发布文件位于 releases/ 目录")
        print("运行程序后访问: http://localhost:8001")
        print("注意: 此版本使用内置HTTP服务器，不依赖Flask")
        
    except KeyboardInterrupt:
        print("\n编译被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n编译过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        # 清理构建文件
        clean_build()


if __name__ == "__main__":
    main()
