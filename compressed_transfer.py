"""
压缩传输模块 - 针对SFTP/FTP等网络协议优化的压缩打包传输
"""

import os
import tempfile
import zipfile
import tarfile
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass
from storage_abstraction import StorageAdapter, FileMetadata
from database_manager import db_manager


@dataclass
class CompressionConfig:
    """压缩配置"""
    enabled: bool = False
    format: str = "zip"  # zip, tar, tar.gz, tar.bz2
    max_archive_size: int = 100 * 1024 * 1024  # 100MB
    compression_level: int = 6  # 1-9
    selected_paths: List[str] = None  # 用户选择的文件/文件夹路径
    exclude_patterns: List[str] = None  # 排除模式


class CompressedTransferEngine:
    """压缩传输引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.temp_dir = None
    
    def should_use_compression(self, source_adapter: StorageAdapter, file_count: int) -> bool:
        """判断是否应该使用压缩传输"""
        # 对于SFTP/FTP，如果文件数量超过100个，建议使用压缩传输
        adapter_name = source_adapter.__class__.__name__
        if adapter_name in ['SFTPStorageAdapter', 'FTPStorageAdapter']:
            return file_count > 100
        return False
    
    def create_file_selection_ui_data(self, source_adapter: StorageAdapter, 
                                    root_path: str = "") -> Dict[str, Any]:
        """创建文件选择界面的数据结构"""
        try:
            # 扫描目录结构，创建树形结构
            tree_data = self._scan_directory_tree(source_adapter, root_path)
            
            return {
                'success': True,
                'tree': tree_data,
                'total_files': self._count_files_in_tree(tree_data),
                'estimated_size': self._estimate_size_in_tree(tree_data)
            }
        except Exception as e:
            self.logger.error(f"创建文件选择数据失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'tree': {},
                'total_files': 0,
                'estimated_size': 0
            }
    
    def _scan_directory_tree(self, adapter: StorageAdapter, path: str, 
                           max_depth: int = 3, current_depth: int = 0) -> Dict[str, Any]:
        """扫描目录树结构（限制深度避免过深）"""
        if current_depth >= max_depth:
            return {'type': 'folder', 'name': os.path.basename(path), 'children': {}, 'truncated': True}
        
        try:
            result = adapter.list_files(prefix=path, max_keys=1000)
            tree = {'type': 'folder', 'name': os.path.basename(path) or 'root', 'children': {}}
            
            # 按目录和文件分组
            directories = set()
            files = []
            
            for file_meta in result.files:
                rel_path = file_meta.key
                if path and rel_path.startswith(path):
                    rel_path = rel_path[len(path):].lstrip('/')
                
                if '/' in rel_path:
                    # 这是子目录中的文件
                    dir_name = rel_path.split('/')[0]
                    directories.add(dir_name)
                else:
                    # 这是当前目录的文件
                    files.append({
                        'type': 'file',
                        'name': rel_path,
                        'size': file_meta.size,
                        'path': file_meta.key
                    })
            
            # 添加文件到树
            for file_info in files[:50]:  # 限制显示文件数量
                tree['children'][file_info['name']] = file_info
            
            # 递归添加目录
            for dir_name in list(directories)[:20]:  # 限制显示目录数量
                dir_path = os.path.join(path, dir_name).replace('\\', '/')
                tree['children'][dir_name] = self._scan_directory_tree(
                    adapter, dir_path, max_depth, current_depth + 1
                )
            
            return tree
            
        except Exception as e:
            self.logger.error(f"扫描目录树失败 {path}: {e}")
            return {'type': 'folder', 'name': os.path.basename(path), 'children': {}, 'error': str(e)}
    
    def _count_files_in_tree(self, tree: Dict[str, Any]) -> int:
        """统计树中的文件数量"""
        count = 0
        if tree.get('type') == 'file':
            return 1
        
        for child in tree.get('children', {}).values():
            count += self._count_files_in_tree(child)
        
        return count
    
    def _estimate_size_in_tree(self, tree: Dict[str, Any]) -> int:
        """估算树中的总大小"""
        size = 0
        if tree.get('type') == 'file':
            return int(tree.get('size', 0))
        
        for child in tree.get('children', {}).values():
            size += self._estimate_size_in_tree(child)
        
        return size
    
    def compress_and_transfer(self, source_adapter: StorageAdapter, 
                            target_adapter: StorageAdapter,
                            config: CompressionConfig,
                            execution_id: str,
                            progress_callback: Optional[callable] = None) -> bool:
        """压缩并传输选定的文件/文件夹"""
        try:
            self.temp_dir = tempfile.mkdtemp(prefix="lightrek_compress_")
            self.logger.info(f"开始压缩传输，临时目录: {self.temp_dir}")
            
            # 1. 下载选定的文件到临时目录
            downloaded_files = self._download_selected_files(
                source_adapter, config.selected_paths, execution_id, progress_callback
            )
            
            if not downloaded_files:
                self.logger.warning("没有文件被下载")
                return False
            
            # 2. 创建压缩包
            archive_files = self._create_archives(
                downloaded_files, config, execution_id, progress_callback
            )
            
            if not archive_files:
                self.logger.error("压缩包创建失败")
                return False
            
            # 3. 上传压缩包到目标
            success = self._upload_archives(
                target_adapter, archive_files, execution_id, progress_callback
            )
            
            return success
            
        except Exception as e:
            self.logger.error(f"压缩传输失败: {e}")
            db_manager.add_task_log(execution_id, 'ERROR', f'压缩传输失败: {e}')
            return False
        finally:
            self._cleanup_temp_dir()
    
    def _download_selected_files(self, source_adapter: StorageAdapter, 
                               selected_paths: List[str], execution_id: str,
                               progress_callback: Optional[callable] = None) -> List[str]:
        """下载选定的文件到临时目录"""
        downloaded_files = []
        
        for path in selected_paths:
            try:
                # 检查是文件还是目录
                if self._is_directory_path(source_adapter, path):
                    # 下载整个目录
                    dir_files = self._download_directory(source_adapter, path, execution_id)
                    downloaded_files.extend(dir_files)
                else:
                    # 下载单个文件
                    file_path = self._download_single_file(source_adapter, path, execution_id)
                    if file_path:
                        downloaded_files.append(file_path)
                
                if progress_callback:
                    progress_callback(f"已下载: {path}")
                    
            except Exception as e:
                self.logger.error(f"下载文件失败 {path}: {e}")
                db_manager.add_task_log(execution_id, 'ERROR', f'下载文件失败 {path}: {e}')
        
        return downloaded_files
    
    def _is_directory_path(self, adapter: StorageAdapter, path: str) -> bool:
        """判断路径是否为目录"""
        try:
            # 尝试列出该路径下的文件
            result = adapter.list_files(prefix=path, max_keys=1)
            return len(result.files) > 0 and any(
                f.key.startswith(path + '/') for f in result.files
            )
        except:
            return False
    
    def _download_directory(self, adapter: StorageAdapter, dir_path: str, 
                          execution_id: str) -> List[str]:
        """下载整个目录"""
        downloaded_files = []
        
        try:
            result = adapter.list_files(prefix=dir_path)
            
            for file_meta in result.files:
                file_path = self._download_single_file(adapter, file_meta.key, execution_id)
                if file_path:
                    downloaded_files.append(file_path)
                    
        except Exception as e:
            self.logger.error(f"下载目录失败 {dir_path}: {e}")
        
        return downloaded_files
    
    def _download_single_file(self, adapter: StorageAdapter, file_key: str, 
                            execution_id: str) -> Optional[str]:
        """下载单个文件到临时目录"""
        try:
            # 创建本地文件路径
            local_path = os.path.join(self.temp_dir, file_key.replace('/', os.sep))
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            # 下载文件
            file_data = adapter.get_file(file_key)
            if file_data:
                with open(local_path, 'wb') as f:
                    f.write(file_data)
                return local_path
            
        except Exception as e:
            self.logger.error(f"下载单个文件失败 {file_key}: {e}")
        
        return None
    
    def _create_archives(self, file_paths: List[str], config: CompressionConfig,
                        execution_id: str, progress_callback: Optional[callable] = None) -> List[str]:
        """创建压缩包"""
        archive_files = []
        current_archive_size = 0
        current_files = []
        archive_count = 1
        
        for file_path in file_paths:
            file_size = os.path.getsize(file_path)
            
            # 检查是否需要创建新的压缩包
            if (current_archive_size + file_size > config.max_archive_size and 
                current_files):
                
                # 创建当前压缩包
                archive_path = self._create_single_archive(
                    current_files, config, archive_count, execution_id
                )
                if archive_path:
                    archive_files.append(archive_path)
                
                # 重置
                current_files = []
                current_archive_size = 0
                archive_count += 1
            
            current_files.append(file_path)
            current_archive_size += file_size
        
        # 创建最后一个压缩包
        if current_files:
            archive_path = self._create_single_archive(
                current_files, config, archive_count, execution_id
            )
            if archive_path:
                archive_files.append(archive_path)
        
        return archive_files
    
    def _create_single_archive(self, file_paths: List[str], config: CompressionConfig,
                             archive_num: int, execution_id: str) -> Optional[str]:
        """创建单个压缩包"""
        try:
            timestamp = int(time.time())
            archive_name = f"lightrek_archive_{timestamp}_{archive_num}.{config.format}"
            archive_path = os.path.join(self.temp_dir, archive_name)
            
            if config.format == "zip":
                self._create_zip_archive(file_paths, archive_path, config.compression_level)
            elif config.format.startswith("tar"):
                self._create_tar_archive(file_paths, archive_path, config.format, config.compression_level)
            
            self.logger.info(f"创建压缩包: {archive_name}, 大小: {os.path.getsize(archive_path)} 字节")
            db_manager.add_task_log(execution_id, 'INFO', f'创建压缩包: {archive_name}')
            
            return archive_path
            
        except Exception as e:
            self.logger.error(f"创建压缩包失败: {e}")
            return None
    
    def _create_zip_archive(self, file_paths: List[str], archive_path: str, compression_level: int):
        """创建ZIP压缩包"""
        with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=compression_level) as zf:
            for file_path in file_paths:
                # 计算相对路径
                rel_path = os.path.relpath(file_path, self.temp_dir)
                zf.write(file_path, rel_path)
    
    def _create_tar_archive(self, file_paths: List[str], archive_path: str, 
                          format_type: str, compression_level: int):
        """创建TAR压缩包"""
        mode = "w"
        if format_type == "tar.gz":
            mode = "w:gz"
        elif format_type == "tar.bz2":
            mode = "w:bz2"
        
        with tarfile.open(archive_path, mode) as tf:
            for file_path in file_paths:
                rel_path = os.path.relpath(file_path, self.temp_dir)
                tf.add(file_path, rel_path)
    
    def _upload_archives(self, target_adapter: StorageAdapter, archive_files: List[str],
                        execution_id: str, progress_callback: Optional[callable] = None) -> bool:
        """上传压缩包到目标"""
        success_count = 0
        
        for archive_path in archive_files:
            try:
                archive_name = os.path.basename(archive_path)
                
                with open(archive_path, 'rb') as f:
                    archive_data = f.read()
                
                success = target_adapter.put_file(
                    f"compressed_transfer/{archive_name}",
                    archive_data,
                    'application/octet-stream'
                )
                
                if success:
                    success_count += 1
                    self.logger.info(f"上传压缩包成功: {archive_name}")
                    db_manager.add_task_log(execution_id, 'INFO', f'上传压缩包成功: {archive_name}')
                    
                    if progress_callback:
                        progress_callback(f"已上传: {archive_name}")
                else:
                    self.logger.error(f"上传压缩包失败: {archive_name}")
                    
            except Exception as e:
                self.logger.error(f"上传压缩包异常 {archive_path}: {e}")
        
        return success_count == len(archive_files)
    
    def _cleanup_temp_dir(self):
        """清理临时目录"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                import shutil
                shutil.rmtree(self.temp_dir)
                self.logger.info(f"清理临时目录: {self.temp_dir}")
            except Exception as e:
                self.logger.error(f"清理临时目录失败: {e}")


# 全局压缩传输引擎实例
compressed_transfer_engine = CompressedTransferEngine()
