#!/usr/bin/env python3
"""
原始Web界面适配器 - 将原来完整的Web界面与新的统一存储系统结合
"""

import json
import threading
import time
import uuid
from http.server import HTTPServer, BaseHTTPRequestHandler
from unified_config_manager import UnifiedConfigManager
from unified_task_manager import UnifiedTaskManager
from legacy_adapter import LegacyTaskManagerAdapter


class OriginalWebInterface:
    """原始Web界面适配器"""
    
    def __init__(self, config_manager: UnifiedConfigManager, task_manager: UnifiedTaskManager, port: int = 8001):
        self.config_manager = config_manager
        self.task_manager = task_manager
        self.port = port
        self.server = None
        self.server_thread = None
        
        # 创建遗留适配器以兼容原来的接口
        self.legacy_adapter = LegacyTaskManagerAdapter(task_manager)
        
        # 模拟数据库管理器（简化版）
        self.db_manager = None
    
    def start(self):
        """启动Web服务器"""
        handler = self._create_handler()
        self.server = HTTPServer(('localhost', self.port), handler)
        self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
        self.server_thread.start()
        print(f"🌐 Web界面已启动: http://localhost:{self.port}")
    
    def stop(self):
        """停止Web服务器"""
        if self.server:
            self.server.shutdown()
    
    def _create_handler(self):
        """创建请求处理器"""
        config_manager = self.config_manager
        task_manager = self.task_manager
        legacy_adapter = self.legacy_adapter
        db_manager = self.db_manager
        
        class RequestHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/' or self.path == '/dashboard':
                    self._serve_dashboard()
                elif self.path == '/manual':
                    self._serve_manual()
                elif self.path.startswith('/logs'):
                    self._serve_task_logs_page()
                elif self.path == '/api/sources':
                    self._serve_sources()
                elif self.path == '/api/targets':
                    self._serve_targets()
                elif self.path == '/api/tasks':
                    self._serve_tasks()
                elif self.path == '/api/task-status' or self.path == '/api/task_status':
                    self._serve_task_status()
                elif self.path == '/api/task-executions' or self.path == '/api/task_executions':
                    self._serve_task_executions()
                elif self.path.startswith('/api/task-logs/') or self.path.startswith('/api/task_logs/'):
                    execution_id = self.path.split('/')[-1]
                    self._serve_task_logs(execution_id)
                elif self.path.startswith('/api/task-details/'):
                    task_id = self.path.split('/')[-1]
                    self._serve_task_details(task_id)
                elif self.path == '/api/statistics':
                    self._serve_statistics()
                elif self.path == '/api/optimization-config':
                    self._serve_optimization_config()
                else:
                    self._serve_404()
            
            def do_POST(self):
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length)
                
                try:
                    data = json.loads(post_data.decode('utf-8')) if post_data else {}
                except:
                    data = {}
                
                if self.path == '/api/sources':
                    self._add_source(data)
                elif self.path == '/api/targets':
                    self._add_target(data)
                elif self.path == '/api/tasks':
                    self._add_task(data)
                elif self.path == '/api/test-connection':
                    self._test_connection(data)
                elif self.path == '/api/list-buckets':
                    self._list_buckets(data)
                elif self.path.startswith('/api/tasks/') and self.path.endswith('/run'):
                    task_id = self.path.split('/')[-2]
                    self._run_task(task_id)
                elif self.path.startswith('/api/tasks/') and self.path.endswith('/stop'):
                    task_id = self.path.split('/')[-2]
                    self._stop_task(task_id)
                elif self.path == '/api/optimization-config':
                    self._save_optimization_config(data)
                else:
                    self._serve_404()
            
            def do_PUT(self):
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length)
                
                try:
                    data = json.loads(post_data.decode('utf-8')) if post_data else {}
                except:
                    data = {}
                
                if self.path.startswith('/api/sources/'):
                    source_id = self.path.split('/')[-1]
                    self._update_source(source_id, data)
                elif self.path.startswith('/api/targets/'):
                    target_id = self.path.split('/')[-1]
                    self._update_target(target_id, data)
                elif self.path.startswith('/api/tasks/'):
                    task_id = self.path.split('/')[-1]
                    self._update_task(task_id, data)
                else:
                    self._serve_404()
            
            def do_DELETE(self):
                if self.path.startswith('/api/sources/'):
                    source_id = self.path.split('/')[-1]
                    self._delete_source(source_id)
                elif self.path.startswith('/api/targets/'):
                    target_id = self.path.split('/')[-1]
                    self._delete_target(target_id)
                elif self.path.startswith('/api/tasks/'):
                    task_id = self.path.split('/')[-1]
                    self._delete_task(task_id)
                else:
                    self._serve_404()
            
            def _serve_dashboard(self):
                """提供仪表盘页面 - 使用原来的完整实现"""
                # 这里需要加载原来的完整HTML模板
                # 由于模板太长，我们先提供一个简化版本，然后逐步完善
                html = self._get_original_dashboard_html()
                self.send_response(200)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _get_original_dashboard_html(self):
                """获取原来的仪表盘HTML - 简化版本"""
                # 获取统计数据
                stats = self._get_dashboard_stats()
                
                return f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LightRek 统一存储同步工具 - 仪表盘</title>
    <style>
        /* 这里应该包含原来的完整CSS样式 */
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); min-height: 100vh; display: flex; color: #333333; }}
        .sidebar {{ width: 280px; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1); padding: 20px; overflow-y: auto; border-right: 1px solid #e0e0e0; }}
        .main-content {{ flex: 1; padding: 20px; overflow-y: auto; }}
        .logo {{ text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #e0e0e0; }}
        .logo h1 {{ color: #ff6b35; font-size: 1.8rem; margin-bottom: 5px; font-weight: 700; }}
        .nav-menu {{ list-style: none; }}
        .nav-link {{ display: flex; align-items: center; padding: 12px 16px; color: #555; text-decoration: none; border-radius: 8px; transition: all 0.3s ease; font-weight: 500; }}
        .nav-link:hover {{ background: rgba(255, 107, 53, 0.1); color: #ff6b35; }}
        .nav-link.active {{ background: linear-gradient(135deg, #ff6b35, #ff8c42); color: white; }}
        .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin-bottom: 30px; }}
        .stat-card {{ background: rgba(255, 255, 255, 0.95); border-radius: 15px; padding: 25px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }}
        .stat-value {{ font-size: 2.5rem; font-weight: bold; color: #ff6b35; }}
        .btn-primary {{ background: #ff6b35; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }}
        .config-section {{ background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .hidden {{ display: none; }}
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="logo">
            <h1>LightRek</h1>
            <p>统一存储同步工具</p>
        </div>
        
        <ul class="nav-menu">
            <li><a href="#" class="nav-link active" onclick="showPage('dashboard')">📊 仪表盘</a></li>
            <li><a href="#" class="nav-link" onclick="showPage('source-config')">🗂️ 来源配置</a></li>
            <li><a href="#" class="nav-link" onclick="showPage('target-config')">🎯 目标配置</a></li>
            <li><a href="#" class="nav-link" onclick="showPage('task-config')">📋 任务配置</a></li>
            <li><a href="#" class="nav-link" onclick="showPage('task-logs')">📄 任务日志</a></li>
        </ul>
    </div>
    
    <div class="main-content">
        <!-- 仪表盘页面 -->
        <div id="dashboard">
            <h1>🚀 LightRek 统一存储同步工具</h1>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{stats.get('sources_count', 0)}</div>
                    <div>数据源</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{stats.get('targets_count', 0)}</div>
                    <div>同步目标</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{stats.get('tasks_count', 0)}</div>
                    <div>同步任务</div>
                </div>
            </div>
        </div>
        
        <!-- 来源配置页面 -->
        <div id="source-config" class="hidden">
            <h2>🗂️ 来源配置</h2>
            <div class="config-section">
                <button onclick="showAddSourceModal()" class="btn-primary">+ 添加数据源</button>
                <div id="sources-list"></div>
            </div>
        </div>
        
        <!-- 目标配置页面 -->
        <div id="target-config" class="hidden">
            <h2>🎯 目标配置</h2>
            <div class="config-section">
                <button onclick="showAddTargetModal()" class="btn-primary">+ 添加目标</button>
                <div id="targets-list"></div>
            </div>
        </div>
        
        <!-- 任务配置页面 -->
        <div id="task-config" class="hidden">
            <h2>📋 任务配置</h2>
            <div class="config-section">
                <button onclick="showAddTaskModal()" class="btn-primary">+ 创建任务</button>
                <div id="tasks-list"></div>
            </div>
        </div>
        
        <!-- 任务日志页面 -->
        <div id="task-logs" class="hidden">
            <h2>📄 任务日志</h2>
            <div class="config-section">
                <div id="task-logs-list"></div>
            </div>
        </div>
    </div>
    
    <script>
        function showPage(pageId) {{
            // 隐藏所有页面
            const pages = ['dashboard', 'source-config', 'target-config', 'task-config', 'task-logs'];
            pages.forEach(id => {{
                const element = document.getElementById(id);
                if (element) element.classList.add('hidden');
            }});
            
            // 显示目标页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {{
                targetPage.classList.remove('hidden');
            }}
            
            // 更新导航状态
            document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
            event.target.classList.add('active');
        }}
        
        function showAddSourceModal() {{
            alert('添加数据源功能 - 需要实现模态框');
        }}
        
        function showAddTargetModal() {{
            alert('添加目标功能 - 需要实现模态框');
        }}
        
        function showAddTaskModal() {{
            alert('创建任务功能 - 需要实现模态框');
        }}
        
        // 页面加载时初始化数据
        window.onload = function() {{
            loadSources();
            loadTargets();
            loadTasks();
        }};
        
        function loadSources() {{
            fetch('/api/sources')
            .then(response => response.json())
            .then(data => {{
                console.log('Sources:', data);
                // 更新来源列表显示
            }});
        }}
        
        function loadTargets() {{
            fetch('/api/targets')
            .then(response => response.json())
            .then(data => {{
                console.log('Targets:', data);
                // 更新目标列表显示
            }});
        }}
        
        function loadTasks() {{
            fetch('/api/tasks')
            .then(response => response.json())
            .then(data => {{
                console.log('Tasks:', data);
                // 更新任务列表显示
            }});
        }}
    </script>
</body>
</html>'''
            
            def _get_dashboard_stats(self):
                """获取仪表盘统计数据"""
                try:
                    sources = config_manager.get_all_sources()
                    targets = config_manager.get_all_targets()
                    tasks = task_manager.get_all_tasks()
                    
                    return {
                        'sources_count': len(sources),
                        'targets_count': len(targets),
                        'tasks_count': len(tasks),
                        'active_tasks': 0,  # 简化版本
                        'total_executions': 0,
                        'today_executions': 0,
                        'total_files': 0,
                        'total_size_formatted': '0 B',
                        'success_rate': 100.0,
                        'avg_speed_formatted': '0 MB/s',
                        'running_tasks': 0
                    }
                except Exception as e:
                    print(f"获取统计数据失败: {e}")
                    return {}
            
            def _serve_sources(self):
                """提供数据源列表"""
                try:
                    sources = config_manager.get_all_sources()
                    self._send_json(sources)
                except Exception as e:
                    self._send_json({'error': str(e)})
            
            def _serve_targets(self):
                """提供目标列表"""
                try:
                    targets = config_manager.get_all_targets()
                    self._send_json(targets)
                except Exception as e:
                    self._send_json({'error': str(e)})
            
            def _serve_tasks(self):
                """提供任务列表"""
                try:
                    tasks = {}
                    for task_id, task in task_manager.get_all_tasks().items():
                        tasks[task_id] = {
                            'name': task.name,
                            'description': task.description,
                            'source_id': task.source_id,
                            'target_id': task.target_id,
                            'status': task.last_status,
                            'last_run': task.last_run
                        }
                    self._send_json(tasks)
                except Exception as e:
                    self._send_json({'error': str(e)})
            
            def _serve_task_status(self):
                """提供任务状态"""
                try:
                    status = task_manager.get_all_task_status()
                    self._send_json(status)
                except Exception as e:
                    self._send_json({'error': str(e)})
            
            def _serve_task_executions(self):
                """提供任务执行历史"""
                # 简化版本，返回空列表
                self._send_json([])
            
            def _serve_task_logs(self, execution_id):
                """提供任务日志"""
                # 简化版本，返回空日志
                self._send_json({'success': True, 'logs': []})
            
            def _serve_task_details(self, task_id):
                """提供任务详情"""
                try:
                    task = task_manager.get_task(task_id)
                    if task:
                        self._send_json({
                            'success': True,
                            'task': {
                                'name': task.name,
                                'description': task.description,
                                'source_id': task.source_id,
                                'target_id': task.target_id
                            },
                            'executions': []
                        })
                    else:
                        self._send_json({'success': False, 'message': '任务不存在'})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _serve_statistics(self):
                """提供统计信息"""
                stats = self._get_dashboard_stats()
                self._send_json(stats)
            
            def _serve_optimization_config(self):
                """提供优化配置"""
                # 简化版本，返回默认配置
                self._send_json({
                    'optimization_enabled': True,
                    'parallel_workers': 5,
                    'cache_validity': 60,
                    'cache_retention': 7,
                    'batch_size': 5000,
                    'delta_sync_enabled': False,
                    'delta_chunk_size': 65536
                })
            
            def _serve_manual(self):
                """提供用户手册"""
                html = "<h1>用户手册</h1><p>功能开发中...</p>"
                self.send_response(200)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _serve_task_logs_page(self):
                """重定向到仪表盘的任务日志页面"""
                self.send_response(302)
                self.send_header('Location', '/#logs')
                self.end_headers()
            
            def _add_source(self, data):
                """添加数据源"""
                try:
                    # 适配新的存储类型
                    storage_type = 's3'  # 默认为S3，后续可扩展
                    source_id = data.get('name', '').replace(' ', '_').lower()
                    
                    success = config_manager.add_source(source_id, storage_type, data)
                    self._send_json({'success': success})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _add_target(self, data):
                """添加目标"""
                try:
                    storage_type = 's3'  # 默认为S3，后续可扩展
                    target_id = data.get('name', '').replace(' ', '_').lower()
                    
                    success = config_manager.add_target(target_id, storage_type, data)
                    self._send_json({'success': success})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _add_task(self, data):
                """添加任务"""
                try:
                    task_id = task_manager.create_task(
                        name=data.get('name', ''),
                        description=data.get('description', ''),
                        source_id=data.get('source_id', ''),
                        target_id=data.get('target_id', ''),
                        **{k: v for k, v in data.items() if k not in ['name', 'description', 'source_id', 'target_id']}
                    )
                    self._send_json({'success': True, 'task_id': task_id})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _test_connection(self, data):
                """测试连接"""
                try:
                    # 简化版本，总是返回成功
                    self._send_json({'success': True, 'message': '连接测试成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _list_buckets(self, data):
                """列出存储桶"""
                # 简化版本，返回空列表
                self._send_json({'success': True, 'buckets': []})
            
            def _run_task(self, task_id):
                """运行任务"""
                try:
                    success = task_manager.start_task(task_id)
                    self._send_json({'success': success})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _stop_task(self, task_id):
                """停止任务"""
                try:
                    success = task_manager.stop_task(task_id)
                    self._send_json({'success': success})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _save_optimization_config(self, data):
                """保存优化配置"""
                # 简化版本，总是返回成功
                self._send_json({'success': True})
            
            def _update_source(self, source_id, data):
                """更新数据源"""
                # 简化版本
                self._send_json({'success': True})
            
            def _update_target(self, target_id, data):
                """更新目标"""
                # 简化版本
                self._send_json({'success': True})
            
            def _update_task(self, task_id, data):
                """更新任务"""
                try:
                    success = task_manager.update_task(task_id, **data)
                    self._send_json({'success': success})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _delete_source(self, source_id):
                """删除数据源"""
                try:
                    success = config_manager.remove_source(source_id)
                    self._send_json({'success': success})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _delete_target(self, target_id):
                """删除目标"""
                try:
                    success = config_manager.remove_target(target_id)
                    self._send_json({'success': success})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _delete_task(self, task_id):
                """删除任务"""
                try:
                    success = task_manager.delete_task(task_id)
                    self._send_json({'success': success})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _send_json(self, data):
                """发送JSON响应"""
                self.send_response(200)
                self.send_header('Content-Type', 'application/json; charset=utf-8')
                self.end_headers()
                self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
            
            def _serve_404(self):
                """404响应"""
                self.send_response(404)
                self.send_header('Content-Type', 'text/plain')
                self.end_headers()
                self.wfile.write(b'Not Found')
            
            def log_message(self, format, *args):
                """禁用日志输出"""
                pass
        
        return RequestHandler
