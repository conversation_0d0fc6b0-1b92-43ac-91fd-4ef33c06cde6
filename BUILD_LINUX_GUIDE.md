# LightRek S3同步工具 - Linux编译指南

## 概述

本指南介绍如何在Linux系统上编译LightRek S3同步工具，支持ARM64和x86_64两种架构。

## 系统要求

### 基本要求
- Linux内核 3.10+
- Python 3.7+
- pip3
- gcc/g++编译器
- make工具

### 可选要求（用于交叉编译）
- Docker 20.10+
- buildx插件

## 方法一：本地编译（推荐）

### 1. 安装系统依赖

#### Ubuntu/Debian系统
```bash
sudo apt-get update
sudo apt-get install -y python3 python3-pip python3-dev gcc g++ binutils upx-ucl make
```

#### CentOS/RHEL系统
```bash
sudo yum install -y python3 python3-pip python3-devel gcc gcc-c++ binutils upx make
# 或者在较新版本中使用dnf
sudo dnf install -y python3 python3-pip python3-devel gcc gcc-c++ binutils upx make
```

#### Alpine Linux
```bash
sudo apk add python3 python3-dev py3-pip gcc g++ binutils upx make musl-dev
```

### 2. 安装Python依赖
```bash
pip3 install -r requirements.txt
pip3 install pyinstaller
```

### 3. 使用编译脚本
```bash
# 给脚本执行权限
chmod +x build_linux_simple.py

# 运行编译
python3 build_linux_simple.py
```

### 4. 手动编译（如果脚本失败）
```bash
# 创建PyInstaller规格文件
cat > lightrek_linux.spec << 'EOF'
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['start_lightrek.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('lightrek_config.json', '.'),
        ('lightrek logo 64px.png', '.'),
        ('lightrek logo 32px.png', '.'),
    ],
    hiddenimports=[
        'schedule',
        'sqlite3',
        'threading',
        'concurrent.futures',
        'xml.etree.ElementTree',
        'mimetypes',
        'fnmatch',
        'urllib.request',
        'urllib.parse',
        'urllib.error',
        'hashlib',
        'hmac',
        'base64',
        'time',
        'datetime',
        'json',
        'uuid',
        'logging',
        'dataclasses',
        'http.server',
        'socketserver'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='lightrek',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
EOF

# 执行编译
python3 -m PyInstaller --clean lightrek_linux.spec

# 验证编译结果
file dist/lightrek
```

## 方法二：Docker交叉编译

### 前提条件
- 安装Docker
- 启用buildx（多架构支持）

### 1. 启用Docker Buildx
```bash
docker buildx create --name multiarch --use
docker buildx inspect --bootstrap
```

### 2. 编译ARM64版本
```bash
# 创建Dockerfile.arm64
cat > Dockerfile.arm64 << 'EOF'
FROM --platform=linux/arm64 python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y gcc g++ binutils upx-ucl file && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY . .

# 安装Python依赖
RUN pip3 install --no-cache-dir -r requirements.txt
RUN pip3 install --no-cache-dir pyinstaller

# 编译
RUN python3 -m PyInstaller --clean lightrek_linux.spec

# 验证文件
RUN file dist/lightrek

CMD ["echo", "编译完成"]
EOF

# 构建镜像
docker buildx build --platform linux/arm64 -f Dockerfile.arm64 -t lightrek-builder-arm64 .

# 运行容器
docker run --platform linux/arm64 --name lightrek-build-arm64 lightrek-builder-arm64

# 复制编译结果
docker cp lightrek-build-arm64:/app/dist/lightrek ./lightrek_arm64

# 清理容器
docker rm lightrek-build-arm64
```

### 3. 编译x86_64版本
```bash
# 创建Dockerfile.x64
cat > Dockerfile.x64 << 'EOF'
FROM --platform=linux/amd64 python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y gcc g++ binutils upx-ucl file && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY . .

# 安装Python依赖
RUN pip3 install --no-cache-dir -r requirements.txt
RUN pip3 install --no-cache-dir pyinstaller

# 编译
RUN python3 -m PyInstaller --clean lightrek_linux.spec

# 验证文件
RUN file dist/lightrek

CMD ["echo", "编译完成"]
EOF

# 构建镜像
docker buildx build --platform linux/amd64 -f Dockerfile.x64 -t lightrek-builder-x64 .

# 运行容器
docker run --platform linux/amd64 --name lightrek-build-x64 lightrek-builder-x64

# 复制编译结果
docker cp lightrek-build-x64:/app/dist/lightrek ./lightrek_x64

# 清理容器
docker rm lightrek-build-x64
```

## 方法三：GitHub Actions自动编译

### 创建GitHub Actions工作流
在项目根目录创建`.github/workflows/build-linux.yml`：

```yaml
name: Build Linux Binaries

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build-linux:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        arch: [x64, arm64]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Set up QEMU
      if: matrix.arch == 'arm64'
      uses: docker/setup-qemu-action@v2
      with:
        platforms: arm64
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pyinstaller
    
    - name: Build with PyInstaller (x64)
      if: matrix.arch == 'x64'
      run: |
        python -m PyInstaller --clean lightrek_linux.spec
        mv dist/lightrek dist/lightrek_x64
    
    - name: Build with Docker (arm64)
      if: matrix.arch == 'arm64'
      run: |
        docker buildx build --platform linux/arm64 --load -f Dockerfile.arm64 -t lightrek-builder-arm64 .
        docker run --name lightrek-build-arm64 lightrek-builder-arm64
        docker cp lightrek-build-arm64:/app/dist/lightrek ./lightrek_arm64
        docker rm lightrek-build-arm64
    
    - name: Create release package
      run: |
        mkdir -p releases/lightrek-linux-${{ matrix.arch }}
        cp lightrek_${{ matrix.arch }} releases/lightrek-linux-${{ matrix.arch }}/lightrek
        chmod +x releases/lightrek-linux-${{ matrix.arch }}/lightrek
        cp lightrek_config.json releases/lightrek-linux-${{ matrix.arch }}/
        cp README.md releases/lightrek-linux-${{ matrix.arch }}/
        cp "lightrek logo 64px.png" releases/lightrek-linux-${{ matrix.arch }}/
        cp "lightrek logo 32px.png" releases/lightrek-linux-${{ matrix.arch }}/
        
        # 创建启动脚本
        cat > releases/lightrek-linux-${{ matrix.arch }}/start.sh << 'EOF'
        #!/bin/bash
        echo "🚀 启动 LightRek S3同步工具..."
        echo "平台: Linux ${{ matrix.arch }}"
        echo "版本: v2.1.0"
        echo ""
        if [ ! -x "./lightrek" ]; then
            echo "设置执行权限..."
            chmod +x ./lightrek
        fi
        ./lightrek
        EOF
        chmod +x releases/lightrek-linux-${{ matrix.arch }}/start.sh
        
        # 创建压缩包
        cd releases
        tar -czf lightrek-linux-${{ matrix.arch }}.tar.gz lightrek-linux-${{ matrix.arch }}/
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: lightrek-linux-${{ matrix.arch }}
        path: releases/lightrek-linux-${{ matrix.arch }}.tar.gz
```

## 编译后验证

### 1. 检查文件架构
```bash
file lightrek_arm64
file lightrek_x64
```

### 2. 测试运行
```bash
# 测试ARM64版本（需要ARM64系统或模拟器）
./lightrek_arm64 --version

# 测试x64版本
./lightrek_x64 --version
```

### 3. 创建发布包
```bash
# 创建发布目录结构
mkdir -p releases/lightrek-linux-arm64
mkdir -p releases/lightrek-linux-x64

# 复制文件到发布目录
cp lightrek_arm64 releases/lightrek-linux-arm64/lightrek
cp lightrek_x64 releases/lightrek-linux-x64/lightrek
chmod +x releases/lightrek-linux-*/lightrek

# 复制配置文件
for arch in arm64 x64; do
    cp lightrek_config.json releases/lightrek-linux-$arch/
    cp README.md releases/lightrek-linux-$arch/
    cp "lightrek logo 64px.png" releases/lightrek-linux-$arch/
    cp "lightrek logo 32px.png" releases/lightrek-linux-$arch/
done

# 创建压缩包
cd releases
tar -czf lightrek-linux-arm64.tar.gz lightrek-linux-arm64/
tar -czf lightrek-linux-x64.tar.gz lightrek-linux-x64/
```

## 常见问题

### 1. PyInstaller编译失败
- 确保安装了所有系统依赖
- 尝试升级PyInstaller版本：`pip3 install --upgrade pyinstaller`
- 检查Python版本兼容性

### 2. 缺少系统库
```bash
# 安装缺少的开发库
sudo apt-get install -y python3-dev libffi-dev libssl-dev
```

### 3. UPX压缩失败
如果UPX压缩失败，可以在spec文件中禁用：
```python
exe = EXE(
    # ...
    upx=False,  # 禁用UPX压缩
    # ...
)
```

### 4. 交叉编译问题
- 确保Docker支持多架构
- 检查网络连接，可能需要配置镜像源
- 尝试使用本地编译替代交叉编译

## 编译结果

成功编译后，您将获得：
- `lightrek-linux-arm64.tar.gz` - ARM64版本
- `lightrek-linux-x64.tar.gz` - x86_64版本

每个压缩包包含：
- `lightrek` - 主程序可执行文件
- `start.sh` - 启动脚本
- `lightrek_config.json` - 配置文件
- `README.md` - 使用说明
- 图标文件

## 技术支持

如果编译过程中遇到问题，请：
1. 检查系统依赖是否完整安装
2. 确认Python版本兼容性
3. 查看详细错误日志
4. 尝试在不同Linux发行版上编译 