#!/bin/bash
# LightRek S3同步工具 - Linux编译脚本

echo "🚀 LightRek S3同步工具 - Linux编译脚本"
echo "=================================================="

# 检查Python版本
python_version=$(python3 --version 2>&1 | grep -oP '\d+\.\d+' | head -1)
required_version="3.7"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ 需要Python 3.7或更高版本，当前版本: $python_version"
    exit 1
fi

echo "✅ Python版本检查通过: $python_version"

# 检查必要文件
required_files=("start_lightrek.py" "lightrek_task_manager.py" "lightrek_database.py" "requirements.txt")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
done

echo "✅ 必要文件检查通过"

# 安装依赖
echo "📦 安装依赖包..."
pip3 install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi
echo "✅ 依赖安装成功"

# 创建PyInstaller规格文件
echo "📝 创建PyInstaller规格文件..."
cat > lightrek_linux.spec << 'EOF'
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['start_lightrek.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('lightrek_config.json', '.'),
    ],
    hiddenimports=[
        'schedule',
        'sqlite3',
        'threading',
        'concurrent.futures',
        'xml.etree.ElementTree',
        'mimetypes',
        'fnmatch',
        'urllib.request',
        'urllib.parse',
        'urllib.error',
        'hashlib',
        'hmac',
        'base64',
        'time',
        'datetime',
        'json',
        'uuid',
        'logging',
        'dataclasses',
        'http.server',
        'socketserver'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='lightrek',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
EOF

echo "✅ 规格文件创建成功"

# 编译可执行文件
echo "🔨 开始编译可执行文件..."
system=$(uname -s | tr '[:upper:]' '[:lower:]')
arch=$(uname -m)

echo "系统: $system"
echo "架构: $arch"

python3 -m PyInstaller --clean lightrek_linux.spec
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi
echo "✅ 编译成功"

# 打包发布文件
echo "📦 打包发布文件..."

# 确定架构名称
case $arch in
    x86_64|amd64)
        arch_name="x64"
        ;;
    aarch64|arm64)
        arch_name="arm64"
        ;;
    *)
        arch_name=$arch
        ;;
esac

# 创建发布目录
release_name="lightrek-linux-$arch_name"
release_dir="releases/$release_name"

rm -rf "$release_dir"
mkdir -p "$release_dir"

# 复制可执行文件
if [ -f "dist/lightrek" ]; then
    cp "dist/lightrek" "$release_dir/"
    chmod +x "$release_dir/lightrek"
else
    echo "❌ 可执行文件不存在: dist/lightrek"
    exit 1
fi

# 复制配置文件
config_files=("lightrek_config.json")
for config_file in "${config_files[@]}"; do
    if [ -f "$config_file" ]; then
        cp "$config_file" "$release_dir/"
    fi
done

# 创建启动脚本
cat > "$release_dir/start.sh" << EOF
#!/bin/bash
# LightRek S3同步工具启动脚本

echo "🚀 启动 LightRek S3同步工具..."
echo "平台: linux-$arch_name"
echo "版本: 1.0.0"
echo ""

# 检查权限
if [ ! -x "./lightrek" ]; then
    echo "设置执行权限..."
    chmod +x ./lightrek
fi

# 启动程序
./lightrek
EOF

chmod +x "$release_dir/start.sh"

# 创建README
cat > "$release_dir/README.md" << EOF
# LightRek S3同步工具

## 系统信息
- 平台: linux
- 架构: $arch_name
- 版本: 1.0.0

## 使用说明

### 启动程序
\`\`\`bash
# 方式1: 使用启动脚本
./start.sh

# 方式2: 直接运行
./lightrek
\`\`\`

### 功能特性
- ✅ 多云存储支持（阿里云OSS、腾讯云COS、AWS S3等）
- ✅ 可视化Web管理界面
- ✅ 多任务并发同步
- ✅ 增量/全量/镜像同步模式
- ✅ 大文件分片传输（>100MB）
- ✅ 定时任务调度
- ✅ 实时进度监控
- ✅ 详细日志记录
- ✅ 连接测试和存储桶自动发现

### 访问地址
程序启动后，请访问: http://localhost:8001

### 配置文件
- \`lightrek_config.json\`: 主配置文件
- \`lightrek.db\`: SQLite数据库文件（自动创建）

### 注意事项
1. 确保网络连接正常
2. 配置正确的访问密钥和端点
3. 防火墙允许8001端口访问
4. 大文件传输需要足够的磁盘空间

### 技术支持
如有问题，请检查日志文件：\`lightrek_sync.log\`
EOF

echo "✅ 发布包创建成功: $release_dir"

# 清理构建文件
echo "🧹 清理构建文件..."
rm -rf build dist lightrek_linux.spec
echo "✅ 清理完成"

echo ""
echo "🎉 编译完成！"
echo "📁 发布文件位于 $release_dir 目录"
echo ""
echo "使用方法:"
echo "cd $release_dir"
echo "./start.sh" 