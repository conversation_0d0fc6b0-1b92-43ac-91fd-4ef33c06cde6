#!/usr/bin/env python3
"""
安装所有必要的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package
        ], capture_output=True, text=True, check=True)
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("🚀 开始安装LightRek统一存储同步工具的依赖包...")
    
    # 必要的依赖包列表
    packages = [
        "schedule==1.2.0",
        "pyinstaller==6.3.0", 
        "boto3>=1.26.0",
        "botocore>=1.29.0",
        "requests>=2.28.0",
        "paramiko>=2.11.0",
        "smbprotocol>=1.10.0",
        "pyftpdlib>=1.5.7",
        "cryptography>=3.4.8"
    ]
    
    success_count = 0
    failed_packages = []
    
    for package in packages:
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
    
    print(f"\n📊 安装结果:")
    print(f"✅ 成功安装: {success_count}/{len(packages)} 个包")
    
    if failed_packages:
        print(f"❌ 安装失败的包:")
        for pkg in failed_packages:
            print(f"   - {pkg}")
        print(f"\n💡 提示: 某些包安装失败不会影响基本功能，但可能影响特定存储类型的支持")
    else:
        print(f"🎉 所有依赖包安装成功！")
    
    print(f"\n📋 支持的存储类型:")
    print(f"   ✅ S3对象存储 (AWS、阿里云、腾讯云等)")
    print(f"   ✅ 本地文件系统")
    
    if "paramiko" not in [pkg.split(">=")[0].split("==")[0] for pkg in failed_packages]:
        print(f"   ✅ SFTP (SSH文件传输)")
    else:
        print(f"   ❌ SFTP (需要paramiko库)")
    
    if "smbprotocol" not in [pkg.split(">=")[0].split("==")[0] for pkg in failed_packages]:
        print(f"   ✅ SMB/CIFS (Windows网络共享)")
    else:
        print(f"   ❌ SMB/CIFS (需要smbprotocol库)")
    
    if "pyftpdlib" not in [pkg.split(">=")[0].split("==")[0] for pkg in failed_packages]:
        print(f"   ✅ FTP/FTPS")
    else:
        print(f"   ❌ FTP/FTPS (需要pyftpdlib库)")

if __name__ == "__main__":
    main()
