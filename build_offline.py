#!/usr/bin/env python3
"""
离线构建脚本 - 跳过网络依赖安装，直接构建
"""

import os
import sys
import subprocess
import shutil
import tempfile
import json
from pathlib import Path


def run_command(cmd, cwd=None, timeout=300):
    """执行命令并返回结果"""
    print(f"🔧 执行命令: {cmd}")
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        
        if result.returncode != 0:
            print(f"❌ 命令执行失败 (返回码: {result.returncode})")
            print(f"错误输出: {result.stderr}")
            if result.stdout:
                print(f"标准输出: {result.stdout}")
            return False
        
        if result.stdout:
            print(f"✅ 命令执行成功")
            # 只显示最后几行输出
            lines = result.stdout.strip().split('\n')
            if len(lines) > 3:
                print("...")
                for line in lines[-2:]:
                    print(line)
            else:
                print(result.stdout)
        
        return True
    
    except subprocess.TimeoutExpired:
        print(f"❌ 命令执行超时 ({timeout}秒)")
        return False
    except Exception as e:
        print(f"❌ 命令执行异常: {e}")
        return False


def check_python_modules():
    """检查Python模块是否可用"""
    print("📋 检查Python模块...")
    
    required_modules = [
        'json', 'os', 'sys', 'threading', 'time', 'datetime',
        'pathlib', 'tempfile', 'shutil', 'uuid', 'logging',
        'dataclasses', 'typing', 'sqlite3', 'urllib', 'hashlib',
        'hmac', 'base64', 'xml', 'mimetypes', 'ftplib', 'ssl'
    ]
    
    optional_modules = {
        'flask': 'Web界面功能',
        'paramiko': 'SFTP支持',
        'smbprotocol': 'SMB支持',
        'requests': '测试功能'
    }
    
    # 检查必需模块
    missing_required = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_required.append(module)
    
    if missing_required:
        print(f"❌ 缺少必需模块: {', '.join(missing_required)}")
        return False
    
    print("✅ 所有必需模块可用")
    
    # 检查可选模块
    available_optional = []
    for module, description in optional_modules.items():
        try:
            __import__(module)
            available_optional.append(f"{module} ({description})")
        except ImportError:
            print(f"⚠️ 可选模块不可用: {module} - {description}")
    
    if available_optional:
        print(f"✅ 可用的可选模块: {', '.join(available_optional)}")
    
    return True


def create_simple_main():
    """创建简化的主入口文件"""
    print("📝 创建简化主入口文件...")
    
    main_content = '''#!/usr/bin/env python3
"""
LightRek 统一存储同步工具 - 简化版主入口
"""

import sys
import os
import json
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_modules():
    """检查模块可用性"""
    modules_status = {}
    
    # 检查存储适配器
    storage_modules = [
        ('s3_storage_adapter', 'S3存储'),
        ('local_storage_adapter', '本地存储'),
        ('sftp_storage_adapter', 'SFTP存储'),
        ('smb_storage_adapter', 'SMB存储'),
        ('ftp_storage_adapter', 'FTP存储')
    ]
    
    for module_name, description in storage_modules:
        try:
            __import__(module_name)
            modules_status[description] = True
            print(f"✅ {description}适配器加载成功")
        except ImportError as e:
            modules_status[description] = False
            print(f"⚠️ {description}适配器加载失败: {e}")
    
    return modules_status

def create_demo_config():
    """创建演示配置"""
    demo_dir = Path("demo_data")
    demo_dir.mkdir(exist_ok=True)
    
    source_dir = demo_dir / "source"
    target_dir = demo_dir / "target"
    source_dir.mkdir(exist_ok=True)
    target_dir.mkdir(exist_ok=True)
    
    # 创建演示文件
    (source_dir / "readme.txt").write_text("这是演示文件\\n用于测试同步功能", encoding='utf-8')
    (source_dir / "data.txt").write_text("演示数据内容", encoding='utf-8')
    
    # 创建配置
    config = {
        "version": "2.0",
        "sources": {
            "demo_source": {
                "storage_type": "local",
                "name": "演示源",
                "root_path": str(source_dir.absolute())
            }
        },
        "targets": {
            "demo_target": {
                "storage_type": "local",
                "name": "演示目标",
                "root_path": str(target_dir.absolute())
            }
        },
        "tasks": {},
        "global_settings": {
            "default_max_workers": 5
        }
    }
    
    with open("demo_config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    return source_dir, target_dir

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    try:
        # 测试配置管理器
        from unified_config_manager import UnifiedConfigManager
        config_manager = UnifiedConfigManager("demo_config.json")
        print("✅ 配置管理器工作正常")
        
        # 测试任务管理器
        from unified_task_manager import UnifiedTaskManager
        task_manager = UnifiedTaskManager(config_manager)
        print("✅ 任务管理器工作正常")
        
        # 测试本地存储适配器
        from local_storage_adapter import LocalStorageAdapter, LocalStorageConfig
        config = LocalStorageConfig(root_path=tempfile.gettempdir())
        adapter = LocalStorageAdapter(config)
        success, message = adapter.test_connection()
        if success:
            print("✅ 本地存储适配器工作正常")
        else:
            print(f"⚠️ 本地存储适配器测试失败: {message}")
        
        return True
    
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def start_web_interface():
    """启动Web界面"""
    try:
        from web_interface_updates import app
        print("🌐 启动Web管理界面...")
        print("📍 访问地址: http://localhost:8001")
        print("💡 按 Ctrl+C 停止程序")
        app.run(host='0.0.0.0', port=8001, debug=False)
    except ImportError:
        print("❌ Flask未安装，无法启动Web界面")
        print("💡 请安装Flask: pip install flask")
    except Exception as e:
        print(f"❌ Web界面启动失败: {e}")

def main():
    """主函数"""
    print("🚀 LightRek 统一存储同步工具 (简化版)")
    print("=" * 50)
    
    try:
        # 检查模块
        modules_status = check_modules()
        
        # 创建演示配置
        source_dir, target_dir = create_demo_config()
        print(f"✅ 演示配置创建完成")
        print(f"   源目录: {source_dir}")
        print(f"   目标目录: {target_dir}")
        
        # 测试基本功能
        if test_basic_functionality():
            print("✅ 基本功能测试通过")
        
        # 启动Web界面
        start_web_interface()
        
    except KeyboardInterrupt:
        print("\\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"\\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
'''
    
    with open('main_simple.py', 'w', encoding='utf-8') as f:
        f.write(main_content)
    
    print("✅ 简化主入口文件创建成功")
    return True


def run_basic_tests():
    """运行基本测试"""
    print("🧪 运行基本测试...")
    
    try:
        # 测试导入
        print("测试模块导入...")
        
        # 测试存储抽象层
        from storage_abstraction import StorageType, StorageFactory
        print("✅ 存储抽象层导入成功")
        
        # 测试本地存储适配器
        from local_storage_adapter import LocalStorageAdapter, LocalStorageConfig
        config = LocalStorageConfig(root_path=tempfile.gettempdir())
        adapter = LocalStorageAdapter(config)
        success, message = adapter.test_connection()
        if success:
            print("✅ 本地存储适配器测试成功")
        else:
            print(f"⚠️ 本地存储适配器测试失败: {message}")
        
        # 测试配置管理器
        from unified_config_manager import UnifiedConfigManager
        config_manager = UnifiedConfigManager()
        print("✅ 配置管理器测试成功")
        
        # 测试任务管理器
        from unified_task_manager import UnifiedTaskManager
        task_manager = UnifiedTaskManager(config_manager)
        print("✅ 任务管理器测试成功")
        
        return True
    
    except Exception as e:
        print(f"❌ 基本测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_portable_package():
    """创建便携式包"""
    print("📦 创建便携式包...")
    
    try:
        # 创建发布目录
        release_dir = Path("releases/lightrek-unified-portable")
        if release_dir.exists():
            shutil.rmtree(release_dir)
        release_dir.mkdir(parents=True)
        
        # 复制Python文件
        python_files = [
            "storage_abstraction.py",
            "s3_storage_adapter.py",
            "sftp_storage_adapter.py", 
            "smb_storage_adapter.py",
            "ftp_storage_adapter.py",
            "local_storage_adapter.py",
            "unified_config_manager.py",
            "unified_task_manager.py",
            "web_interface_updates.py",
            "legacy_adapter.py",
            "main_simple.py"
        ]
        
        for file in python_files:
            if Path(file).exists():
                shutil.copy2(file, release_dir / file)
                print(f"✅ 复制文件: {file}")
            else:
                print(f"⚠️ 文件不存在: {file}")
        
        # 复制文档文件
        doc_files = [
            "README_UNIFIED.md",
            "PROJECT_SUMMARY.md",
            "requirements_unified.txt"
        ]
        
        for file in doc_files:
            if Path(file).exists():
                shutil.copy2(file, release_dir / file)
        
        # 创建启动脚本
        # Windows批处理文件
        with open(release_dir / "start.bat", 'w', encoding='utf-8') as f:
            f.write('''@echo off
chcp 65001 >nul
echo Starting LightRek Unified Storage Sync Tool...
echo Version: 2.0.0 (Portable)
echo.
python main_simple.py
pause
''')

        # Linux/Mac shell脚本
        with open(release_dir / "start.sh", 'w', encoding='utf-8') as f:
            f.write('''#!/bin/bash
echo "Starting LightRek Unified Storage Sync Tool..."
echo "Version: 2.0.0 (Portable)"
echo ""
python3 main_simple.py
''')
        
        # 设置执行权限（如果在Unix系统上）
        try:
            os.chmod(release_dir / "start.sh", 0o755)
        except:
            pass
        
        # 创建README
        readme_content = """# LightRek 统一存储同步工具 (便携版)

## 使用说明

### 启动程序
- Windows: 双击 `start.bat`
- Linux/Mac: 运行 `./start.sh` 或 `python3 main_simple.py`

### 功能特性
- ✅ 多种存储类型支持 (S3, SFTP, SMB, FTP, 本地)
- ✅ 统一配置管理
- ✅ 任务管理和监控
- ✅ Web管理界面 (需要Flask)

### 依赖要求
- Python 3.7+
- 可选: Flask (Web界面)
- 可选: paramiko (SFTP支持)
- 可选: smbprotocol (SMB支持)

### 安装依赖
```bash
pip install flask paramiko smbprotocol requests
```

### 访问地址
程序启动后访问: http://localhost:8001

### 注意事项
- 这是便携版，需要Python环境
- 部分功能需要安装对应的依赖包
- 查看 requirements_unified.txt 了解完整依赖列表
"""
        
        with open(release_dir / "README.md", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"✅ 便携式包创建成功: {release_dir}")
        return True
    
    except Exception as e:
        print(f"❌ 便携式包创建失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 LightRek 统一存储同步工具 - 离线构建")
    print("=" * 50)
    
    try:
        # 检查Python模块
        if not check_python_modules():
            print("❌ 模块检查失败")
            sys.exit(1)
        
        # 创建简化主入口
        if not create_simple_main():
            print("❌ 主入口创建失败")
            sys.exit(1)
        
        # 运行基本测试
        if not run_basic_tests():
            print("❌ 基本测试失败")
            sys.exit(1)
        
        # 创建便携式包
        if not create_portable_package():
            print("❌ 便携式包创建失败")
            sys.exit(1)
        
        print("\n🎉 离线构建完成！")
        print("📁 便携式包位于: releases/lightrek-unified-portable/")
        print("🚀 运行方式:")
        print("   cd releases/lightrek-unified-portable/")
        print("   python main_simple.py")
        print("🌐 访问地址: http://localhost:8001")
        
    except Exception as e:
        print(f"\n❌ 构建过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
