"""
存储抽象层 - 统一不同存储类型的接口

支持的存储类型:
- S3兼容对象存储 (AWS S3, 阿里云OSS, 腾讯云COS等)
- SFTP (SSH File Transfer Protocol)
- SMB/CIFS (Windows网络共享)
- FTP/FTPS (File Transfer Protocol)
- 本地文件系统 (包括挂载的NAS)
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Any, Optional, Tuple, Union, BinaryIO
from enum import Enum
import os
from datetime import datetime


class StorageType(Enum):
    """存储类型枚举"""
    S3 = "s3"
    SFTP = "sftp"
    SMB = "smb"
    FTP = "ftp"
    LOCAL = "local"


@dataclass
class StorageConfig:
    """通用存储配置基类"""
    storage_type: StorageType = None
    name: str = ""
    description: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'storage_type': self.storage_type.value,
            'name': self.name,
            'description': self.description
        }


@dataclass
class S3StorageConfig(StorageConfig):
    """S3存储配置"""
    access_key: str = ""
    secret_key: str = ""
    endpoint: str = ""
    region: str = ""
    bucket: str = ""

    def __post_init__(self):
        self.storage_type = StorageType.S3
        if 'aliyuncs.com' in self.endpoint:
            if not self.endpoint.startswith('http'):
                self.endpoint = f'https://{self.endpoint}'
        elif not self.endpoint.startswith('http'):
            self.endpoint = f'https://{self.endpoint}'


@dataclass
class SFTPStorageConfig(StorageConfig):
    """SFTP存储配置"""
    hostname: str = ""
    port: int = 22
    username: str = ""
    password: Optional[str] = None
    private_key_path: Optional[str] = None
    private_key_passphrase: Optional[str] = None
    root_path: str = "/"

    def __post_init__(self):
        self.storage_type = StorageType.SFTP


@dataclass
class SMBStorageConfig(StorageConfig):
    """SMB存储配置"""
    hostname: str = ""
    port: int = 445
    username: str = ""
    password: str = ""
    domain: str = ""
    share_name: str = ""
    root_path: str = "/"

    def __post_init__(self):
        self.storage_type = StorageType.SMB


@dataclass
class FTPStorageConfig(StorageConfig):
    """FTP存储配置"""
    hostname: str = ""
    port: int = 21
    username: str = ""
    password: str = ""
    use_tls: bool = False
    passive_mode: bool = True
    root_path: str = "/"

    def __post_init__(self):
        self.storage_type = StorageType.FTP


@dataclass
class LocalStorageConfig(StorageConfig):
    """本地存储配置"""
    root_path: str = ""

    def __post_init__(self):
        self.storage_type = StorageType.LOCAL
        # 规范化路径
        if self.root_path:
            self.root_path = os.path.abspath(self.root_path)


@dataclass
class FileMetadata:
    """文件元数据"""
    key: str  # 文件路径/键
    size: int  # 文件大小（字节）
    last_modified: datetime  # 最后修改时间
    etag: Optional[str] = None  # 文件哈希/ETag
    content_type: Optional[str] = None  # 内容类型
    custom_metadata: Optional[Dict[str, str]] = None  # 自定义元数据
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式（兼容现有代码）"""
        return {
            'Key': self.key,
            'Size': self.size,
            'LastModified': self.last_modified.isoformat(),
            'ETag': self.etag or '',
            'ContentType': self.content_type or 'binary/octet-stream',
            'CustomMetadata': self.custom_metadata or {}
        }


@dataclass
class ListResult:
    """列表查询结果"""
    files: List[FileMetadata]
    is_truncated: bool = False
    next_token: Optional[str] = None
    total_count: Optional[int] = None


class StorageAdapter(ABC):
    """存储适配器抽象基类"""
    
    def __init__(self, config: StorageConfig):
        self.config = config
    
    @abstractmethod
    def test_connection(self) -> Tuple[bool, str]:
        """测试连接
        
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        pass
    
    @abstractmethod
    def list_files(self, prefix: str = "", max_keys: int = 1000, 
                   continuation_token: Optional[str] = None) -> ListResult:
        """列出文件
        
        Args:
            prefix: 路径前缀
            max_keys: 最大返回数量
            continuation_token: 分页令牌
            
        Returns:
            ListResult: 文件列表结果
        """
        pass
    
    @abstractmethod
    def get_file(self, key: str) -> Optional[bytes]:
        """下载文件
        
        Args:
            key: 文件路径/键
            
        Returns:
            Optional[bytes]: 文件内容，失败返回None
        """
        pass
    
    @abstractmethod
    def put_file(self, key: str, data: bytes, 
                 content_type: str = 'binary/octet-stream',
                 metadata: Optional[Dict[str, str]] = None) -> bool:
        """上传文件
        
        Args:
            key: 文件路径/键
            data: 文件内容
            content_type: 内容类型
            metadata: 自定义元数据
            
        Returns:
            bool: 是否成功
        """
        pass
    
    @abstractmethod
    def delete_file(self, key: str) -> bool:
        """删除文件
        
        Args:
            key: 文件路径/键
            
        Returns:
            bool: 是否成功
        """
        pass
    
    @abstractmethod
    def get_file_metadata(self, key: str) -> Optional[FileMetadata]:
        """获取文件元数据
        
        Args:
            key: 文件路径/键
            
        Returns:
            Optional[FileMetadata]: 文件元数据，失败返回None
        """
        pass
    
    @abstractmethod
    def file_exists(self, key: str) -> bool:
        """检查文件是否存在
        
        Args:
            key: 文件路径/键
            
        Returns:
            bool: 是否存在
        """
        pass
    
    def put_file_chunked(self, key: str, data: bytes, chunk_size_mb: int = 10,
                        content_type: str = 'binary/octet-stream',
                        metadata: Optional[Dict[str, str]] = None) -> bool:
        """分片上传大文件（默认实现，子类可重写优化）
        
        Args:
            key: 文件路径/键
            data: 文件内容
            chunk_size_mb: 分片大小（MB）
            content_type: 内容类型
            metadata: 自定义元数据
            
        Returns:
            bool: 是否成功
        """
        # 默认实现：如果文件小于分片大小，直接上传
        chunk_size = chunk_size_mb * 1024 * 1024
        if len(data) <= chunk_size:
            return self.put_file(key, data, content_type, metadata)
        
        # 大文件分片上传（子类应该重写此方法以优化性能）
        return self.put_file(key, data, content_type, metadata)
    
    def get_storage_info(self) -> Dict[str, Any]:
        """获取存储信息
        
        Returns:
            Dict[str, Any]: 存储信息
        """
        return {
            'type': self.config.storage_type.value,
            'name': self.config.name,
            'description': self.config.description
        }


class StorageFactory:
    """存储适配器工厂类"""
    
    _adapters = {}
    
    @classmethod
    def register_adapter(cls, storage_type: StorageType, adapter_class):
        """注册存储适配器
        
        Args:
            storage_type: 存储类型
            adapter_class: 适配器类
        """
        cls._adapters[storage_type] = adapter_class
    
    @classmethod
    def create_adapter(cls, config: StorageConfig) -> StorageAdapter:
        """创建存储适配器
        
        Args:
            config: 存储配置
            
        Returns:
            StorageAdapter: 存储适配器实例
            
        Raises:
            ValueError: 不支持的存储类型
        """
        if config.storage_type not in cls._adapters:
            raise ValueError(f"不支持的存储类型: {config.storage_type}")
        
        adapter_class = cls._adapters[config.storage_type]
        return adapter_class(config)
    
    @classmethod
    def get_supported_types(cls) -> List[StorageType]:
        """获取支持的存储类型
        
        Returns:
            List[StorageType]: 支持的存储类型列表
        """
        return list(cls._adapters.keys())
