#!/usr/bin/env python3
"""
LightRek S3同步工具 - 本地构建脚本
当Docker不可用时的备用方案
"""

import os
import sys
import platform
import subprocess
import shutil
import argparse
from pathlib import Path

def run_command(cmd, cwd=None):
    """执行命令并返回结果"""
    print(f"执行命令: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"命令执行失败: {result.stderr}")
            return False
        print(f"命令执行成功")
        return True
    except Exception as e:
        print(f"命令执行异常: {e}")
        return False

def check_pyinstaller():
    """检查PyInstaller是否可用"""
    print("🔍 检查PyInstaller...")
    
    try:
        result = subprocess.run([sys.executable, "-m", "PyInstaller", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ PyInstaller可用: {result.stdout.strip()}")
            return True
        else:
            print("❌ PyInstaller不可用，尝试安装...")
            return install_pyinstaller()
    except Exception as e:
        print(f"❌ PyInstaller检查失败: {e}")
        return install_pyinstaller()

def install_pyinstaller():
    """安装PyInstaller"""
    print("📦 安装PyInstaller...")
    
    try:
        # 使用国内镜像源安装
        cmd = f"{sys.executable} -m pip install -i https://mirrors.aliyun.com/pypi/simple/ pyinstaller"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PyInstaller安装成功")
            return True
        else:
            print(f"❌ PyInstaller安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ PyInstaller安装异常: {e}")
        return False

def create_spec_file():
    """创建PyInstaller spec文件"""
    print("📝 创建PyInstaller spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['start_lightrek.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('lightrek_config.json', '.'),
        ('lightrek logo 64px.png', '.'),
        ('lightrek logo 32px.png', '.'),
    ],
    hiddenimports=[
        'schedule',
        'sqlite3',
        'threading',
        'concurrent.futures',
        'xml.etree.ElementTree',
        'mimetypes',
        'fnmatch',
        'urllib.request',
        'urllib.parse',
        'urllib.error',
        'hashlib',
        'hmac',
        'base64',
        'time',
        'datetime',
        'json',
        'uuid',
        'logging',
        'dataclasses',
        'http.server',
        'socketserver'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='lightrek',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('lightrek.spec', 'w') as f:
        f.write(spec_content)
    
    print("✅ spec文件创建成功")
    return True

def build_executable():
    """构建可执行文件"""
    print("🔨 构建可执行文件...")
    
    # 创建spec文件
    if not create_spec_file():
        return False
    
    # 使用PyInstaller构建
    cmd = f"{sys.executable} -m PyInstaller --clean lightrek.spec"
    
    if run_command(cmd):
        print("✅ 构建成功")
        
        # 检查输出文件
        exe_path = Path("dist/lightrek")
        if exe_path.exists():
            print(f"✅ 可执行文件已生成: {exe_path}")
            return True
        else:
            print("❌ 未找到可执行文件")
            return False
    else:
        print("❌ 构建失败")
        return False

def package_release():
    """打包发布文件"""
    print("📦 打包发布文件...")
    
    # 获取当前平台信息
    system = platform.system().lower()
    machine = platform.machine().lower()
    
    if machine == "x86_64":
        arch = "x64"
    elif machine == "arm64":
        arch = "arm64"
    else:
        arch = machine
    
    # 创建发布目录
    release_name = f"lightrek-{system}-{arch}"
    release_dir = Path("releases") / release_name
    
    if release_dir.exists():
        shutil.rmtree(release_dir)
    
    release_dir.mkdir(parents=True, exist_ok=True)
    
    # 复制可执行文件
    exe_path = Path("dist/lightrek")
    if exe_path.exists():
        shutil.copy2(exe_path, release_dir / "lightrek")
        os.chmod(release_dir / "lightrek", 0o755)
    else:
        print(f"❌ 可执行文件不存在: {exe_path}")
        return False
    
    # 复制配置文件和资源文件
    config_files = [
        "lightrek_config.json",
        "README.md",
        "lightrek logo 64px.png",
        "lightrek logo 32px.png"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            shutil.copy2(config_file, release_dir / config_file)
            print(f"✅ 复制文件: {config_file}")
        else:
            print(f"⚠️ 文件不存在: {config_file}")
    
    # 创建启动脚本
    if system == "darwin":
        start_script = release_dir / "start.sh"
        with open(start_script, 'w') as f:
            f.write(f'''#!/bin/bash
# LightRek S3同步工具启动脚本 (macOS {arch}版本)

echo "🚀 启动 LightRek S3同步工具..."
echo "平台: macOS {arch}"
echo "版本: v2.1.0"
echo ""

# 检查权限
if [ ! -x "./lightrek" ]; then
    echo "设置执行权限..."
    chmod +x ./lightrek
fi

# 启动程序
./lightrek
''')
        os.chmod(start_script, 0o755)
    
    # 创建README
    readme_content = f"""# LightRek S3同步工具 - {system.title()} {arch}版本

## 系统信息
- 平台: {system.title()}
- 架构: {arch}
- 版本: v2.1.0

## 使用说明

### 启动程序
```bash
# 方式1: 使用启动脚本
./start.sh

# 方式2: 直接运行
./lightrek
```

### 功能特性
- ✅ 多云存储支持（阿里云OSS、腾讯云COS、AWS S3等）
- ✅ 可视化Web管理界面
- ✅ 多任务并发同步
- ✅ 增量/全量/镜像同步模式
- ✅ 大文件分片传输（>100MB）
- ✅ 定时任务调度
- ✅ 实时进度监控
- ✅ 详细日志记录
- ✅ 连接测试和存储桶自动发现

### 访问地址
程序启动后，请访问: http://localhost:8001

### 技术支持
如有问题，请检查日志文件：`lightrek_sync.log`
"""

    with open(release_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ 发布包创建成功: {release_dir}")
    
    # 创建压缩包
    if system == "darwin":
        zip_path = f"releases/{release_name}.zip"
        cmd = f"cd releases && zip -r {release_name}.zip {release_name}/"
        if run_command(cmd):
            print(f"✅ 创建压缩包: {zip_path}")
    
    return True

def clean_build():
    """清理构建文件"""
    print("🧹 清理构建文件...")
    
    clean_dirs = ["build", "dist", "__pycache__"]
    clean_files = ["lightrek.spec"]
    
    for clean_dir in clean_dirs:
        if Path(clean_dir).exists():
            shutil.rmtree(clean_dir)
            print(f"删除目录: {clean_dir}")
    
    for clean_file in clean_files:
        if Path(clean_file).exists():
            os.remove(clean_file)
            print(f"删除文件: {clean_file}")
    
    print("✅ 清理完成")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='LightRek S3同步工具 - 本地构建脚本')
    parser.add_argument('--clean', action='store_true', help='清理构建文件后退出')
    
    args = parser.parse_args()
    
    if args.clean:
        clean_build()
        return
    
    print("🚀 LightRek S3同步工具 - 本地构建脚本")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        print("❌ 无法安装PyInstaller")
        sys.exit(1)
    
    # 检查必要文件
    required_files = ["start_lightrek.py", "lightrek_task_manager.py", "lightrek_database.py"]
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少必要文件: {file}")
            sys.exit(1)
    
    try:
        # 构建可执行文件
        if build_executable():
            # 打包发布文件
            if package_release():
                print("\n🎉 构建完成！")
                print("📁 发布文件位于 releases/ 目录")
            else:
                print("\n❌ 打包失败")
                sys.exit(1)
        else:
            print("\n❌ 构建失败")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n⚠️  构建被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 构建过程中发生错误: {e}")
        sys.exit(1)
    finally:
        # 清理构建文件
        clean_build()

if __name__ == "__main__":
    main() 