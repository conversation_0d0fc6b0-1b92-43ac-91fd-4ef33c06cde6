{"version": "2.0", "sources": {}, "targets": {}, "tasks": {"83f3a7c4-5f91-458b-8199-c990a3c67aaa": {"task_id": "83f3a7c4-5f91-458b-8199-c990a3c67aaa", "name": "test3", "description": "cursor", "source_id": "3d0e8e77-70ed-4e82-ae66-10d3a8ad759f", "target_id": "6761afe2-428a-4676-9af6-e30ee195008f", "prefix": "", "sync_mode": "incremental", "delete_extra": false, "file_filter": "", "exclude_filter": "", "max_workers": 20, "retry_times": 5, "retry_delay": 3, "verify_integrity": true, "bandwidth_limit": 0, "chunk_threshold": 100, "chunk_size": 10, "schedule_type": "manual", "schedule_interval": 1, "schedule_time": "02:00", "enabled": true, "created_at": "2025-06-20T00:51:35.580210"}}, "global_settings": {"default_max_workers": 20, "default_retry_times": 5, "default_retry_delay": 3, "default_chunk_size_mb": 10, "default_bandwidth_limit": 0}}