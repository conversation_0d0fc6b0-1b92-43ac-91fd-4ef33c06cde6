#!/usr/bin/env python3
"""
LightRek 统一存储同步工具 - 简化版主入口
"""

import sys
import os
import json
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_modules():
    """检查模块可用性"""
    modules_status = {}
    
    # 检查存储适配器
    storage_modules = [
        ('s3_storage_adapter', 'S3存储'),
        ('local_storage_adapter', '本地存储'),
        ('sftp_storage_adapter', 'SFTP存储'),
        ('smb_storage_adapter', 'SMB存储'),
        ('ftp_storage_adapter', 'FTP存储')
    ]
    
    for module_name, description in storage_modules:
        try:
            __import__(module_name)
            modules_status[description] = True
            print(f"✅ {description}适配器加载成功")
        except ImportError as e:
            modules_status[description] = False
            print(f"⚠️ {description}适配器加载失败: {e}")
    
    return modules_status

def create_demo_config():
    """创建演示配置"""
    demo_dir = Path("demo_data")
    demo_dir.mkdir(exist_ok=True)
    
    source_dir = demo_dir / "source"
    target_dir = demo_dir / "target"
    source_dir.mkdir(exist_ok=True)
    target_dir.mkdir(exist_ok=True)
    
    # 创建演示文件
    (source_dir / "readme.txt").write_text("这是演示文件\n用于测试同步功能", encoding='utf-8')
    (source_dir / "data.txt").write_text("演示数据内容", encoding='utf-8')
    
    # 创建配置
    config = {
        "version": "2.0",
        "sources": {
            "demo_source": {
                "storage_type": "local",
                "name": "演示源",
                "root_path": str(source_dir.absolute())
            }
        },
        "targets": {
            "demo_target": {
                "storage_type": "local",
                "name": "演示目标",
                "root_path": str(target_dir.absolute())
            }
        },
        "tasks": {},
        "global_settings": {
            "default_max_workers": 5
        }
    }
    
    with open("demo_config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    return source_dir, target_dir

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    try:
        # 测试配置管理器
        from unified_config_manager import UnifiedConfigManager
        config_manager = UnifiedConfigManager("demo_config.json")
        print("✅ 配置管理器工作正常")
        
        # 测试任务管理器
        from unified_task_manager import UnifiedTaskManager
        task_manager = UnifiedTaskManager(config_manager)
        print("✅ 任务管理器工作正常")
        
        # 测试本地存储适配器
        from local_storage_adapter import LocalStorageAdapter, LocalStorageConfig
        config = LocalStorageConfig(root_path=tempfile.gettempdir())
        adapter = LocalStorageAdapter(config)
        success, message = adapter.test_connection()
        if success:
            print("✅ 本地存储适配器工作正常")
        else:
            print(f"⚠️ 本地存储适配器测试失败: {message}")
        
        return True
    
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def start_web_interface():
    """启动Web界面"""
    try:
        from web_interface_updates import app
        print("🌐 启动Web管理界面...")
        print("📍 访问地址: http://localhost:8001")
        print("💡 按 Ctrl+C 停止程序")
        app.run(host='0.0.0.0', port=8001, debug=False)
    except ImportError:
        print("❌ Flask未安装，无法启动Web界面")
        print("💡 请安装Flask: pip install flask")
    except Exception as e:
        print(f"❌ Web界面启动失败: {e}")

def main():
    """主函数"""
    print("🚀 LightRek 统一存储同步工具 (简化版)")
    print("=" * 50)
    
    try:
        # 检查模块
        modules_status = check_modules()
        
        # 创建演示配置
        source_dir, target_dir = create_demo_config()
        print(f"✅ 演示配置创建完成")
        print(f"   源目录: {source_dir}")
        print(f"   目标目录: {target_dir}")
        
        # 测试基本功能
        if test_basic_functionality():
            print("✅ 基本功能测试通过")
        
        # 启动Web界面
        start_web_interface()
        
    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
