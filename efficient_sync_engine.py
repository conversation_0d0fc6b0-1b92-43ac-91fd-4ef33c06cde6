"""
高效同步引擎 - 支持块级别同步、并行处理、断点续传
"""

import hashlib
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Tuple
import logging
from dataclasses import dataclass
from storage_abstraction import StorageAdapter, FileMetadata
from database_manager import db_manager


@dataclass
class SyncProgress:
    """同步进度信息"""
    total_files: int = 0
    completed_files: int = 0
    failed_files: int = 0
    skipped_files: int = 0
    total_bytes: int = 0
    transferred_bytes: int = 0
    current_file: str = ""
    speed_mbps: float = 0.0


class EfficientSyncEngine:
    """高效同步引擎"""
    
    def __init__(self, max_workers: int = 8, chunk_size: int = 8 * 1024 * 1024):
        self.max_workers = max_workers
        self.chunk_size = chunk_size  # 8MB chunks
        self.logger = logging.getLogger(__name__)
        self._stop_event = threading.Event()
        self._progress_lock = threading.Lock()
        
    def sync_files(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter,
                   files_to_sync: List[FileMetadata], task_id: str, execution_id: str,
                   progress_callback: Optional[callable] = None) -> SyncProgress:
        """高效同步文件列表"""
        
        progress = SyncProgress(total_files=len(files_to_sync))
        
        # 计算总字节数
        for file_meta in files_to_sync:
            try:
                size = int(file_meta.size) if file_meta.size else 0
                progress.total_bytes += size
            except (ValueError, TypeError):
                pass
        
        self.logger.info(f"开始高效同步: {progress.total_files} 个文件, 总大小: {progress.total_bytes / (1024*1024):.2f} MB")
        
        # 按文件大小分组，优化同步策略
        small_files = []  # < 1MB
        medium_files = []  # 1MB - 100MB
        large_files = []  # > 100MB
        
        for file_meta in files_to_sync:
            try:
                size = int(file_meta.size) if file_meta.size else 0
                if size < 1024 * 1024:  # 1MB
                    small_files.append(file_meta)
                elif size < 100 * 1024 * 1024:  # 100MB
                    medium_files.append(file_meta)
                else:
                    large_files.append(file_meta)
            except (ValueError, TypeError):
                small_files.append(file_meta)
        
        self.logger.info(f"文件分组: 小文件 {len(small_files)}, 中等文件 {len(medium_files)}, 大文件 {len(large_files)}")
        
        start_time = time.time()
        
        try:
            # 1. 并行同步小文件 (高并发)
            if small_files:
                self._sync_small_files_parallel(source_adapter, target_adapter, small_files, 
                                               progress, execution_id, progress_callback)
            
            # 2. 并行同步中等文件 (中等并发)
            if medium_files:
                self._sync_medium_files_parallel(source_adapter, target_adapter, medium_files,
                                                progress, execution_id, progress_callback)
            
            # 3. 串行同步大文件 (块级别传输)
            if large_files:
                self._sync_large_files_chunked(source_adapter, target_adapter, large_files,
                                              progress, execution_id, progress_callback)
            
        except Exception as e:
            self.logger.error(f"同步过程中出错: {e}")
            db_manager.add_task_log(execution_id, 'ERROR', f'同步失败: {e}')
        
        # 计算同步速度
        elapsed_time = time.time() - start_time
        if elapsed_time > 0:
            progress.speed_mbps = (progress.transferred_bytes / (1024 * 1024)) / elapsed_time
        
        self.logger.info(f"同步完成: {progress.completed_files}/{progress.total_files} 文件, "
                        f"速度: {progress.speed_mbps:.2f} MB/s")
        
        return progress
    
    def _sync_small_files_parallel(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter,
                                  files: List[FileMetadata], progress: SyncProgress, execution_id: str,
                                  progress_callback: Optional[callable] = None):
        """并行同步小文件"""
        self.logger.info(f"开始并行同步 {len(files)} 个小文件")

        # 对于SFTP，降低并发数避免连接过载
        max_workers = 2 if 'SFTP' in source_adapter.__class__.__name__ else self.max_workers

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {
                executor.submit(self._sync_single_file, source_adapter, target_adapter, file_meta, execution_id): file_meta
                for file_meta in files
            }
            
            for future in as_completed(future_to_file):
                if self._stop_event.is_set():
                    break
                    
                file_meta = future_to_file[future]
                try:
                    success, bytes_transferred = future.result()
                    with self._progress_lock:
                        if success:
                            progress.completed_files += 1
                            progress.transferred_bytes += bytes_transferred
                        else:
                            progress.failed_files += 1
                        
                        progress.current_file = file_meta.key
                        
                        if progress_callback:
                            progress_callback(progress)
                            
                except Exception as e:
                    self.logger.error(f"同步文件 {file_meta.key} 失败: {e}")
                    with self._progress_lock:
                        progress.failed_files += 1
    
    def _sync_medium_files_parallel(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter,
                                   files: List[FileMetadata], progress: SyncProgress, execution_id: str,
                                   progress_callback: Optional[callable] = None):
        """并行同步中等文件"""
        self.logger.info(f"开始并行同步 {len(files)} 个中等文件")

        # 对于SFTP，使用更少的并发数
        if 'SFTP' in source_adapter.__class__.__name__:
            workers = 1  # SFTP串行处理中等文件
        else:
            workers = min(4, self.max_workers)
        
        with ThreadPoolExecutor(max_workers=workers) as executor:
            future_to_file = {
                executor.submit(self._sync_single_file, source_adapter, target_adapter, file_meta, execution_id): file_meta
                for file_meta in files
            }
            
            for future in as_completed(future_to_file):
                if self._stop_event.is_set():
                    break
                    
                file_meta = future_to_file[future]
                try:
                    success, bytes_transferred = future.result()
                    with self._progress_lock:
                        if success:
                            progress.completed_files += 1
                            progress.transferred_bytes += bytes_transferred
                        else:
                            progress.failed_files += 1
                        
                        progress.current_file = file_meta.key
                        
                        if progress_callback:
                            progress_callback(progress)
                            
                except Exception as e:
                    self.logger.error(f"同步文件 {file_meta.key} 失败: {e}")
                    with self._progress_lock:
                        progress.failed_files += 1
    
    def _sync_large_files_chunked(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter,
                                 files: List[FileMetadata], progress: SyncProgress, execution_id: str,
                                 progress_callback: Optional[callable] = None):
        """块级别同步大文件"""
        self.logger.info(f"开始块级别同步 {len(files)} 个大文件")
        
        for file_meta in files:
            if self._stop_event.is_set():
                break
                
            try:
                success, bytes_transferred = self._sync_large_file_chunked(
                    source_adapter, target_adapter, file_meta, execution_id, progress_callback
                )
                
                with self._progress_lock:
                    if success:
                        progress.completed_files += 1
                        progress.transferred_bytes += bytes_transferred
                    else:
                        progress.failed_files += 1
                    
                    progress.current_file = file_meta.key
                    
                    if progress_callback:
                        progress_callback(progress)
                        
            except Exception as e:
                self.logger.error(f"同步大文件 {file_meta.key} 失败: {e}")
                with self._progress_lock:
                    progress.failed_files += 1
    
    def _sync_single_file(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter,
                         file_meta: FileMetadata, execution_id: str) -> Tuple[bool, int]:
        """同步单个文件，带重试机制"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                # 下载文件
                self.logger.debug(f"开始下载文件: {file_meta.key} (尝试 {attempt + 1}/{max_retries})")
                file_data = source_adapter.get_file(file_meta.key)
                if file_data is None:
                    if attempt < max_retries - 1:
                        self.logger.warning(f"下载失败，将重试: {file_meta.key} (尝试 {attempt + 1}/{max_retries})")
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    else:
                        error_msg = f"下载失败，已达最大重试次数: {file_meta.key}"
                        self.logger.error(error_msg)
                        db_manager.add_task_log(execution_id, 'ERROR', error_msg)
                        return False, 0

                self.logger.debug(f"文件下载成功: {file_meta.key}, 大小: {len(file_data)} 字节")
                break  # 下载成功，跳出重试循环

            except Exception as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"下载异常，将重试: {file_meta.key} - {e}")
                    time.sleep(1)
                    continue
                else:
                    error_msg = f"下载异常，已达最大重试次数: {file_meta.key} - {e}"
                    self.logger.error(error_msg)
                    db_manager.add_task_log(execution_id, 'ERROR', error_msg)
                    return False, 0
            
        # 上传文件
        try:
            self.logger.debug(f"开始上传文件: {file_meta.key}")
            success = target_adapter.put_file(
                file_meta.key, file_data,
                file_meta.content_type or 'binary/octet-stream'
            )

            if success:
                bytes_transferred = len(file_data)
                self.logger.debug(f"文件上传成功: {file_meta.key}")

                # 更新文件哈希记录
                try:
                    file_hash = hashlib.md5(file_data).hexdigest()
                    db_manager.update_file_hash(
                        f"target_{target_adapter.__class__.__name__}",
                        file_meta.key,
                        len(file_data),
                        file_hash,
                        file_meta.last_modified.isoformat() if file_meta.last_modified else ""
                    )
                except Exception as e:
                    self.logger.warning(f"更新文件哈希失败: {file_meta.key} - {e}")

                return True, bytes_transferred
            else:
                error_msg = f"文件上传失败: {file_meta.key}"
                self.logger.warning(error_msg)
                db_manager.add_task_log(execution_id, 'WARNING', error_msg)
                return False, 0

        except Exception as e:
            error_msg = f"同步文件异常: {file_meta.key} - {e}"
            self.logger.error(error_msg)
            db_manager.add_task_log(execution_id, 'ERROR', error_msg)
            return False, 0
    
    def _sync_large_file_chunked(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter,
                                file_meta: FileMetadata, execution_id: str, 
                                progress_callback: Optional[callable] = None) -> Tuple[bool, int]:
        """块级别同步大文件"""
        try:
            # 对于大文件，如果目标适配器支持分片上传，使用分片上传
            file_data = source_adapter.get_file(file_meta.key)
            if file_data is None:
                return False, 0
            
            # 检查目标适配器是否支持分片上传
            if hasattr(target_adapter, 'put_file_chunked'):
                success = target_adapter.put_file_chunked(
                    file_meta.key, file_data, self.chunk_size,
                    file_meta.content_type or 'binary/octet-stream'
                )
            else:
                # 回退到普通上传
                success = target_adapter.put_file(
                    file_meta.key, file_data,
                    file_meta.content_type or 'binary/octet-stream'
                )
            
            if success:
                bytes_transferred = len(file_data)
                # 更新文件哈希记录
                file_hash = hashlib.md5(file_data).hexdigest()
                db_manager.update_file_hash(
                    f"target_{target_adapter.__class__.__name__}",
                    file_meta.key,
                    len(file_data),
                    file_hash,
                    file_meta.last_modified.isoformat() if file_meta.last_modified else ""
                )
                
                db_manager.add_task_log(execution_id, 'INFO', 
                                      f'大文件同步成功: {file_meta.key} ({bytes_transferred / (1024*1024):.2f} MB)')
                return True, bytes_transferred
            else:
                return False, 0
                
        except Exception as e:
            self.logger.error(f"块级别同步文件 {file_meta.key} 时出错: {e}")
            db_manager.add_task_log(execution_id, 'ERROR', f'大文件同步失败: {file_meta.key} - {e}')
            return False, 0
    
    def stop(self):
        """停止同步"""
        self._stop_event.set()
        self.logger.info("同步引擎停止信号已发送")
