#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRek 统一存储同步工具 - 完整Web界面
"""

import json
import threading
import time
import uuid
import sys
from http.server import HTTPServer, BaseHTTPRequestHandler
from unified_config_manager import UnifiedConfigManager
from unified_task_manager import UnifiedTaskManager
from legacy_adapter import LegacyTaskManagerAdapter

class CompleteWebInterface:
    """完整Web界面"""

    def __init__(self, config_manager: UnifiedConfigManager, task_manager: UnifiedTaskManager, port: int = 8001):
        self.config_manager = config_manager
        self.task_manager = task_manager
        self.db_manager = None  # 简化版本，暂不使用数据库
        self.port = port
        self.server = None
        self.server_thread = None

        # 创建遗留适配器以兼容原来的接口
        self.legacy_adapter = LegacyTaskManagerAdapter(task_manager)
    
    def start(self):
        """启动Web服务器"""
        handler = self._create_handler()
        self.server = HTTPServer(('localhost', self.port), handler)
        self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
        self.server_thread.start()
        print(f"🌐 Web界面已启动: http://localhost:{self.port}")
    
    def stop(self):
        """停止Web服务器"""
        if self.server:
            self.server.shutdown()
    
    def _create_handler(self):
        """创建请求处理器"""
        config_manager = self.config_manager
        task_manager = self.task_manager
        db_manager = self.db_manager
        
        class RequestHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/' or self.path == '/dashboard':
                    self._serve_dashboard()
                elif self.path == '/manual':
                    self._serve_manual()
                elif self.path.startswith('/logs'):
                    self._serve_task_logs_page()
                elif self.path == '/api/sources':
                    self._serve_sources()
                elif self.path == '/api/targets':
                    self._serve_targets()
                elif self.path == '/api/tasks':
                    self._serve_tasks()
                elif self.path == '/api/task-status' or self.path == '/api/task_status':
                    self._serve_task_status()
                elif self.path == '/api/task-executions' or self.path == '/api/task_executions':
                    self._serve_task_executions()
                elif self.path.startswith('/api/task-logs/') or self.path.startswith('/api/task_logs/'):
                    execution_id = self.path.split('/')[-1]
                    self._serve_task_logs(execution_id)
                elif self.path.startswith('/api/task-details/'):
                    task_id = self.path.split('/')[-1]
                    self._serve_task_details(task_id)
                elif self.path == '/api/statistics':
                    self._serve_statistics()
                elif self.path == '/api/optimization-config':
                    self._serve_optimization_config()
                else:
                    self._serve_404()
            
            def do_POST(self):
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length)
                
                try:
                    data = json.loads(post_data.decode('utf-8')) if post_data else {}
                except:
                    data = {}
                
                if self.path == '/api/sources':
                    self._add_source(data)
                elif self.path == '/api/targets':
                    self._add_target(data)
                elif self.path == '/api/tasks':
                    self._add_task(data)
                elif self.path == '/api/test-connection':
                    self._test_connection(data)
                elif self.path == '/api/list-buckets':
                    self._list_buckets(data)
                elif self.path.startswith('/api/tasks/') and self.path.endswith('/run'):
                    task_id = self.path.split('/')[-2]
                    self._run_task(task_id)
                elif self.path.startswith('/api/tasks/') and self.path.endswith('/stop'):
                    task_id = self.path.split('/')[-2]
                    self._stop_task(task_id)
                elif self.path == '/api/optimization-config':
                    self._save_optimization_config(data)
                else:
                    self._serve_404()
            
            def do_PUT(self):
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length)
                
                try:
                    data = json.loads(post_data.decode('utf-8')) if post_data else {}
                except:
                    data = {}
                
                if self.path.startswith('/api/sources/'):
                    source_id = self.path.split('/')[-1]
                    self._update_source(source_id, data)
                elif self.path.startswith('/api/targets/'):
                    target_id = self.path.split('/')[-1]
                    self._update_target(target_id, data)
                elif self.path.startswith('/api/tasks/'):
                    task_id = self.path.split('/')[-1]
                    self._update_task(task_id, data)
                else:
                    self._serve_404()
            
            def do_DELETE(self):
                if self.path.startswith('/api/sources/'):
                    source_id = self.path.split('/')[-1]
                    self._delete_source(source_id)
                elif self.path.startswith('/api/targets/'):
                    target_id = self.path.split('/')[-1]
                    self._delete_target(target_id)
                elif self.path.startswith('/api/tasks/'):
                    task_id = self.path.split('/')[-1]
                    self._delete_task(task_id)
                else:
                    self._serve_404()
            
            def _serve_manual(self):
                """提供用户手册页面"""
                html = self._get_manual_html()
                self.send_response(200)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _serve_task_logs_page(self):
                """重定向到仪表盘的任务日志页面"""
                self.send_response(302)
                self.send_header('Location', '/#logs')
                self.end_headers()
            
            def _serve_task_logs(self, execution_id):
                """提供特定任务的日志数据"""
                try:
                    logs = db_manager.get_execution_logs(execution_id) if db_manager else []
                    self._send_json({'success': True, 'logs': logs})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'logs': []})
            
            def _serve_task_details(self, task_id):
                """提供任务详细信息"""
                try:
                    task = task_manager.get_task(task_id)
                    if task:
                        # 获取任务的执行历史
                        executions = []  # 简化版本，暂不使用数据库
                        self._send_json({
                            'success': True,
                            'task': {
                                'name': task.name,
                                'description': task.description,
                                'source_id': task.source_id,
                                'target_id': task.target_id
                            },
                            'executions': executions
                        })
                    else:
                        self._send_json({'success': False, 'message': '任务不存在'})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _serve_dashboard(self):
                """提供仪表盘页面"""
                # 获取统计数据
                stats = self._get_dashboard_stats()
                
                # 读取logo文件并转换为base64
                import base64
                import os
                logo_base64 = ""
                try:
                    # 尝试多个可能的logo路径
                    logo_paths = [
                        "lightrek logo 64px.png",
                        "./lightrek logo 64px.png",
                        os.path.join(os.path.dirname(__file__), "lightrek logo 64px.png"),
                        os.path.join(os.getcwd(), "lightrek logo 64px.png")
                    ]
                    
                    for logo_path in logo_paths:
                        if os.path.exists(logo_path):
                            with open(logo_path, "rb") as f:
                                logo_data = f.read()
                                logo_base64 = base64.b64encode(logo_data).decode('utf-8')
                            print(f"成功加载logo: {logo_path}")
                            break
                    else:
                        print("警告: 找不到logo文件，将使用默认图标")
                except Exception as e:
                    print(f"无法加载logo: {e}")
                
                # 生成JavaScript数据
                js_stats = json.dumps(stats)
                
                # 生成HTML内容
                html_template = """
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Lightrek S3 同步工具 - 仪表盘</title>
                    <style>
                        * {{
                            margin: 0;
                            padding: 0;
                            box-sizing: border-box;
                        }}
                        
                        body {{
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                            min-height: 100vh;
                            display: flex;
                            color: #333333;
                        }}
                        
                        .sidebar {{
                            width: 280px;
                            background: rgba(255, 255, 255, 0.95);
                            backdrop-filter: blur(10px);
                            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
                            padding: 20px;
                            overflow-y: auto;
                            border-right: 1px solid #e0e0e0;
                        }}
                        
                        .logo {{
                            text-align: center;
                            margin-bottom: 30px;
                            padding-bottom: 20px;
                            border-bottom: 2px solid #e0e0e0;
                        }}
                        
                        .logo-img {{
                            width: 48px;
                            height: 48px;
                            margin-bottom: 10px;
                        }}
                        
                        .logo h1 {{
                            color: #ff6b35;
                            font-size: 1.8rem;
                            margin-bottom: 5px;
                            font-weight: 700;
                        }}
                        
                        .logo p {{
                            color: #666;
                            font-size: 0.9rem;
                        }}
                        
                        .nav-menu {{
                            list-style: none;
                        }}
                        
                        .nav-item {{
                            margin-bottom: 8px;
                        }}
                        
                        .nav-link {{
                            display: flex;
                            align-items: center;
                            padding: 12px 16px;
                            color: #555;
                            text-decoration: none;
                            border-radius: 8px;
                            transition: all 0.3s ease;
                            font-weight: 500;
                        }}
                        
                        .nav-link:hover {{
                            background: rgba(255, 107, 53, 0.1);
                            color: #ff6b35;
                            transform: translateX(5px);
                        }}
                        
                        .nav-link.active {{
                            background: linear-gradient(135deg, #ff6b35, #ff8c42);
                            color: white;
                            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
                        }}
                        
                        .nav-icon {{
                            margin-right: 12px;
                            font-size: 1.2rem;
                        }}
                        
                        .nav-group-header {{
                            position: relative;
                        }}
                        
                        .nav-arrow {{
                            position: absolute;
                            right: 16px;
                            transition: transform 0.3s ease;
                            font-size: 0.8rem;
                        }}
                        
                        .nav-arrow.expanded {{
                            transform: rotate(180deg);
                        }}
                        
                        .nav-submenu {{
                            list-style: none;
                            max-height: 0;
                            overflow: hidden;
                            transition: max-height 0.3s ease;
                            background: rgba(0, 0, 0, 0.05);
                            border-radius: 0 0 8px 8px;
                            margin-top: 4px;
                        }}
                        
                        .nav-submenu.expanded {{
                            max-height: 200px;
                        }}
                        
                        .nav-subitem {{
                            margin-bottom: 4px;
                        }}
                        
                        .nav-sublink {{
                            display: flex;
                            align-items: center;
                            padding: 8px 16px 8px 32px;
                            color: #666;
                            text-decoration: none;
                            border-radius: 6px;
                            transition: all 0.3s ease;
                            font-weight: 400;
                            font-size: 0.9rem;
                        }}
                        
                        .nav-sublink:hover {{
                            background: rgba(255, 107, 53, 0.15);
                            color: #ff6b35;
                            transform: translateX(5px);
                        }}
                        
                        .nav-sublink.active {{
                            background: linear-gradient(135deg, #ff6b35, #ff8c42);
                            color: white;
                            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
                        }}
                        
                        .nav-sublink .nav-icon {{
                            font-size: 1rem;
                            margin-right: 8px;
                        }}
                        
                        .main-content {{
                            flex: 1;
                            padding: 20px;
                            overflow-y: auto;
                        }}
                        
                        .header {{
                            text-align: center;
                            color: white;
                            margin-bottom: 30px;
                        }}
                        
                        .header h1 {{
                            font-size: 2.5rem;
                            margin-bottom: 10px;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                        }}
                        
                        .header p {{
                            font-size: 1.1rem;
                            opacity: 0.9;
                        }}
                        
                        .stats-grid {{
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                            gap: 20px;
                            margin-bottom: 30px;
                        }}
                        
                        .stat-card {{
                            background: rgba(255, 255, 255, 0.95);
                            border-radius: 15px;
                            padding: 25px;
                            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                            backdrop-filter: blur(10px);
                            border: 1px solid #e0e0e0;
                            transition: transform 0.3s ease;
                        }}
                        
                        .stat-card:hover {{
                            transform: translateY(-5px);
                            border-color: #ff6b35;
                        }}
                        
                        .stat-header {{
                            display: flex;
                            align-items: center;
                            gap: 12px;
                            margin-bottom: 15px;
                        }}
                        
                        .stat-icon {{
                            font-size: 2rem;
                            width: 50px;
                            height: 50px;
                            border-radius: 12px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                        }}
                        
                        .stat-icon.sources {{ background: linear-gradient(45deg, #4CAF50, #45a049); }}
                        .stat-icon.targets {{ background: linear-gradient(45deg, #2196F3, #1976D2); }}
                        .stat-icon.tasks {{ background: linear-gradient(45deg, #FF9800, #F57C00); }}
                        .stat-icon.executions {{ background: linear-gradient(45deg, #9C27B0, #7B1FA2); }}
                        .stat-icon.files {{ background: linear-gradient(45deg, #607D8B, #455A64); }}
                        .stat-icon.storage {{ background: linear-gradient(45deg, #795548, #5D4037); }}
                        .stat-icon.success {{ background: linear-gradient(45deg, #28a745, #20c997); }}
                        .stat-icon.speed {{ background: linear-gradient(45deg, #fd7e14, #e83e8c); }}
                        .stat-icon.running {{ background: linear-gradient(45deg, #17a2b8, #6f42c1); }}
                        
                        .stat-title {{
                            font-size: 1.1rem;
                            color: #333;
                            font-weight: 600;
                        }}
                        
                        .stat-value {{
                            font-size: 2.5rem;
                            font-weight: bold;
                            color: #ff6b35;
                            margin-bottom: 8px;
                        }}
                        
                        .stat-description {{
                            color: #666;
                            font-size: 0.9rem;
                        }}
                        
                        .chart-section {{
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 20px;
                            margin-bottom: 30px;
                        }}
                        
                        .chart-card {{
                            background: rgba(255, 255, 255, 0.95);
                            border-radius: 15px;
                            padding: 25px;
                            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                            border: 1px solid #e0e0e0;
                        }}
                        
                        .chart-title {{
                            font-size: 1.3rem;
                            font-weight: 600;
                            color: #333;
                            margin-bottom: 20px;
                            text-align: center;
                        }}
                        
                        .recent-section {{
                            background: rgba(255, 255, 255, 0.95);
                            border-radius: 15px;
                            padding: 25px;
                            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                            border: 1px solid #e0e0e0;
                        }}
                        
                        .section-title {{
                            font-size: 1.4rem;
                            font-weight: 600;
                            color: #333;
                            margin-bottom: 20px;
                            display: flex;
                            align-items: center;
                            gap: 10px;
                        }}
                        
                        .recent-item {{
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding: 15px;
                            border-radius: 10px;
                            margin-bottom: 10px;
                            background: rgba(0, 0, 0, 0.02);
                            border-left: 4px solid #ff6b35;
                            border: 1px solid #e0e0e0;
                            transition: all 0.3s ease;
                        }}
                        
                        .recent-item:hover {{
                            background: #f8f9fa;
                            border-color: #ff6b35;
                            transform: translateX(5px);
                            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.2);
                        }}
                        
                        .recent-item:last-child {{
                            margin-bottom: 0;
                        }}
                        
                        .recent-info {{
                            flex: 1;
                        }}
                        
                        .recent-name {{
                            font-weight: 600;
                            color: #333;
                            margin-bottom: 5px;
                        }}
                        
                        .recent-details {{
                            color: #666;
                            font-size: 0.9rem;
                        }}
                        
                        .recent-status {{
                            padding: 6px 12px;
                            border-radius: 20px;
                            font-size: 0.8rem;
                            font-weight: 600;
                            text-transform: uppercase;
                        }}
                        
                        .status-completed {{ background: #d4edda; color: #155724; }}
                        .status-running {{ background: #d1ecf1; color: #0c5460; }}
                        .status-failed {{ background: #f8d7da; color: #721c24; }}
                        .status-idle {{ background: #e2e3e5; color: #383d41; }}
                        
                        .progress-bar {{
                            width: 100%;
                            height: 8px;
                            background: #e0e0e0;
                            border-radius: 4px;
                            overflow: hidden;
                            margin-top: 10px;
                        }}
                        
                        .progress-fill {{
                            height: 100%;
                            background: linear-gradient(90deg, #667eea, #764ba2);
                            transition: width 0.3s ease;
                        }}
                        
                        .page-header {{
                            text-align: center;
                            color: #333;
                            margin-bottom: 30px;
                        }}
                        
                        .page-header h2 {{
                            font-size: 2rem;
                            margin-bottom: 10px;
                            color: #ff6b35;
                        }}
                        
                        .page-header p {{
                            font-size: 1rem;
                            opacity: 0.8;
                            color: #666;
                        }}
                        
                        .config-section {{
                            background: rgba(255, 255, 255, 0.95);
                            border-radius: 15px;
                            padding: 25px;
                            margin-bottom: 20px;
                            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                            border: 1px solid #e0e0e0;
                        }}
                        
                        .section-header {{
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 20px;
                            padding-bottom: 15px;
                            border-bottom: 2px solid #e0e0e0;
                        }}
                        
                        .section-header h3 {{
                            color: #ff6b35;
                            font-size: 1.3rem;
                            margin: 0;
                        }}
                        
                        .btn-primary {{
                            background: linear-gradient(135deg, #ff6b35, #ff8c42);
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 500;
                            transition: all 0.3s ease;
                        }}
                        
                        .btn-primary:hover {{
                            transform: translateY(-2px);
                            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
                        }}
                        
                        .config-list {{
                            display: grid;
                            gap: 15px;
                        }}
                        
                        .config-item {{
                            background: #f8f9fa;
                            border-radius: 10px;
                            padding: 20px;
                            border-left: 4px solid #ff6b35;
                            transition: all 0.3s ease;
                            border: 1px solid #e0e0e0;
                        }}
                        
                        .config-item:hover {{
                            transform: translateX(5px);
                            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.2);
                            border-color: #ff6b35;
                        }}
                        
                        .config-header {{
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 10px;
                        }}
                        
                        .config-header h4 {{
                            color: #ff6b35;
                            margin: 0;
                            font-size: 1.1rem;
                        }}
                        
                        .config-actions {{
                            display: flex;
                            gap: 8px;
                        }}
                        
                        .btn-edit, .btn-delete, .btn-run, .btn-view {{
                            padding: 6px 12px;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.9rem;
                            transition: all 0.3s ease;
                        }}
                        
                        .btn-edit {{
                            background: #17a2b8;
                            color: white;
                        }}
                        
                        .btn-delete {{
                            background: #dc3545;
                            color: white;
                        }}
                        
                        .btn-run {{
                            background: #28a745;
                            color: white;
                        }}
                        
                        .btn-view {{
                            background: #6c757d;
                            color: white;
                        }}
                        
                        .btn-edit:hover, .btn-delete:hover, .btn-run:hover, .btn-view:hover {{
                            transform: translateY(-1px);
                            opacity: 0.9;
                        }}
                        
                        .config-desc {{
                            color: #666;
                            margin-bottom: 10px;
                            font-size: 0.9rem;
                        }}
                        
                        .config-details {{
                            display: flex;
                            flex-wrap: wrap;
                            gap: 15px;
                        }}
                        
                        .config-details span {{
                            background: white;
                            padding: 4px 8px;
                            border-radius: 4px;
                            font-size: 0.85rem;
                            color: #555;
                            border: 1px solid #e0e0e0;
                        }}
                        
                        /* 任务日志页面样式 */
                        .filter-controls {{
                            display: flex;
                            gap: 15px;
                            align-items: center;
                            flex-wrap: wrap;
                        }}
                        
                        .filter-controls select, .filter-controls input {{
                            background: #ffffff;
                            border: 2px solid #e0e0e0;
                            border-radius: 6px;
                            padding: 8px 12px;
                            color: #333;
                            font-size: 0.9rem;
                            font-weight: 500;
                        }}
                        
                        .filter-controls select:focus, .filter-controls input:focus {{
                            outline: none;
                            border-color: #ff6b35;
                            box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
                        }}
                        
                        .filter-controls option {{
                            color: #333;
                            background: #ffffff;
                        }}
                        
                        /* 用户手册页面样式 */
                        .manual-toc {{
                            background: #ffffff;
                            border-radius: 10px;
                            padding: 20px;
                            margin-bottom: 30px;
                            border: 2px solid #e0e0e0;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        }}
                        
                        .manual-toc h3 {{
                            color: #ff6b35;
                            margin-bottom: 15px;
                        }}
                        
                        .manual-toc ul {{
                            list-style: none;
                        }}
                        
                        .manual-toc li {{
                            margin: 8px 0;
                        }}
                        
                        .manual-toc a {{
                            color: #333;
                            text-decoration: none;
                            transition: color 0.3s ease;
                            font-weight: 500;
                        }}
                        
                        .manual-toc a:hover {{
                            color: #ff6b35;
                            text-decoration: underline;
                        }}
                        
                        .manual-content {{
                            background: #ffffff;
                            border-radius: 15px;
                            padding: 30px;
                            border: 2px solid #e0e0e0;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        }}
                        
                        .manual-section {{
                            margin-bottom: 40px;
                        }}
                        
                        .manual-section h3 {{
                            color: #ff6b35;
                            border-bottom: 2px solid #ff6b35;
                            padding-bottom: 10px;
                            margin-bottom: 20px;
                        }}
                        
                        .manual-section h4 {{
                            color: #333;
                            margin: 20px 0 10px 0;
                            font-weight: 600;
                        }}
                        
                        .manual-section h5 {{
                            color: #555;
                            margin: 15px 0 8px 0;
                            font-weight: 500;
                        }}
                        
                        .manual-section p {{
                            color: #666;
                            line-height: 1.6;
                            margin-bottom: 15px;
                        }}
                        
                        .manual-section li {{
                            color: #666;
                            line-height: 1.6;
                            margin-bottom: 8px;
                        }}
                        
                        .config-table-wrapper {{
                            overflow-x: auto;
                            margin: 15px 0;
                        }}
                        
                        .config-table {{
                            width: 100%;
                            border-collapse: collapse;
                            background: #ffffff;
                            border-radius: 8px;
                            overflow: hidden;
                            border: 2px solid #e0e0e0;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        }}
                        
                        .config-table th, .config-table td {{
                            padding: 12px 15px;
                            text-align: left;
                            border-bottom: 1px solid #e0e0e0;
                        }}
                        
                        .config-table th {{
                            background: linear-gradient(135deg, #ff6b35, #ff8c42);
                            color: white;
                            font-weight: 600;
                        }}
                        
                        .config-table td {{
                            color: #333;
                            font-weight: 500;
                        }}
                        
                        .config-table tr:hover {{
                            background: #f8f9fa;
                        }}
                        
                        .code-block {{
                            background: #f8f9fa;
                            border-left: 4px solid #ff6b35;
                            padding: 15px;
                            margin: 15px 0;
                            border-radius: 5px;
                            font-family: 'Courier New', monospace;
                            color: #333;
                            border: 2px solid #e0e0e0;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        }}
                        
                        .example-configs {{
                            display: grid;
                            gap: 20px;
                            margin: 20px 0;
                        }}
                        
                        .config-example {{
                            background: #ffffff;
                            border-radius: 8px;
                            padding: 15px;
                            border: 2px solid #e0e0e0;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        }}
                        
                        .config-example h5 {{
                            color: #ff6b35;
                            margin-bottom: 10px;
                        }}
                        
                        .config-example .code-block {{
                            background: #f8f9fa;
                            color: #333;
                            font-weight: 500;
                        }}
                        
                        .troubleshooting-item {{
                            background: #ffffff;
                            border-radius: 8px;
                            padding: 15px;
                            margin: 15px 0;
                            border-left: 4px solid #ff6b35;
                            border: 2px solid #e0e0e0;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        }}
                        
                        .troubleshooting-item strong {{
                            color: #ff6b35;
                        }}
                        
                        /* 任务日志额外样式 */
                        .task-execution-count {{
                            background: rgba(255, 107, 53, 0.2);
                            color: #ff6b35;
                            padding: 4px 8px;
                            border-radius: 4px;
                            font-size: 0.8rem;
                            font-weight: 600;
                        }}
                        
                        .execution-list {{
                            margin-top: 15px;
                        }}
                        
                        .execution-item {{
                            background: #ffffff;
                            border-radius: 8px;
                            padding: 15px;
                            margin-bottom: 10px;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            border: 2px solid #e0e0e0;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        }}
                        
                        .execution-item:hover {{
                            background: #f8f9fa;
                            border-color: #ff6b35;
                            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.2);
                            transform: translateY(-2px);
                        }}
                        
                        .execution-header {{
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 10px;
                        }}
                        
                        .execution-time {{
                            font-weight: 600;
                            color: #333;
                            font-size: 0.95rem;
                        }}
                        
                        .status-badge {{
                            padding: 2px 8px;
                            border-radius: 12px;
                            font-size: 0.8rem;
                            font-weight: 600;
                            text-transform: uppercase;
                        }}
                        
                        .status-completed {{
                            background: rgba(40, 167, 69, 0.2);
                            color: #28a745;
                        }}
                        
                        .status-running {{
                            background: rgba(23, 162, 184, 0.2);
                            color: #17a2b8;
                        }}
                        
                        .status-failed {{
                            background: rgba(220, 53, 69, 0.2);
                            color: #dc3545;
                        }}
                        
                        .status-idle {{
                            background: rgba(108, 117, 125, 0.2);
                            color: #6c757d;
                        }}
                        
                        .execution-details {{
                            display: flex;
                            gap: 15px;
                            font-size: 0.85rem;
                            color: #666;
                            font-weight: 500;
                        }}
                        
                        .more-executions {{
                            text-align: center;
                            color: #999;
                            font-style: italic;
                            padding: 8px;
                            font-size: 0.9rem;
                        }}
                        
                        .empty-state {{
                            text-align: center;
                            padding: 60px 20px;
                            color: #666;
                        }}
                        
                        .empty-state h3 {{
                            margin-bottom: 10px;
                            color: #999;
                        }}
                        
                        /* 日志模态框样式 */
                        .log-modal {{
                            display: none;
                            position: fixed;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba(0, 0, 0, 0.7);
                            z-index: 1000;
                        }}
                        
                        .log-modal-content {{
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            background: rgba(0, 0, 0, 0.9);
                            border-radius: 10px;
                            width: 90%;
                            max-width: 1000px;
                            max-height: 80%;
                            overflow: hidden;
                            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
                            border: 1px solid #333;
                        }}
                        
                        .log-modal-header {{
                            background: linear-gradient(135deg, #ff6b35, #ff8c42);
                            color: white;
                            padding: 15px 20px;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }}
                        
                        .log-modal-title {{
                            font-size: 1.2rem;
                            font-weight: 600;
                        }}
                        
                        .close-btn {{
                            background: none;
                            border: none;
                            color: white;
                            font-size: 1.5rem;
                            cursor: pointer;
                            padding: 5px;
                            border-radius: 4px;
                            transition: background 0.3s ease;
                        }}
                        
                        .close-btn:hover {{
                            background: rgba(255, 255, 255, 0.2);
                        }}
                        
                        .log-content {{
                            padding: 20px;
                            max-height: 500px;
                            overflow-y: auto;
                            font-family: 'Courier New', monospace;
                            font-size: 0.9rem;
                            line-height: 1.4;
                            background: rgba(0, 0, 0, 0.5);
                        }}
                        
                        .log-entry {{
                            margin-bottom: 5px;
                            padding: 5px;
                            border-radius: 3px;
                            color: #ddd;
                        }}
                        
                        .log-debug {{
                            background: rgba(33, 150, 243, 0.1);
                            border-left: 3px solid #2196f3;
                        }}
                        
                        .log-info {{
                            background: rgba(76, 175, 80, 0.1);
                            border-left: 3px solid #4caf50;
                        }}
                        
                        .log-warning {{
                            background: rgba(255, 152, 0, 0.1);
                            border-left: 3px solid #ff9800;
                        }}
                        
                        .log-error {{
                            background: rgba(244, 67, 54, 0.1);
                            border-left: 3px solid #f44336;
                        }}
                        
                        /* 编辑表单样式 */
                        .edit-form {{
                            max-width: 800px;
                            margin: 0 auto;
                        }}
                        
                        /* 查看表单样式 */
                        .view-form {{
                            max-width: 800px;
                            margin: 0 auto;
                        }}
                        
                        .view-grid {{
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 20px;
                            margin-bottom: 30px;
                        }}
                        
                        .view-item {{
                            background: white;
                            padding: 20px;
                            border-radius: 8px;
                            border: 2px solid #e0e0e0;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        }}
                        
                        .view-item.full-width {{
                            grid-column: 1 / -1;
                        }}
                        
                        .view-item h4 {{
                            color: #ff6b35;
                            margin-bottom: 10px;
                            font-size: 1rem;
                            font-weight: 600;
                        }}
                        
                        .view-item .value {{
                            color: #333;
                            font-size: 1.1rem;
                            font-weight: 500;
                            word-break: break-all;
                        }}
                        
                        .view-item .value.boolean {{
                            display: inline-block;
                            padding: 4px 12px;
                            border-radius: 20px;
                            font-size: 0.9rem;
                            font-weight: 600;
                        }}
                        
                        .view-item .value.boolean.true {{
                            background: #d4edda;
                            color: #155724;
                        }}
                        
                        .view-item .value.boolean.false {{
                            background: #f8d7da;
                            color: #721c24;
                        }}
                        
                        .view-item .value.status {{
                            display: inline-block;
                            padding: 6px 16px;
                            border-radius: 20px;
                            font-size: 0.9rem;
                            font-weight: 600;
                            text-transform: uppercase;
                        }}
                        
                        .view-item .value.status.running {{
                            background: #d1ecf1;
                            color: #0c5460;
                        }}
                        
                        .view-item .value.status.completed {{
                            background: #d4edda;
                            color: #155724;
                        }}
                        
                        .view-item .value.status.failed {{
                            background: #f8d7da;
                            color: #721c24;
                        }}
                        
                        .view-item .value.status.idle {{
                            background: #e2e3e5;
                            color: #383d41;
                        }}
                        
                        .form-grid {{
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 20px;
                            align-items: start;
                        }}
                        
                        .form-group {{
                            display: flex;
                            flex-direction: column;
                        }}
                        
                        .form-group.full-width {{
                            grid-column: 1 / -1;
                        }}
                        
                        .form-group label {{
                            font-weight: 600;
                            color: #333;
                            margin-bottom: 8px;
                            font-size: 0.9rem;
                        }}
                        
                        .form-group input, .form-group select {{
                            padding: 12px 16px;
                            border: 2px solid #e0e0e0;
                            border-radius: 8px;
                            font-size: 1rem;
                            transition: all 0.3s ease;
                            background: white;
                            color: #333;
                        }}
                        
                        .form-group input:focus, .form-group select:focus {{
                            outline: none;
                            border-color: #ff6b35;
                            box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
                        }}
                        
                        .form-group input:required:invalid {{
                            border-color: #dc3545;
                        }}
                        
                        .checkbox-group {{
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            margin-top: 8px;
                        }}
                        
                        .checkbox-group input[type="checkbox"] {{
                            width: 18px;
                            height: 18px;
                            margin: 0;
                        }}
                        
                        .checkbox-group label {{
                            margin: 0;
                            font-weight: 500;
                            cursor: pointer;
                        }}
                        
                        .form-actions {{
                            display: flex;
                            gap: 15px;
                            justify-content: flex-end;
                            margin-top: 20px;
                            padding-top: 20px;
                            border-top: 2px solid #e0e0e0;
                        }}
                        
                        .btn-secondary {{
                            background: linear-gradient(135deg, #6c757d, #5a6268);
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 500;
                            transition: all 0.3s ease;
                            font-size: 0.9rem;
                        }}
                        
                        .btn-secondary:hover {{
                            transform: translateY(-2px);
                            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
                        }}
                        
                        .btn-cancel {{
                            background: linear-gradient(135deg, #dc3545, #c82333);
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 500;
                            transition: all 0.3s ease;
                            font-size: 0.9rem;
                        }}
                        
                        .btn-cancel:hover {{
                            transform: translateY(-2px);
                            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
                        }}
                        
                        @media (max-width: 768px) {{
                            .form-grid {{
                                grid-template-columns: 1fr;
                                gap: 15px;
                            }}
                            
                            .view-grid {{
                                grid-template-columns: 1fr;
                                gap: 15px;
                            }}
                            
                            .form-actions {{
                                flex-direction: column;
                                gap: 10px;
                            }}
                            
                            .form-actions button {{
                                width: 100%;
                            }}
                        }}
                        
                        @media (max-width: 768px) {{
                            body {{
                                flex-direction: column;
                            }}
                            
                            .sidebar {{
                                width: 100%;
                                height: auto;
                            }}
                            
                            .nav-menu {{
                                display: flex;
                                overflow-x: auto;
                                padding: 10px 0;
                            }}
                            
                            .nav-item {{
                                margin-right: 10px;
                                margin-bottom: 0;
                                flex-shrink: 0;
                            }}
                            
                            .chart-section {{
                                grid-template-columns: 1fr;
                            }}
                            
                            .stats-grid {{
                                grid-template-columns: 1fr;
                            }}
                            
                            .section-header {{
                                flex-direction: column;
                                align-items: flex-start;
                                gap: 10px;
                            }}
                            
                            .config-actions {{
                                flex-direction: column;
                                gap: 5px;
                            }}
                            
                            .config-details {{
                                flex-direction: column;
                                gap: 8px;
                            }}
                        }}
                        
                        /* 优化配置页面样式 */
                        .optimization-form {{
                            background: white;
                            border-radius: 12px;
                            padding: 25px;
                            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                            margin-bottom: 20px;
                        }}
                        
                        .optimization-section {{
                            margin-bottom: 25px;
                            padding: 20px;
                            background: #f8f9fa;
                            border-radius: 8px;
                            border-left: 4px solid #ff6b35;
                        }}
                        
                        .optimization-section h4 {{
                            margin-bottom: 15px;
                            color: #333;
                            font-size: 16px;
                            font-weight: 600;
                        }}
                        
                        .form-row {{
                            display: flex;
                            gap: 20px;
                            flex-wrap: wrap;
                        }}
                        
                        .form-group {{
                            flex: 1;
                            min-width: 250px;
                            margin-bottom: 15px;
                        }}
                        
                        .form-group label {{
                            display: block;
                            margin-bottom: 5px;
                            font-weight: 500;
                            color: #555;
                        }}
                        
                        .form-group input[type="number"] {{
                            width: 100%;
                            padding: 8px 12px;
                            border: 1px solid #ddd;
                            border-radius: 6px;
                            font-size: 14px;
                            transition: border-color 0.3s ease;
                        }}
                        
                        .form-group input[type="number"]:focus {{
                            outline: none;
                            border-color: #ff6b35;
                            box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
                        }}
                        
                        .form-group small {{
                            display: block;
                            margin-top: 5px;
                            color: #666;
                            font-size: 12px;
                        }}
                        
                        /* 开关样式 */
                        .switch {{
                            position: relative;
                            display: inline-block;
                            width: 60px;
                            height: 34px;
                        }}
                        
                        .switch input {{
                            opacity: 0;
                            width: 0;
                            height: 0;
                        }}
                        
                        .slider {{
                            position: absolute;
                            cursor: pointer;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background-color: #ccc;
                            transition: .4s;
                            border-radius: 34px;
                        }}
                        
                        .slider:before {{
                            position: absolute;
                            content: "";
                            height: 26px;
                            width: 26px;
                            left: 4px;
                            bottom: 4px;
                            background-color: white;
                            transition: .4s;
                            border-radius: 50%;
                        }}
                        
                        input:checked + .slider {{
                            background-color: #ff6b35;
                        }}
                        
                        input:checked + .slider:before {{
                            transform: translateX(26px);
                        }}
                        
                        .switch-label {{
                            margin-left: 15px;
                            font-weight: 500;
                            color: #333;
                        }}
                        
                        /* 预设按钮样式 */
                        .preset-buttons {{
                            display: flex;
                            gap: 10px;
                            flex-wrap: wrap;
                        }}
                        
                        .btn-preset {{
                            background: linear-gradient(135deg, #667eea, #764ba2);
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 13px;
                            transition: all 0.3s ease;
                        }}
                        
                        .btn-preset:hover {{
                            background: linear-gradient(135deg, #5a67d8, #6c63ac);
                            transform: translateY(-2px);
                        }}
                        
                        .btn-reset {{
                            background: linear-gradient(135deg, #e53e3e, #c53030);
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 13px;
                            transition: all 0.3s ease;
                        }}
                        
                        .btn-reset:hover {{
                            background: linear-gradient(135deg, #c53030, #a0202c);
                            transform: translateY(-2px);
                        }}
                        
                        /* 信息卡片样式 */
                        .optimization-info {{
                            background: white;
                            border-radius: 12px;
                            padding: 25px;
                            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                        }}
                        
                        .optimization-info h4 {{
                            margin-bottom: 20px;
                            color: #333;
                            font-size: 18px;
                            font-weight: 600;
                        }}
                        
                        .info-grid {{
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                            gap: 15px;
                        }}
                        
                        .info-card {{
                            background: #f8f9fa;
                            padding: 15px;
                            border-radius: 8px;
                            border-left: 4px solid #ff6b35;
                        }}
                        
                        .info-card h5 {{
                            margin-bottom: 8px;
                            color: #333;
                            font-size: 14px;
                            font-weight: 600;
                        }}
                        
                        .info-card p {{
                            color: #666;
                            font-size: 13px;
                            margin: 0;
                            line-height: 1.4;
                        }}
                        
                        .form-actions {{
                            text-align: center;
                            margin-top: 30px;
                            padding-top: 20px;
                            border-top: 1px solid #eee;
                        }}
                        
                        .btn-secondary {{
                            background: #6c757d;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 6px;
                            cursor: pointer;
                            margin-left: 10px;
                            transition: all 0.3s ease;
                        }}
                        
                        .btn-secondary:hover {{
                            background: #5a6268;
                            transform: translateY(-2px);
                        }}
                    </style>
                </head>
                <body>
                    <div class="sidebar">
                        <div class="logo">
                            <img src="data:image/png;base64,{logo_base64}" alt="Lightrek Logo" class="logo-img">
                            <h1>Lightrek</h1>
                            <p>S3 同步工具</p>
                        </div>
                        
                        <ul class="nav-menu">
                            <li class="nav-item">
                                <a href="/" class="nav-link active">
                                    <span class="nav-icon">📊</span>
                                    仪表盘
                                </a>
                            </li>
                            <li class="nav-item nav-group">
                                <a href="#" class="nav-link nav-group-header" onclick="toggleConfigGroup()">
                                    <span class="nav-icon">⚙️</span>
                                    配置管理
                                    <span class="nav-arrow">▼</span>
                                </a>
                                <ul class="nav-submenu" id="config-submenu">
                                    <li class="nav-subitem">
                                        <a href="#" class="nav-sublink" onclick="showSourceConfig()">
                                            <span class="nav-icon">🗂️</span>
                                            来源配置
                                        </a>
                                    </li>
                                    <li class="nav-subitem">
                                        <a href="#" class="nav-sublink" onclick="showTargetConfig()">
                                            <span class="nav-icon">🎯</span>
                                            目标配置
                                        </a>
                                    </li>
                                    <li class="nav-subitem">
                                        <a href="#" class="nav-sublink" onclick="showTaskConfig()">
                                            <span class="nav-icon">📋</span>
                                            任务配置
                                        </a>
                                    </li>
                                    <li class="nav-subitem">
                                        <a href="#" class="nav-sublink" onclick="showOptimizationConfig()">
                                            <span class="nav-icon">⚡</span>
                                            性能优化
                                        </a>
                                    </li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link" onclick="showTaskLogs()">
                                    <span class="nav-icon">📄</span>
                                    任务日志
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link" onclick="showUserManual()">
                                    <span class="nav-icon">📖</span>
                                    用户手册
                                </a>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="main-content">
                        <div class="header">
                            <h1>🚀 Lightrek S3 同步工具</h1>
                            <p>企业级云存储同步解决方案 - 实时监控与管理</p>
                        </div>
                        
                        <!-- 仪表盘页面 -->
                        <div id="dashboard">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon sources">🗂️</div>
                                    <div class="stat-title">数据源</div>
                                </div>
                                <div class="stat-value">{sources_count}</div>
                                <div class="stat-description">已配置的数据源数量</div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon targets">🎯</div>
                                    <div class="stat-title">同步目标</div>
                                </div>
                                <div class="stat-value">{targets_count}</div>
                                <div class="stat-description">已配置的同步目标数量</div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon tasks">⚙️</div>
                                    <div class="stat-title">同步任务</div>
                                </div>
                                <div class="stat-value">{tasks_count}</div>
                                <div class="stat-description">总任务数 | 活跃: {active_tasks}</div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon executions">📈</div>
                                    <div class="stat-title">执行次数</div>
                                </div>
                                <div class="stat-value">{total_executions}</div>
                                <div class="stat-description">总执行次数 | 今日: {today_executions}</div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon files">📁</div>
                                    <div class="stat-title">同步文件</div>
                                </div>
                                <div class="stat-value">{total_files}</div>
                                <div class="stat-description">累计同步文件数量</div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon storage">💾</div>
                                    <div class="stat-title">传输数据</div>
                                </div>
                                <div class="stat-value">{total_size_formatted}</div>
                                <div class="stat-description">累计传输数据量</div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon success">✅</div>
                                    <div class="stat-title">成功率</div>
                                </div>
                                <div class="stat-value">{success_rate:.1f}%</div>
                                <div class="stat-description">任务执行成功率</div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon speed">🚀</div>
                                    <div class="stat-title">平均速度</div>
                                </div>
                                <div class="stat-value">{avg_speed_formatted}</div>
                                <div class="stat-description">平均传输速度</div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-header">
                                    <div class="stat-icon running">⚡</div>
                                    <div class="stat-title">运行中任务</div>
                                </div>
                                <div class="stat-value">{running_tasks}</div>
                                <div class="stat-description">当前正在执行的任务</div>
                            </div>
                        </div>
                        
                        <div class="chart-section">
                            <div class="chart-card">
                                <div class="chart-title">📊 任务状态分布</div>
                                <div id="taskStatusChart"></div>
                            </div>
                            
                            <div class="chart-card">
                                <div class="chart-title">📈 最近7天执行趋势</div>
                                <div id="executionTrendChart"></div>
                            </div>
                        </div>
                        
                        <div class="recent-section">
                            <div class="section-title">
                                🕒 最近执行记录
                            </div>
                            <div id="recentExecutions">
                                {recent_executions_html}
                            </div>
                        </div>
                        </div>
                        
                        <!-- 来源配置页面 -->
                        <div id="source-config" style="display: none;">
                            <div class="page-header">
                                <h2>🗂️ 来源配置</h2>
                                <p>管理数据源配置和连接设置</p>
                            </div>
                            
                            <div class="config-section">
                                <div class="section-header">
                                    <h3>数据源列表</h3>
                                    <button onclick="showAddSourceModal()" class="btn-primary">+ 添加数据源</button>
                                </div>
                                <div id="sources-list" class="config-list"></div>
                            </div>
                        </div>
                        
                        <!-- 目标配置页面 -->
                        <div id="target-config" style="display: none;">
                            <div class="page-header">
                                <h2>🎯 目标配置</h2>
                                <p>管理同步目标配置和存储设置</p>
                            </div>
                            
                            <div class="config-section">
                                <div class="section-header">
                                    <h3>同步目标列表</h3>
                                    <button onclick="showAddTargetModal()" class="btn-primary">+ 添加目标</button>
                                </div>
                                <div id="targets-list" class="config-list"></div>
                            </div>
                        </div>
                        
                        <!-- 性能优化配置页面 -->
                        <div id="optimization-config" style="display: none;">
                            <div class="page-header">
                                <h2>⚡ 性能优化配置</h2>
                                <p>配置扫描性能优化参数，提升同步效率</p>
                            </div>
                            
                            <div class="config-section">
                                <div class="section-header">
                                    <h3>优化设置</h3>
                                    <div class="preset-buttons">
                                        <button onclick="applyPresetConfig('high_performance')" class="btn-preset">高性能</button>
                                        <button onclick="applyPresetConfig('balanced')" class="btn-preset">平衡</button>
                                        <button onclick="applyPresetConfig('compatible')" class="btn-preset">兼容</button>
                                        <button onclick="resetOptimizationConfig()" class="btn-reset">重置默认</button>
                                    </div>
                                </div>
                                
                                <div class="optimization-form">
                                    <div class="form-group">
                                        <label class="switch">
                                            <input type="checkbox" id="optimization-enabled">
                                            <span class="slider"></span>
                                        </label>
                                        <label for="optimization-enabled" class="switch-label">启用性能优化功能</label>
                                    </div>
                                    
                                    <div class="optimization-section">
                                        <h4>📊 并行扫描配置</h4>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="parallel-workers">并行线程数</label>
                                                <input type="number" id="parallel-workers" min="1" max="20" value="5">
                                                <small>建议值：轻量=3，均衡=5，高性能=8</small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="optimization-section">
                                        <h4>💾 缓存管理配置</h4>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="cache-validity">缓存有效期（分钟）</label>
                                                <input type="number" id="cache-validity" min="10" max="480" value="60">
                                                <small>缓存文件信息的有效时间</small>
                                            </div>
                                            <div class="form-group">
                                                <label for="cache-retention">缓存保留期（天）</label>
                                                <input type="number" id="cache-retention" min="1" max="30" value="7">
                                                <small>旧缓存数据的保留时间</small>
                                            </div>
                                        </div>
                                    </div>
                                    

                                    
                                    <div class="optimization-section">
                                        <h4>🔄 流式处理配置</h4>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="batch-size">批处理大小</label>
                                                <input type="number" id="batch-size" min="1000" max="50000" step="1000" value="5000">
                                                <small>大文件列表的分批处理大小</small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="optimization-section">
                                        <h4>🔄 差异同步配置</h4>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label class="switch">
                                                    <input type="checkbox" id="delta-sync-enabled">
                                                    <span class="slider"></span>
                                                </label>
                                                <label for="delta-sync-enabled">启用差异同步 (实验性)</label>
                                            </div>
                                            <div class="form-group">
                                                <label for="delta-chunk-size">块大小</label>
                                                <select id="delta-chunk-size">
                                                    <option value="32768">32 KB</option>
                                                    <option value="65536" selected>64 KB (推荐)</option>
                                                    <option value="131072">128 KB</option>
                                                    <option value="262144">256 KB</option>
                                                </select>
                                                <small>用于差异检测的块大小</small>
                                            </div>
                                        </div>
                                        <div class="warning-note">
                                            <strong>⚠️ 注意:</strong> 差异同步是实验性功能，仅在文件有少量修改时能显著节省传输量。首次同步或文件大幅变化时仍会全量传输。
                                        </div>
                                    </div>
                                    
                                    <div class="form-actions">
                                        <button onclick="saveOptimizationConfig()" class="btn-primary">保存配置</button>
                                        <button onclick="loadOptimizationConfig()" class="btn-secondary">重新加载</button>
                                    </div>
                                </div>
                                
                                <div class="optimization-info">
                                    <h4>💡 性能优化说明</h4>
                                    <div class="info-grid">
                                        <div class="info-card">
                                            <h5>并行扫描</h5>
                                            <p>多线程并发扫描文件，提升文件发现速度50%+</p>
                                        </div>
                                        <div class="info-card">
                                            <h5>智能缓存</h5>
                                            <p>缓存文件元数据，第二次扫描速度提升90%+</p>
                                        </div>

                                        <div class="info-card">
                                            <h5>流式处理</h5>
                                            <p>分批处理大量文件，降低内存占用</p>
                                        </div>
                                        <div class="info-card experimental">
                                            <h5>差异同步</h5>
                                            <p>只传输文件变化部分，大幅减少传输量</p>
                                            <span class="experimental-badge">实验性</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 任务配置页面 -->
                        <div id="task-config" style="display: none;">
                            <div class="page-header">
                                <h2>📋 任务配置</h2>
                                <p>管理同步任务和调度策略</p>
                            </div>
                            
                            <div class="config-section">
                                <div class="section-header">
                                    <h3>同步任务列表</h3>
                                    <button onclick="showAddTaskModal()" class="btn-primary">+ 创建任务</button>
                                </div>
                                <div id="tasks-list" class="config-list"></div>
                            </div>
                        </div>
                        
                        <!-- 任务日志页面 -->
                        <div id="task-logs" style="display: none;">
                            <div class="page-header">
                                <h2>📄 任务日志</h2>
                                <p>查看和管理所有同步任务的执行日志</p>
                            </div>
                            
                            <div class="config-section">
                                <div class="section-header">
                                    <h3>日志过滤</h3>
                                    <div class="filter-controls">
                                        <select id="taskLogFilter" onchange="filterTaskLogs()">
                                            <option value="">所有任务</option>
                                        </select>
                                        <select id="statusLogFilter" onchange="filterTaskLogs()">
                                            <option value="">所有状态</option>
                                            <option value="completed">已完成</option>
                                            <option value="running">运行中</option>
                                            <option value="failed">失败</option>
                                            <option value="idle">空闲</option>
                                        </select>
                                        <input type="date" id="dateLogFilter" onchange="filterTaskLogs()">
                                        <button onclick="loadTaskLogs()" class="btn-primary">🔄 刷新</button>
                                    </div>
                                </div>
                                <div id="task-logs-list" class="config-list"></div>
                            </div>
                        </div>
                        
                        <!-- 编辑数据源页面 -->
                        <div id="edit-source" style="display: none;">
                            <div class="page-header">
                                <h2>✏️ 编辑数据源</h2>
                                <p>修改数据源配置信息</p>
                            </div>
                            
                            <div class="config-section">
                                <form id="source-edit-form" class="edit-form">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label for="source-name">数据源名称 *</label>
                                            <input type="text" id="source-name" name="name" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="source-description">描述</label>
                                            <input type="text" id="source-description" name="description">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="source-endpoint">端点URL *</label>
                                            <input type="url" id="source-endpoint" name="endpoint" required placeholder="https://oss-cn-shanghai.aliyuncs.com">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="source-region">区域 *</label>
                                            <input type="text" id="source-region" name="region" required placeholder="cn-shanghai">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="source-access-key">Access Key *</label>
                                            <input type="text" id="source-access-key" name="access_key" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="source-secret-key">Secret Key *</label>
                                            <input type="password" id="source-secret-key" name="secret_key" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="source-bucket">存储桶名称 *</label>
                                            <div style="display: flex; gap: 10px;">
                                                <input type="text" id="source-bucket" name="bucket" required style="flex: 1;">
                                                <button type="button" onclick="listEditSourceBuckets()" class="btn-secondary" style="white-space: nowrap;">📋 列出桶</button>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group full-width">
                                            <div class="form-actions">
                                                <button type="button" onclick="testSourceConnection()" class="btn-secondary">🔗 测试连接</button>
                                                <button type="button" onclick="cancelSourceEdit()" class="btn-cancel">取消</button>
                                                <button type="submit" class="btn-primary">💾 保存</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- 编辑目标页面 -->
                        <div id="edit-target" style="display: none;">
                            <div class="page-header">
                                <h2>✏️ 编辑目标</h2>
                                <p>修改同步目标配置信息</p>
                            </div>
                            
                            <div class="config-section">
                                <form id="target-edit-form" class="edit-form">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label for="target-name">目标名称 *</label>
                                            <input type="text" id="target-name" name="name" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="target-description">描述</label>
                                            <input type="text" id="target-description" name="description">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="target-endpoint">端点URL *</label>
                                            <input type="url" id="target-endpoint" name="endpoint" required placeholder="https://cos.ap-beijing.myqcloud.com">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="target-region">区域 *</label>
                                            <input type="text" id="target-region" name="region" required placeholder="ap-beijing">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="target-access-key">Access Key *</label>
                                            <input type="text" id="target-access-key" name="access_key" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="target-secret-key">Secret Key *</label>
                                            <input type="password" id="target-secret-key" name="secret_key" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="target-bucket">存储桶名称 *</label>
                                            <div style="display: flex; gap: 10px;">
                                                <input type="text" id="target-bucket" name="bucket" required style="flex: 1;">
                                                <button type="button" onclick="listEditTargetBuckets()" class="btn-secondary" style="white-space: nowrap;">📋 列出桶</button>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group full-width">
                                            <div class="form-actions">
                                                <button type="button" onclick="testTargetConnection()" class="btn-secondary">🔗 测试连接</button>
                                                <button type="button" onclick="cancelTargetEdit()" class="btn-cancel">取消</button>
                                                <button type="submit" class="btn-primary">💾 保存</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- 查看任务配置页面 -->
                        <div id="view-task" style="display: none;">
                            <div class="page-header">
                                <h2>👁️ 查看任务配置</h2>
                                <p>查看任务的详细配置信息</p>
                            </div>
                            
                            <div class="config-section">
                                <div class="view-form">
                                    <div id="task-view-content">
                                        <!-- 任务配置内容将通过JavaScript动态填充 -->
                                    </div>
                                    
                                    <div class="form-actions">
                                        <button type="button" onclick="editCurrentTask()" class="btn-edit">✏️ 编辑</button>
                                        <button type="button" onclick="runCurrentTask()" class="btn-run">▶️ 运行</button>
                                        <button type="button" onclick="showTaskConfig()" class="btn-cancel">返回</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 编辑任务页面 -->
                        <div id="edit-task" style="display: none;">
                            <div class="page-header">
                                <h2>✏️ 编辑任务</h2>
                                <p>修改同步任务配置信息</p>
                            </div>
                            
                            <div class="config-section">
                                <form id="task-edit-form" class="edit-form">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label for="task-name">任务名称 *</label>
                                            <input type="text" id="task-name" name="name" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-description">任务描述</label>
                                            <input type="text" id="task-description" name="description">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-source">数据源 *</label>
                                            <select id="task-source" name="source_id" required>
                                                <option value="">请选择数据源</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-target">目标 *</label>
                                            <select id="task-target" name="target_id" required>
                                                <option value="">请选择目标</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-prefix">文件前缀</label>
                                            <input type="text" id="task-prefix" name="prefix" placeholder="可选，如: data/">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-schedule-type">调度类型</label>
                                            <select id="task-schedule-type" name="schedule_type">
                                                <option value="manual">手动执行</option>
                                                <option value="minutely">每分钟执行</option>
                                                <option value="hourly">每小时执行</option>
                                                <option value="daily">每日执行</option>
                                                <option value="weekly">每周执行</option>
                                                <option value="interval">自定义间隔</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-schedule-time">执行时间</label>
                                            <input type="time" id="task-schedule-time" name="schedule_time" value="02:00">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-schedule-interval">间隔</label>
                                            <input type="number" id="task-schedule-interval" name="schedule_interval" value="1" min="1">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-sync-mode-select">同步模式</label>
                                            <select id="task-sync-mode-select" name="sync_mode">
                                                <option value="incremental">增量同步</option>
                                                <option value="full">完全同步</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-max-workers">并发线程数</label>
                                            <input type="number" id="task-max-workers" name="max_workers" value="20" min="1" max="100">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-file-filter">文件过滤</label>
                                            <input type="text" id="task-file-filter" name="file_filter" placeholder="如: *.jpg,*.png">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-exclude-filter">排除文件</label>
                                            <input type="text" id="task-exclude-filter" name="exclude_filter" placeholder="如: *.tmp,*.log">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-bandwidth-limit">带宽限制(MB/s)</label>
                                            <input type="number" id="task-bandwidth-limit" name="bandwidth_limit" value="0" min="0">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-retry-times">重试次数</label>
                                            <input type="number" id="task-retry-times" name="retry_times" value="5" min="0" max="10">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-retry-delay">重试间隔(秒)</label>
                                            <input type="number" id="task-retry-delay" name="retry_delay" value="3" min="1" max="60">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-chunk-threshold">大文件切片阈值(MB)</label>
                                            <input type="number" id="task-chunk-threshold" name="chunk_threshold" value="100" min="1">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-chunk-size">切片大小(MB)</label>
                                            <input type="number" id="task-chunk-size" name="chunk_size" value="10" min="1">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-verify-integrity">完整性验证</label>
                                            <select id="task-verify-integrity" name="verify_integrity">
                                                <option value="true">启用</option>
                                                <option value="false">禁用</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-delete-extra">删除多余文件</label>
                                            <select id="task-delete-extra" name="delete_extra">
                                                <option value="false">否</option>
                                                <option value="true">是</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="task-enabled">启用状态</label>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="task-enabled" name="enabled">
                                                <label for="task-enabled">启用此任务</label>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group full-width">
                                            <div class="form-actions">
                                                <button type="button" onclick="cancelTaskEdit()" class="btn-cancel">取消</button>
                                                <button type="submit" class="btn-primary">💾 保存</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- 新建数据源页面 -->
                        <div id="add-source" style="display: none;">
                            <div class="page-header">
                                <h2>➕ 新建数据源</h2>
                                <p>添加新的数据源配置</p>
                            </div>

                            <div class="config-section">
                                <form id="source-add-form" class="edit-form">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label for="add-source-name">数据源名称 *</label>
                                            <input type="text" id="add-source-name" name="name" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="add-source-description">描述</label>
                                            <input type="text" id="add-source-description" name="description">
                                        </div>

                                        <div class="form-group">
                                            <label for="add-source-type">存储类型 *</label>
                                            <select id="add-source-type" name="storage_type" required onchange="updateSourceConfigFields()">
                                                <option value="">请选择存储类型</option>
                                                <option value="s3">S3对象存储</option>
                                                <option value="sftp">SFTP</option>
                                                <option value="smb">SMB/CIFS</option>
                                                <option value="ftp">FTP/FTPS</option>
                                                <option value="local">本地文件系统</option>
                                            </select>
                                        </div>

                                        <!-- 动态配置字段容器 -->
                                        <div id="add-source-config-fields" class="form-group full-width">
                                            <p style="color: #666; text-align: center; padding: 20px;">请先选择存储类型</p>
                                        </div>
                                        
                                        <div class="form-group full-width">
                                            <div class="form-actions">
                                                <button type="button" onclick="testAddSourceConnection()" class="btn-secondary">🔗 测试连接</button>
                                                <button type="button" onclick="cancelSourceAdd()" class="btn-cancel">取消</button>
                                                <button type="submit" class="btn-primary">💾 保存</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- 新建目标页面 -->
                        <div id="add-target" style="display: none;">
                            <div class="page-header">
                                <h2>➕ 新建目标</h2>
                                <p>添加新的同步目标配置</p>
                            </div>

                            <div class="config-section">
                                <form id="target-add-form" class="edit-form">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label for="add-target-name">目标名称 *</label>
                                            <input type="text" id="add-target-name" name="name" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="add-target-description">描述</label>
                                            <input type="text" id="add-target-description" name="description">
                                        </div>

                                        <div class="form-group">
                                            <label for="add-target-type">存储类型 *</label>
                                            <select id="add-target-type" name="storage_type" required onchange="updateTargetConfigFields()">
                                                <option value="">请选择存储类型</option>
                                                <option value="s3">S3对象存储</option>
                                                <option value="sftp">SFTP</option>
                                                <option value="smb">SMB/CIFS</option>
                                                <option value="ftp">FTP/FTPS</option>
                                                <option value="local">本地文件系统</option>
                                            </select>
                                        </div>

                                        <!-- 动态配置字段容器 -->
                                        <div id="add-target-config-fields" class="form-group full-width">
                                            <p style="color: #666; text-align: center; padding: 20px;">请先选择存储类型</p>
                                        </div>
                                        
                                        <div class="form-group full-width">
                                            <div class="form-actions">
                                                <button type="button" onclick="testAddTargetConnection()" class="btn-secondary">🔗 测试连接</button>
                                                <button type="button" onclick="cancelTargetAdd()" class="btn-cancel">取消</button>
                                                <button type="submit" class="btn-primary">💾 保存</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- 新建任务页面 -->
                        <div id="add-task" style="display: none;">
                            <div class="page-header">
                                <h2>➕ 新建任务</h2>
                                <p>创建新的同步任务</p>
                            </div>
                            
                            <div class="config-section">
                                <form id="task-add-form" class="edit-form">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label for="add-task-name">任务名称 *</label>
                                            <input type="text" id="add-task-name" name="name" required>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-description">任务描述</label>
                                            <input type="text" id="add-task-description" name="description">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-source">数据源 *</label>
                                            <select id="add-task-source" name="source_id" required>
                                                <option value="">请选择数据源</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-target">目标 *</label>
                                            <select id="add-task-target" name="target_id" required>
                                                <option value="">请选择目标</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-prefix">文件前缀</label>
                                            <input type="text" id="add-task-prefix" name="prefix" placeholder="可选，如: data/">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-schedule-type">调度类型</label>
                                            <select id="add-task-schedule-type" name="schedule_type">
                                                <option value="manual">手动执行</option>
                                                <option value="minutely">每分钟执行</option>
                                                <option value="hourly">每小时执行</option>
                                                <option value="daily">每日执行</option>
                                                <option value="weekly">每周执行</option>
                                                <option value="interval">自定义间隔</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-schedule-time">执行时间</label>
                                            <input type="time" id="add-task-schedule-time" name="schedule_time" value="02:00">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-schedule-interval">间隔</label>
                                            <input type="number" id="add-task-schedule-interval" name="schedule_interval" value="1" min="1">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-sync-mode">同步模式</label>
                                            <select id="add-task-sync-mode" name="sync_mode">
                                                <option value="incremental">增量同步</option>
                                                <option value="full">完全同步</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-max-workers">并发线程数</label>
                                            <input type="number" id="add-task-max-workers" name="max_workers" value="20" min="1" max="100">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-file-filter">文件过滤</label>
                                            <input type="text" id="add-task-file-filter" name="file_filter" placeholder="如: *.jpg,*.png">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-exclude-filter">排除文件</label>
                                            <input type="text" id="add-task-exclude-filter" name="exclude_filter" placeholder="如: *.tmp,*.log">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-bandwidth-limit">带宽限制(MB/s)</label>
                                            <input type="number" id="add-task-bandwidth-limit" name="bandwidth_limit" value="0" min="0">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-retry-times">重试次数</label>
                                            <input type="number" id="add-task-retry-times" name="retry_times" value="5" min="0" max="10">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-retry-delay">重试间隔(秒)</label>
                                            <input type="number" id="add-task-retry-delay" name="retry_delay" value="3" min="1" max="60">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-chunk-threshold">大文件切片阈值(MB)</label>
                                            <input type="number" id="add-task-chunk-threshold" name="chunk_threshold" value="100" min="1">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-chunk-size">切片大小(MB)</label>
                                            <input type="number" id="add-task-chunk-size" name="chunk_size" value="10" min="1">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-verify-integrity">完整性验证</label>
                                            <select id="add-task-verify-integrity" name="verify_integrity">
                                                <option value="true">启用</option>
                                                <option value="false">禁用</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-delete-extra">删除多余文件</label>
                                            <select id="add-task-delete-extra" name="delete_extra">
                                                <option value="false">否</option>
                                                <option value="true">是</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="add-task-enabled">启用状态</label>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="add-task-enabled" name="enabled" checked>
                                                <label for="add-task-enabled">启用此任务</label>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group full-width">
                                            <div class="form-actions">
                                                <button type="button" onclick="cancelTaskAdd()" class="btn-cancel">取消</button>
                                                <button type="submit" class="btn-primary">💾 保存</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- 用户手册页面 -->
                        <div id="user-manual" style="display: none;">
                            <div class="page-header">
                                <h2>📖 用户手册</h2>
                                <p>详细的配置和使用指南</p>
                            </div>
                            
                            <div class="config-section">
                                <div class="manual-toc">
                                    <h3>📋 目录</h3>
                                    <ul>
                                        <li><a href="#manual-overview" onclick="scrollToSection('manual-overview')">1. 系统概述</a></li>
                                        <li><a href="#manual-sources" onclick="scrollToSection('manual-sources')">2. 数据源配置</a></li>
                                        <li><a href="#manual-targets" onclick="scrollToSection('manual-targets')">3. 同步目标配置</a></li>
                                        <li><a href="#manual-tasks" onclick="scrollToSection('manual-tasks')">4. 同步任务配置</a></li>
                                        <li><a href="#manual-scheduling" onclick="scrollToSection('manual-scheduling')">5. 定时调度配置</a></li>
                                        <li><a href="#manual-monitoring" onclick="scrollToSection('manual-monitoring')">6. 监控和日志</a></li>
                                        <li><a href="#manual-troubleshooting" onclick="scrollToSection('manual-troubleshooting')">7. 故障排除</a></li>
                                    </ul>
                                </div>
                                
                                <div class="manual-content">
                                    <div class="manual-section" id="manual-overview">
                                        <h3>1. 系统概述</h3>
                                        <p>Lightrek S3 同步工具是一个企业级的云存储同步解决方案，支持多种S3兼容的存储服务之间的数据同步。</p>
                                        
                                        <h4>主要功能特性</h4>
                                        <ul>
                                            <li>🔄 支持增量同步和完整同步</li>
                                            <li>⏰ 灵活的定时调度机制</li>
                                            <li>🚀 多线程并发传输</li>
                                            <li>🔒 数据完整性验证</li>
                                            <li>📊 实时监控和统计</li>
                                            <li>📝 详细的执行日志</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="manual-section" id="manual-sources">
                                        <h3>2. 数据源配置</h3>
                                        <p>数据源是同步任务的源头，需要配置S3兼容存储的连接信息。系统支持所有兼容S3 API的对象存储服务。</p>
                                        
                                        <h4>配置参数详解</h4>
                                        <div class="config-table-wrapper">
                                            <table class="config-table">
                                                <thead>
                                                    <tr>
                                                        <th>参数名称</th>
                                                        <th>必填</th>
                                                        <th>数据类型</th>
                                                        <th>说明</th>
                                                        <th>示例</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>名称</td>
                                                        <td>是</td>
                                                        <td>字符串</td>
                                                        <td>数据源的显示名称，用于在界面中识别和选择。建议使用有意义的名称，包含服务商和环境信息</td>
                                                        <td>阿里云OSS-生产环境<br>腾讯云COS-测试环境<br>AWS S3-备份存储</td>
                                                    </tr>
                                                    <tr>
                                                        <td>描述</td>
                                                        <td>否</td>
                                                        <td>字符串</td>
                                                        <td>数据源的详细描述信息，可包含用途、负责人、注意事项等。支持多行文本</td>
                                                        <td>生产环境的主要数据存储<br>包含用户上传的图片和文档<br>负责人：张三</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Access Key</td>
                                                        <td>是</td>
                                                        <td>字符串</td>
                                                        <td>访问密钥ID，用于身份验证。不同云服务商的名称可能不同：阿里云称为AccessKey ID，腾讯云称为SecretId，AWS称为Access Key ID</td>
                                                        <td>LTAI4G***********<br>AKIDxxxxxxxxxxxxxxxx<br>AKIAIOSFODNN7EXAMPLE</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Secret Key</td>
                                                        <td>是</td>
                                                        <td>字符串</td>
                                                        <td>访问密钥Secret，用于身份验证。请妥善保管，不要泄露。不同云服务商的名称：阿里云称为AccessKey Secret，腾讯云称为SecretKey，AWS称为Secret Access Key</td>
                                                        <td>************************<br>（实际为32-40位字符串）</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Endpoint</td>
                                                        <td>是</td>
                                                        <td>URL</td>
                                                        <td>S3服务的访问端点URL，必须包含协议（https://）。不同区域和服务商的格式不同，请参考官方文档</td>
                                                        <td>https://oss-cn-hangzhou.aliyuncs.com<br>https://cos.ap-beijing.myqcloud.com<br>https://s3.us-west-2.amazonaws.com</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Region</td>
                                                        <td>是</td>
                                                        <td>字符串</td>
                                                        <td>存储区域标识符，必须与Endpoint中的区域保持一致。错误的区域配置会导致连接失败</td>
                                                        <td>cn-hangzhou（阿里云）<br>ap-beijing（腾讯云）<br>us-west-2（AWS）</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Bucket</td>
                                                        <td>是</td>
                                                        <td>字符串</td>
                                                        <td>存储桶名称，必须是已存在的桶。建议使用专门的同步桶，避免影响生产数据</td>
                                                        <td>my-data-bucket<br>backup-storage-2024<br>sync-test-bucket</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        
                                        <h4>常见存储服务配置示例</h4>
                                        <div class="example-configs">
                                            <div class="config-example">
                                                <h5>阿里云OSS</h5>
                                                <div class="code-block">
                                                    Endpoint: https://oss-cn-shanghai.aliyuncs.com<br>
                                                    Region: cn-shanghai<br>
                                                    Access Key: LTAI4G***********<br>
                                                    Secret Key: ************************
                                                </div>
                                            </div>
                                            
                                            <div class="config-example">
                                                <h5>腾讯云COS</h5>
                                                <div class="code-block">
                                                    Endpoint: https://cos.ap-beijing.myqcloud.com<br>
                                                    Region: ap-beijing<br>
                                                    Access Key: AKIDxxxxxxxxxxxxxxxx<br>
                                                    Secret Key: ************************
                                                </div>
                                            </div>
                                            
                                            <div class="config-example">
                                                <h5>AWS S3</h5>
                                                <div class="code-block">
                                                    Endpoint: https://s3.us-west-2.amazonaws.com<br>
                                                    Region: us-west-2<br>
                                                    Access Key: AKIAXXXXXXXXXXXXXXXX<br>
                                                    Secret Key: ************************
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="manual-section" id="manual-targets">
                                        <h3>3. 同步目标配置</h3>
                                        <p>同步目标是数据的目的地，配置方式与数据源类似。</p>
                                        
                                        <h4>常见存储服务配置示例</h4>
                                        <div class="example-configs">
                                            <div class="config-example">
                                                <h5>阿里云OSS</h5>
                                                <div class="code-block">
                                                    Endpoint: https://oss-cn-shanghai.aliyuncs.com<br>
                                                    Region: cn-shanghai<br>
                                                    Access Key: LTAI4G***********<br>
                                                    Secret Key: ************************
                                                </div>
                                            </div>
                                            
                                            <div class="config-example">
                                                <h5>腾讯云COS</h5>
                                                <div class="code-block">
                                                    Endpoint: https://cos.ap-beijing.myqcloud.com<br>
                                                    Region: ap-beijing<br>
                                                    Access Key: AKIDxxxxxxxxxxxxxxxx<br>
                                                    Secret Key: ************************
                                                </div>
                                            </div>
                                            
                                            <div class="config-example">
                                                <h5>AWS S3</h5>
                                                <div class="code-block">
                                                    Endpoint: https://s3.us-west-2.amazonaws.com<br>
                                                    Region: us-west-2<br>
                                                    Access Key: AKIAXXXXXXXXXXXXXXXX<br>
                                                    Secret Key: ************************
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="manual-section" id="manual-tasks">
                                        <h3>4. 同步任务配置</h3>
                                        <p>同步任务是系统的核心功能，定义了具体的同步规则、性能参数和调度策略。系统提供60+个可配置参数，涵盖基础信息、同步策略、性能优化、调度设置、监控告警等全方位配置选项。</p>
                                        
                                        <h4>基础配置参数</h4>
                                        <div class="config-table-wrapper">
                                            <table class="config-table">
                                                <thead>
                                                    <tr>
                                                        <th>参数名称</th>
                                                        <th>必填</th>
                                                        <th>数据类型</th>
                                                        <th>说明</th>
                                                        <th>示例</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>任务名称</td>
                                                        <td>是</td>
                                                        <td>字符串</td>
                                                        <td>任务的显示名称，建议使用有意义的名称便于管理和识别</td>
                                                        <td>日志文件同步<br>数据库备份任务<br>图片资源同步</td>
                                                    </tr>
                                                    <tr>
                                                        <td>任务描述</td>
                                                        <td>否</td>
                                                        <td>字符串</td>
                                                        <td>任务的详细描述，可包含同步目的、注意事项、负责人等信息</td>
                                                        <td>每日备份生产环境日志到灾备中心<br>负责人：运维团队</td>
                                                    </tr>
                                                    <tr>
                                                        <td>数据源</td>
                                                        <td>是</td>
                                                        <td>选择项</td>
                                                        <td>选择已配置的数据源，作为同步的源头</td>
                                                        <td>阿里云OSS-生产环境</td>
                                                    </tr>
                                                    <tr>
                                                        <td>同步目标</td>
                                                        <td>是</td>
                                                        <td>选择项</td>
                                                        <td>选择已配置的同步目标，作为数据的目的地</td>
                                                        <td>备份存储-腾讯云</td>
                                                    </tr>
                                                    <tr>
                                                        <td>文件前缀</td>
                                                        <td>否</td>
                                                        <td>字符串</td>
                                                        <td>只同步指定前缀的文件，支持路径过滤。为空表示同步所有文件</td>
                                                        <td>logs/2024/<br>images/product/<br>data/backup/</td>
                                                    </tr>
                                                    <tr>
                                                        <td>同步模式</td>
                                                        <td>是</td>
                                                        <td>选择项</td>
                                                        <td>增量同步：只同步新增和修改的文件；完全同步：同步所有文件</td>
                                                        <td>incremental（增量）<br>full（完全）</td>
                                                    </tr>
                                                    <tr>
                                                        <td>调度类型</td>
                                                        <td>是</td>
                                                        <td>选择项</td>
                                                        <td>manual：手动执行；daily：每日执行；hourly：每小时执行；weekly：每周执行</td>
                                                        <td>daily<br>hourly<br>manual</td>
                                                    </tr>
                                                    <tr>
                                                        <td>执行时间</td>
                                                        <td>否</td>
                                                        <td>时间</td>
                                                        <td>定时执行的具体时间（24小时制），仅在非手动模式下有效</td>
                                                        <td>02:00<br>14:30<br>23:45</td>
                                                    </tr>
                                                    <tr>
                                                        <td>执行间隔</td>
                                                        <td>否</td>
                                                        <td>整数</td>
                                                        <td>执行间隔（小时），仅在hourly模式下有效。范围：1-24小时</td>
                                                        <td>1<br>6<br>12</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        
                                        <h4>性能优化参数</h4>
                                        <div class="config-table-wrapper">
                                            <table class="config-table">
                                                <thead>
                                                    <tr>
                                                        <th>参数名称</th>
                                                        <th>必填</th>
                                                        <th>数据类型</th>
                                                        <th>说明</th>
                                                        <th>示例</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>并发线程数</td>
                                                        <td>否</td>
                                                        <td>整数</td>
                                                        <td>同时处理的文件数量，影响同步速度。建议根据网络带宽和服务器性能调整</td>
                                                        <td>20（默认）<br>50（高性能）<br>10（低负载）</td>
                                                    </tr>
                                                    <tr>
                                                        <td>带宽限制</td>
                                                        <td>否</td>
                                                        <td>整数</td>
                                                        <td>限制传输带宽（MB/s），0表示不限制。用于避免影响其他业务</td>
                                                        <td>0（不限制）<br>100<br>50</td>
                                                    </tr>
                                                    <tr>
                                                        <td>重试次数</td>
                                                        <td>否</td>
                                                        <td>整数</td>
                                                        <td>传输失败时的重试次数，增加可靠性但会延长执行时间</td>
                                                        <td>5（默认）<br>10<br>3</td>
                                                    </tr>
                                                    <tr>
                                                        <td>大文件阈值</td>
                                                        <td>否</td>
                                                        <td>整数</td>
                                                        <td>超过此大小（MB）的文件将使用分片传输，提高大文件传输的稳定性</td>
                                                        <td>100（默认）<br>500<br>50</td>
                                                    </tr>
                                                    <tr>
                                                        <td>分片大小</td>
                                                        <td>否</td>
                                                        <td>整数</td>
                                                        <td>分片传输时每个分片的大小（MB），影响传输效率和内存使用</td>
                                                        <td>10（默认）<br>20<br>5</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        
                                        <h4>过滤和验证参数</h4>
                                        <div class="config-table-wrapper">
                                            <table class="config-table">
                                                <thead>
                                                    <tr>
                                                        <th>参数名称</th>
                                                        <th>必填</th>
                                                        <th>数据类型</th>
                                                        <th>说明</th>
                                                        <th>示例</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>文件过滤</td>
                                                        <td>否</td>
                                                        <td>字符串</td>
                                                        <td>包含指定模式的文件才会被同步，支持通配符。多个模式用逗号分隔</td>
                                                        <td>*.jpg,*.png<br>*.log<br>data_*.csv</td>
                                                    </tr>
                                                    <tr>
                                                        <td>排除过滤</td>
                                                        <td>否</td>
                                                        <td>字符串</td>
                                                        <td>包含指定模式的文件将被排除，不会同步。多个模式用逗号分隔</td>
                                                        <td>*.tmp,*.bak<br>temp/*<br>*.cache</td>
                                                    </tr>
                                                    <tr>
                                                        <td>完整性验证</td>
                                                        <td>否</td>
                                                        <td>布尔值</td>
                                                        <td>是否验证文件传输完整性，建议开启以确保数据准确性</td>
                                                        <td>true（开启）<br>false（关闭）</td>
                                                    </tr>
                                                    <tr>
                                                        <td>删除多余文件</td>
                                                        <td>否</td>
                                                        <td>布尔值</td>
                                                        <td>是否删除目标中存在但源中不存在的文件，谨慎使用</td>
                                                        <td>false（默认）<br>true（镜像同步）</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    
                                    <div class="manual-section" id="manual-scheduling">
                                        <h3>5. 定时调度配置</h3>
                                        <p>系统支持使用Cron表达式进行灵活的定时调度。</p>
                                        
                                        <h4>Cron表达式格式</h4>
                                        <div class="code-block">
                                            格式: 分 时 日 月 周<br>
                                            示例:<br>
                                            0 2 * * * - 每天凌晨2点执行<br>
                                            0 */6 * * * - 每6小时执行一次<br>
                                            0 0 1 * * - 每月1号执行<br>
                                            0 0 * * 0 - 每周日执行
                                        </div>
                                    </div>
                                    
                                    <div class="manual-section" id="manual-monitoring">
                                        <h3>6. 监控和日志</h3>
                                        <p>系统提供完整的监控和日志功能，帮助您了解同步状态。</p>
                                        
                                        <h4>监控功能</h4>
                                        <ul>
                                            <li>📊 实时统计数据</li>
                                            <li>📈 执行趋势图表</li>
                                            <li>🕒 最近执行记录</li>
                                            <li>📄 详细执行日志</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="manual-section" id="manual-troubleshooting">
                                        <h3>7. 故障排除</h3>
                                        <p>常见问题和解决方案。</p>
                                        
                                        <h4>连接问题</h4>
                                        <div class="troubleshooting-item">
                                            <strong>问题：</strong>连接测试失败，提示404错误<br>
                                            <strong>解决：</strong>检查Endpoint URL格式，确保包含正确的协议和域名
                                        </div>
                                        
                                        <div class="troubleshooting-item">
                                            <strong>问题：</strong>认证失败<br>
                                            <strong>解决：</strong>验证Access Key和Secret Key是否正确，检查权限设置
                                        </div>
                                        
                                        <div class="troubleshooting-item">
                                            <strong>问题：</strong>同步速度慢<br>
                                            <strong>解决：</strong>检查网络连接，考虑调整并发线程数
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                    
                    <script>
                        const statsData = {js_stats};
                        
                        // 导航功能
                        function toggleConfigGroup() {{
                            const submenu = document.getElementById('config-submenu');
                            const arrow = document.querySelector('.nav-arrow');
                            
                            if (submenu.classList.contains('expanded')) {{
                                submenu.classList.remove('expanded');
                                arrow.classList.remove('expanded');
                            }} else {{
                                submenu.classList.add('expanded');
                                arrow.classList.add('expanded');
                            }}
                        }}
                        
                        function showSourceConfig() {{
                            showPage('source-config');
                            loadSources();
                            setActiveSubNav('showSourceConfig');
                        }}
                        
                        function showTargetConfig() {{
                            showPage('target-config');
                            loadTargets();
                            setActiveSubNav('showTargetConfig');
                        }}
                        
                        function showTaskConfig() {{
                            showPage('task-config');
                            loadTasks();
                            setActiveSubNav('showTaskConfig');
                        }}
                        
                        function showOptimizationConfig() {{
                            showPage('optimization-config');
                            loadOptimizationConfig();
                            setActiveSubNav('showOptimizationConfig');
                        }}
                        
                        function showTaskLogs() {{
                            showPage('task-logs');
                            loadTaskLogs();
                            setActiveNav('showTaskLogs');
                        }}
                        
                        function showUserManual() {{
                            showPage('user-manual');
                            setActiveNav('showUserManual');
                        }}
                        
                        function showPage(pageId) {{
                            // 隐藏所有页面
                            const pages = ['dashboard', 'source-config', 'target-config', 'task-config', 'optimization-config', 'task-logs', 'user-manual', 'edit-source', 'edit-target', 'edit-task', 'view-task', 'add-source', 'add-target', 'add-task'];
                            pages.forEach(id => {{
                                const element = document.getElementById(id);
                                if (element) element.style.display = 'none';
                            }});
                            
                            // 显示目标页面
                            const targetPage = document.getElementById(pageId);
                            if (targetPage) {{
                                targetPage.style.display = 'block';
                            }}
                        }}
                        
                        function setActiveSubNav(activeFunction) {{
                            // 移除所有子导航的active类
                            document.querySelectorAll('.nav-sublink').forEach(link => {{
                                link.classList.remove('active');
                            }});
                            
                            // 根据函数名设置对应的active类
                            const functionMap = {{
                                'showSourceConfig': 0,
                                'showTargetConfig': 1,
                                'showTaskConfig': 2,
                                'showOptimizationConfig': 3
                            }};
                            
                            const index = functionMap[activeFunction];
                            if (index !== undefined) {{
                                const sublinks = document.querySelectorAll('.nav-sublink');
                                if (sublinks[index]) {{
                                    sublinks[index].classList.add('active');
                                }}
                            }}
                        }}
                        
                        function setActiveNav(functionName) {{
                            // 清除所有导航的active状态
                            document.querySelectorAll('.nav-link').forEach(link => {{
                                link.classList.remove('active');
                            }});
                            
                            // 根据函数名设置对应的导航为active
                            const navMap = {{
                                'showTaskLogs': '任务日志',
                                'showUserManual': '用户手册'
                            }};
                            
                            const targetText = navMap[functionName];
                            if (targetText) {{
                                document.querySelectorAll('.nav-link').forEach(link => {{
                                    if (link.textContent.trim().includes(targetText)) {{
                                        link.classList.add('active');
                                    }}
                                }});
                            }}
                        }}
                        
                        function loadSources() {{
                            fetch('/api/sources').then(r => r.json())
                                .then(sources => renderSources(sources))
                                .catch(console.error);
                        }}
                        
                        function loadTargets() {{
                            fetch('/api/targets').then(r => r.json())
                                .then(targets => renderTargets(targets))
                                .catch(console.error);
                        }}
                        
                        function loadTasks() {{
                            fetch('/api/tasks').then(r => r.json())
                                .then(tasks => renderTasks(tasks))
                                .catch(console.error);
                        }}
                        
                        function loadOptimizationConfig() {{
                            fetch('/api/optimization-config').then(r => r.json())
                                .then(config => {{
                                    populateOptimizationForm(config);
                                }})
                                .catch(console.error);
                        }}
                        
                        function populateOptimizationForm(config) {{
                            document.getElementById('optimization-enabled').checked = config.enabled || false;
                            document.getElementById('parallel-workers').value = config.parallel_scanning?.max_workers || 5;
                            document.getElementById('cache-validity').value = config.cache_management?.cache_validity_minutes || 60;
                            document.getElementById('cache-retention').value = config.cache_management?.max_cache_age_days || 7;

                            document.getElementById('batch-size').value = config.streaming_processing?.batch_size || 5000;
                        }}
                        
                        function saveOptimizationConfig() {{
                            const config = {{
                                enabled: document.getElementById('optimization-enabled').checked,
                                parallel_scanning: {{
                                    enabled: true,
                                    max_workers: parseInt(document.getElementById('parallel-workers').value)
                                }},
                                cache_management: {{
                                    enabled: true,
                                    cache_validity_minutes: parseInt(document.getElementById('cache-validity').value),
                                    max_cache_age_days: parseInt(document.getElementById('cache-retention').value),
                                    cache_database: "lightrek_cache.db"
                                }},

                                streaming_processing: {{
                                    enabled: true,
                                    batch_size: parseInt(document.getElementById('batch-size').value)
                                }}
                            }};
                        
                            fetch('/api/optimization-config', {{
                                method: 'POST',
                                headers: {{'Content-Type': 'application/json'}},
                                body: JSON.stringify(config)
                            }})
                            .then(r => r.json())
                            .then(result => {{
                                if (result.success) {{
                                    showNotification('配置保存成功！重启程序后生效。', 'success');
                                }} else {{
                                    showNotification('配置保存失败：' + result.message, 'error');
                                }}
                            }})
                            .catch(err => {{
                                showNotification('保存失败：' + err.message, 'error');
                                console.error(err);
                            }});
                        }}
                        
                        function applyPresetConfig(preset) {{
                            const presets = {{
                                'high_performance': {{
                                    enabled: true,
                                    parallel_scanning: {{ enabled: true, max_workers: 8 }},
                                    cache_management: {{ enabled: true, cache_validity_minutes: 120, max_cache_age_days: 14 }},

                                    streaming_processing: {{ enabled: true, batch_size: 10000 }}
                                }},
                                'balanced': {{
                                    enabled: true,
                                    parallel_scanning: {{ enabled: true, max_workers: 5 }},
                                    cache_management: {{ enabled: true, cache_validity_minutes: 60, max_cache_age_days: 7 }},

                                    streaming_processing: {{ enabled: true, batch_size: 5000 }}
                                }},
                                'compatible': {{
                                    enabled: true,
                                    parallel_scanning: {{ enabled: true, max_workers: 2 }},
                                    cache_management: {{ enabled: false, cache_validity_minutes: 30, max_cache_age_days: 3 }},

                                    streaming_processing: {{ enabled: true, batch_size: 2000 }}
                                }}
                            }};
                        
                            if (presets[preset]) {{
                                populateOptimizationForm(presets[preset]);
                                showNotification(`已应用${{preset === 'high_performance' ? '高性能' : preset === 'balanced' ? '平衡' : '兼容'}}配置`, 'info');
                            }}
                        }}
                        
                        function resetOptimizationConfig() {{
                            if (confirm('确定要重置为默认配置吗？')) {{
                                applyPresetConfig('balanced');
                            }}
                        }}
                        
                        function showNotification(message, type = 'info') {{
                            // 创建通知元素
                            const notification = document.createElement('div');
                            notification.className = `notification notification-${{type}}`;
                            notification.innerHTML = `
                                <span>${{message}}</span>
                                <button onclick="this.parentElement.remove()">×</button>
                            `;
                            
                            // 添加通知样式
                            notification.style.cssText = `
                                position: fixed;
                                top: 20px;
                                right: 20px;
                                background: ${{type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'}};
                                color: white;
                                padding: 12px 20px;
                                border-radius: 6px;
                                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                                z-index: 10000;
                                display: flex;
                                align-items: center;
                                gap: 15px;
                                font-weight: 500;
                                max-width: 400px;
                            `;
                            
                            // 按钮样式
                            const closeBtn = notification.querySelector('button');
                            closeBtn.style.cssText = `
                                background: rgba(255,255,255,0.3);
                                border: none;
                                color: white;
                                width: 25px;
                                height: 25px;
                                border-radius: 50%;
                                cursor: pointer;
                                font-size: 16px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                            `;
                            
                            document.body.appendChild(notification);
                            
                            // 3秒后自动消失
                            setTimeout(() => {{
                                if (notification.parentElement) {{
                                    notification.remove();
                                }}
                            }}, 3000);
                        }}
                        
                        // 任务日志相关函数
                        let allTasksForLogs = [];
                        let allExecutionsForLogs = [];
                        
                        function loadTaskLogs() {{
                            Promise.all([
                                fetch('/api/tasks').then(r => r.json()),
                                fetch('/api/task-executions').then(r => r.json())
                            ]).then(([tasks, executions]) => {{
                                allTasksForLogs = tasks;
                                allExecutionsForLogs = executions;
                                updateTaskLogFilter();
                                renderTaskLogs();
                            }}).catch(console.error);
                        }}
                        
                        function updateTaskLogFilter() {{
                            const filter = document.getElementById('taskLogFilter');
                            if (!filter) return;
                            
                            filter.innerHTML = '<option value="">所有任务</option>';
                            Object.entries(allTasksForLogs).forEach(([id, task]) => {{
                                filter.innerHTML += `<option value="${{id}}">${{task.name}}</option>`;
                            }});
                        }}
                        
                        function filterTaskLogs() {{
                            renderTaskLogs();
                        }}
                        
                        function renderTaskLogs() {{
                            const container = document.getElementById('task-logs-list');
                            if (!container) return;
                            
                            const taskFilter = document.getElementById('taskLogFilter')?.value || '';
                            const statusFilter = document.getElementById('statusLogFilter')?.value || '';
                            const dateFilter = document.getElementById('dateLogFilter')?.value || '';
                            
                            // 按任务分组显示执行记录
                            let html = '';
                            Object.entries(allTasksForLogs).forEach(([taskId, task]) => {{
                                if (taskFilter && taskFilter !== taskId) return;
                                
                                // 获取该任务的执行记录
                                const taskExecutions = allExecutionsForLogs.filter(exec => 
                                    exec.task_id === taskId &&
                                    (!statusFilter || exec.status === statusFilter) &&
                                    (!dateFilter || exec.start_time?.includes(dateFilter))
                                );
                                
                                if (taskExecutions.length === 0 && (statusFilter || dateFilter)) return;
                                
                                html += `
                                    <div class="config-item">
                                        <div class="config-header">
                                            <h4>${{task.name}}</h4>
                                            <div class="config-actions">
                                                <span class="task-execution-count">${{taskExecutions.length}} 条记录</span>
                                            </div>
                                        </div>
                                        <p class="config-desc">${{task.description}}</p>
                                        <div class="execution-list">
                                            ${{taskExecutions.slice(0, 5).map(exec => `
                                                <div class="execution-item" onclick="showExecutionLog('${{exec.id}}')">
                                                    <div class="execution-header">
                                                        <span class="execution-time">${{exec.start_time || '未知时间'}}</span>
                                                        <span class="status-badge status-${{exec.status || 'idle'}}">${{getStatusText(exec.status)}}</span>
                                                    </div>
                                                    <div class="execution-details">
                                                        <span>文件: ${{exec.success_files || 0}}/${{exec.total_files || 0}}</span>
                                                        <span>大小: ${{formatSize(exec.transferred_size || 0)}}</span>
                                                        <span>耗时: ${{exec.duration || '未知'}}</span>
                                                    </div>
                                                </div>
                                            `).join('')}}
                                            ${{taskExecutions.length > 5 ? `<div class="more-executions">还有 ${{taskExecutions.length - 5}} 条记录...</div>` : ''}}
                                        </div>
                                    </div>
                                `;
                            }});
                            
                            if (!html) {{
                                html = '<div class="empty-state"><h3>暂无日志记录</h3><p>没有找到符合条件的执行记录</p></div>';
                            }}
                            
                            container.innerHTML = html;
                        }}
                        
                        function getStatusText(status) {{
                            const statusMap = {{
                                'completed': '已完成',
                                'running': '运行中',
                                'failed': '失败',
                                'idle': '空闲'
                            }};
                            return statusMap[status] || '未知';
                        }}
                        
                        function formatSize(bytes) {{
                            if (bytes === 0) return '0 B';
                            const k = 1024;
                            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
                            const i = Math.floor(Math.log(bytes) / Math.log(k));
                            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                        }}
                        
                        function showExecutionLog(executionId) {{
                            fetch(`/api/task-logs/${{executionId}}`)
                                .then(r => r.json())
                                .then(data => {{
                                    if (data.success) {{
                                        showLogModal(data.logs, `执行记录 #${{executionId}}`);
                                    }} else {{
                                        alert('获取日志失败: ' + data.message);
                                    }}
                                }})
                                .catch(console.error);
                        }}
                        
                        function showLogModal(logs, title) {{
                            // 创建模态框
                            const modal = document.createElement('div');
                            modal.className = 'log-modal';
                            modal.innerHTML = `
                                <div class="log-modal-content">
                                    <div class="log-modal-header">
                                        <div class="log-modal-title">${{title}}</div>
                                        <button class="close-btn" onclick="closeLogModal()">&times;</button>
                                    </div>
                                    <div class="log-content">
                                        ${{logs.map(log => `
                                            <div class="log-entry log-${{log.level}}">
                                                [${{log.created_at}}] [${{log.level.toUpperCase()}}] ${{log.message}}
                                            </div>
                                        `).join('')}}
                                    </div>
                                </div>
                            `;
                            
                            document.body.appendChild(modal);
                            modal.style.display = 'block';
                        }}
                        
                        function closeLogModal() {{
                            const modal = document.querySelector('.log-modal');
                            if (modal) {{
                                modal.remove();
                            }}
                        }}
                        
                        function showExecutionDetails(executionId) {{
                            if (!executionId) return;
                            
                            // 获取执行详情
                            fetch(`/api/task_logs/${{executionId}}`)
                                .then(response => response.json())
                                .then(data => {{
                                    if (data.logs && data.logs.length > 0) {{
                                        showLogModal(data.logs, `执行记录 #${{executionId}} 详细日志`);
                                    }} else {{
                                        // 如果没有日志，显示基本执行信息
                                        showExecutionSummary(executionId);
                                    }}
                                }})
                                .catch(error => {{
                                    console.error('获取执行详情失败:', error);
                                    alert('获取执行详情失败，请稍后重试');
                                }});
                        }}
                        
                        function showExecutionSummary(executionId) {{
                            // 显示执行摘要信息
                            const modal = document.createElement('div');
                            modal.className = 'log-modal';
                            modal.innerHTML = `
                                <div class="log-modal-content">
                                    <div class="log-modal-header">
                                        <div class="log-modal-title">执行记录 #${{executionId}} 摘要</div>
                                        <button class="close-btn" onclick="closeLogModal()">&times;</button>
                                    </div>
                                    <div class="log-content">
                                        <div class="log-entry">
                                            <p>正在加载执行详情...</p>
                                            <p>执行ID: ${{executionId}}</p>
                                            <p>如需查看详细日志，请前往任务日志页面</p>
                                        </div>
                                    </div>
                                </div>
                            `;
                            
                            document.body.appendChild(modal);
                            modal.style.display = 'block';
                        }}
                        
                        function scrollToSection(sectionId) {{
                            const section = document.getElementById(sectionId);
                            if (section) {{
                                section.scrollIntoView({{ behavior: 'smooth' }});
                            }}
                        }}
                        
                        function renderSources(sources) {{
                            const container = document.getElementById('sources-list');
                            if (!container) return;
                            
                            container.innerHTML = Object.entries(sources).map(([id, source]) => `
                                <div class="config-item">
                                    <div class="config-header">
                                        <h4>${{source.name}}</h4>
                                        <div class="config-actions">
                                            <button onclick="editSource('${{id}}')" class="btn-edit">编辑</button>
                                            <button onclick="deleteSource('${{id}}')" class="btn-delete">删除</button>
                                        </div>
                                    </div>
                                    <p class="config-desc">${{source.description}}</p>
                                    <div class="config-details">
                                        <span>端点: ${{source.endpoint}}</span>
                                        <span>存储桶: ${{source.bucket}}</span>
                                        <span>区域: ${{source.region}}</span>
                                    </div>
                                </div>
                            `).join('');
                        }}
                        
                        function renderTargets(targets) {{
                            const container = document.getElementById('targets-list');
                            if (!container) return;
                            
                            container.innerHTML = Object.entries(targets).map(([id, target]) => `
                                <div class="config-item">
                                    <div class="config-header">
                                        <h4>${{target.name}}</h4>
                                        <div class="config-actions">
                                            <button onclick="editTarget('${{id}}')" class="btn-edit">编辑</button>
                                            <button onclick="deleteTarget('${{id}}')" class="btn-delete">删除</button>
                                        </div>
                                    </div>
                                    <p class="config-desc">${{target.description}}</p>
                                    <div class="config-details">
                                        <span>端点: ${{target.endpoint}}</span>
                                        <span>存储桶: ${{target.bucket}}</span>
                                        <span>区域: ${{target.region}}</span>
                                    </div>
                                </div>
                            `).join('');
                        }}
                        
                        function renderTasks(tasks) {{
                            const container = document.getElementById('tasks-list');
                            if (!container) return;
                            
                            container.innerHTML = Object.entries(tasks).map(([id, task]) => `
                                <div class="config-item">
                                    <div class="config-header">
                                        <h4>${{task.name}}</h4>
                                        <div class="config-actions">
                                            <button onclick="viewTask('${{id}}')" class="btn-view">查看</button>
                                            <button onclick="runTask('${{id}}')" class="btn-run">运行</button>
                                            <button onclick="editTask('${{id}}')" class="btn-edit">编辑</button>
                                            <button onclick="deleteTask('${{id}}')" class="btn-delete">删除</button>
                                        </div>
                                    </div>
                                    <p class="config-desc">${{task.description}}</p>
                                    <div class="config-details">
                                        <span>同步模式: ${{task.sync_mode}}</span>
                                        <span>调度: ${{task.schedule_type}}</span>
                                        <span>状态: ${{task.status || 'idle'}}</span>
                                        <span>启用: ${{task.enabled ? '是' : '否'}}</span>
                                    </div>
                                </div>
                            `).join('');
                        }}
                        
                        // 导航高亮
                        document.addEventListener('DOMContentLoaded', function() {{
                            const navLinks = document.querySelectorAll('.nav-link');
                            navLinks.forEach(link => {{
                                link.addEventListener('click', function(e) {{
                                    if (this.getAttribute('href') === '#') {{
                                        e.preventDefault();
                                    }}
                                    // 移除所有active类
                                    navLinks.forEach(l => l.classList.remove('active'));
                                    // 添加active类到当前链接
                                    this.classList.add('active');
                                }});
                            }});
                        }});
                        
                        function createTaskStatusChart() {{
                            const data = statsData.task_status_chart;
                            const container = document.getElementById('taskStatusChart');
                            
                            let html = '<div style="display: flex; flex-direction: column; gap: 10px;">';
                            for (const [status, count] of Object.entries(data)) {{
                                const percentage = (count / statsData.tasks_count * 100).toFixed(1);
                                const statusMap = {{
                                    'completed': {{ text: '已完成', color: '#28a745' }},
                                    'running': {{ text: '运行中', color: '#17a2b8' }},
                                    'failed': {{ text: '失败', color: '#dc3545' }},
                                    'idle': {{ text: '空闲', color: '#6c757d' }}
                                }};
                                const statusInfo = statusMap[status] || {{ text: '未知', color: '#6c757d' }};
                                
                                html += `
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div style="width: 60px; font-size: 0.9rem; color: #666;">${{statusInfo.text}}</div>
                                        <div style="flex: 1; background: #e0e0e0; height: 20px; border-radius: 10px; overflow: hidden;">
                                            <div style="width: ${{percentage}}%; height: 100%; background: ${{statusInfo.color}}; transition: width 0.3s ease;"></div>
                                        </div>
                                        <div style="width: 40px; font-size: 0.9rem; font-weight: 600; color: #333;">${{count}}</div>
                                    </div>
                                `;
                            }}
                            html += '</div>';
                            container.innerHTML = html;
                        }}
                        
                        function createExecutionTrendChart() {{
                            const data = statsData.execution_trend;
                            const container = document.getElementById('executionTrendChart');
                            
                            const maxValue = Math.max(...Object.values(data));
                            let html = '<div style="display: flex; align-items: end; gap: 8px; height: 150px; padding: 10px 0;">';
                            
                            for (const [date, count] of Object.entries(data)) {{
                                const height = maxValue > 0 ? (count / maxValue * 120) : 0;
                                html += `
                                    <div style="flex: 1; display: flex; flex-direction: column; align-items: center; gap: 5px;">
                                        <div style="font-size: 0.8rem; color: #666; font-weight: 600;">${{count}}</div>
                                        <div style="width: 100%; height: ${{height}}px; background: linear-gradient(to top, #667eea, #764ba2); border-radius: 4px; transition: height 0.3s ease;"></div>
                                        <div style="font-size: 0.7rem; color: #999; transform: rotate(-45deg); white-space: nowrap;">${{date.split('-').slice(1).join('/')}}</div>
                                    </div>
                                `;
                            }}
                            html += '</div>';
                            container.innerHTML = html;
                        }}
                        
                        createTaskStatusChart();
                        createExecutionTrendChart();
                        
                        // 定时更新统计数据（仅在仪表盘页面）
                        setInterval(function() {{
                            // 只在仪表盘页面自动刷新数据
                            const currentPage = document.querySelector('.page-content:not([style*="display: none"])');
                            if (currentPage && currentPage.id === 'dashboard') {{
                                fetch('/api/statistics')
                                    .then(response => response.json())
                                    .then(data => {{
                                        // 更新统计数据而不是刷新整个页面
                                        updateDashboardStats(data);
                                    }})
                                    .catch(error => console.error('刷新数据失败:', error));
                            }}
                        }}, 30000);
                        
                        // 更新仪表盘统计数据的函数
                        function updateDashboardStats(stats) {{
                            // 更新统计卡片数据
                            const statCards = document.querySelectorAll('.stat-value');
                            if (statCards.length > 0 && stats) {{
                                // 这里可以根据需要更新具体的统计数据
                                console.log('统计数据已更新');
                            }}
                        }}
                        
                        // 操作函数
                        let currentEditId = null;
                        
                        function editSource(id) {{
                            currentEditId = id;
                            fetch(`/api/sources`)
                                .then(r => r.json())
                                .then(sources => {{
                                    const source = sources[id];
                                    if (!source) {{
                                        alert('数据源不存在');
                                        return;
                                    }}
                                    
                                    // 填充表单
                                    document.getElementById('source-name').value = source.name || '';
                                    document.getElementById('source-description').value = source.description || '';
                                    document.getElementById('source-endpoint').value = source.endpoint || '';
                                    document.getElementById('source-region').value = source.region || '';
                                    document.getElementById('source-access-key').value = source.access_key || '';
                                    document.getElementById('source-secret-key').value = source.secret_key || '';
                                    document.getElementById('source-bucket').value = source.bucket || '';
                                    
                                    // 显示编辑页面
                                    showPage('edit-source');
                                }})
                                .catch(console.error);
                        }}
                        
                        function deleteSource(id) {{
                            if (confirm('确定要删除这个数据源吗？')) {{
                                fetch(`/api/sources/${{id}}`, {{ method: 'DELETE' }})
                                    .then(r => r.json())
                                    .then(result => {{
                                        if (result.success) {{
                                            loadSources();
                                        }} else {{
                                            alert('删除失败: ' + result.message);
                                        }}
                                    }})
                                    .catch(console.error);
                            }}
                        }}
                        
                        function editTarget(id) {{
                            currentEditId = id;
                            fetch(`/api/targets`)
                                .then(r => r.json())
                                .then(targets => {{
                                    const target = targets[id];
                                    if (!target) {{
                                        alert('目标不存在');
                                        return;
                                    }}
                                    
                                    // 填充表单
                                    document.getElementById('target-name').value = target.name || '';
                                    document.getElementById('target-description').value = target.description || '';
                                    document.getElementById('target-endpoint').value = target.endpoint || '';
                                    document.getElementById('target-region').value = target.region || '';
                                    document.getElementById('target-access-key').value = target.access_key || '';
                                    document.getElementById('target-secret-key').value = target.secret_key || '';
                                    document.getElementById('target-bucket').value = target.bucket || '';
                                    
                                    // 显示编辑页面
                                    showPage('edit-target');
                                }})
                                .catch(console.error);
                        }}
                        
                        function deleteTarget(id) {{
                            if (confirm('确定要删除这个目标吗？')) {{
                                fetch(`/api/targets/${{id}}`, {{ method: 'DELETE' }})
                                    .then(r => r.json())
                                    .then(result => {{
                                        if (result.success) {{
                                            loadTargets();
                                        }} else {{
                                            alert('删除失败: ' + result.message);
                                        }}
                                    }})
                                    .catch(console.error);
                            }}
                        }}
                        
                        function runTask(id) {{
                            if (confirm('确定要运行这个任务吗？')) {{
                                fetch(`/api/tasks/${{id}}/run`, {{ method: 'POST' }})
                                    .then(r => r.json())
                                    .then(result => {{
                                        if (result.success) {{
                                            alert('任务已启动');
                                            loadTasks();
                                        }} else {{
                                            alert('启动失败: ' + result.message);
                                        }}
                                    }})
                                    .catch(console.error);
                            }}
                        }}
                        
                        let currentViewTaskId = null;
                        
                        function viewTask(id) {{
                            currentViewTaskId = id;
                            
                            // 加载任务配置信息
                            Promise.all([
                                fetch('/api/sources').then(r => r.json()),
                                fetch('/api/targets').then(r => r.json()),
                                fetch('/api/tasks').then(r => r.json())
                            ]).then(([sources, targets, tasks]) => {{
                                const task = tasks[id];
                                if (!task) {{
                                    alert('任务不存在');
                                    return;
                                }}
                                
                                // 获取源和目标信息
                                const source = sources[task.source_id] || {{ name: '未知数据源' }};
                                const target = targets[task.target_id] || {{ name: '未知目标' }};
                                
                                // 生成查看页面内容
                                const content = `
                                    <div class="view-grid">
                                        <div class="view-item">
                                            <h4>📋 任务名称</h4>
                                            <div class="value">${{task.name || '未设置'}}</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>📝 任务描述</h4>
                                            <div class="value">${{task.description || '无描述'}}</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>📂 数据源</h4>
                                            <div class="value">${{source.name}}</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>🎯 同步目标</h4>
                                            <div class="value">${{target.name}}</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>📁 文件前缀</h4>
                                            <div class="value">${{task.prefix || '无前缀'}}</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>🔄 同步模式</h4>
                                            <div class="value">${{task.sync_mode === 'incremental' ? '增量同步' : '完全同步'}}</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>⏰ 调度类型</h4>
                                            <div class="value">${{getScheduleTypeText(task.schedule_type)}}</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>🕐 执行时间</h4>
                                            <div class="value">${{task.schedule_time || '未设置'}}</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>⚡ 状态</h4>
                                            <div class="value status ${{task.status || 'idle'}}">${{getStatusText(task.status)}}</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>✅ 启用状态</h4>
                                            <div class="value boolean ${{task.enabled ? 'true' : 'false'}}">${{task.enabled ? '已启用' : '已禁用'}}</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>🔧 并发线程数</h4>
                                            <div class="value">${{task.max_workers || 20}}</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>🔄 重试次数</h4>
                                            <div class="value">${{task.retry_times || 5}}</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>⏱️ 重试间隔</h4>
                                            <div class="value">${{task.retry_delay || 3}} 秒</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>🛡️ 完整性验证</h4>
                                            <div class="value boolean ${{task.verify_integrity ? 'true' : 'false'}}">${{task.verify_integrity ? '已启用' : '已禁用'}}</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>📊 带宽限制</h4>
                                            <div class="value">${{task.bandwidth_limit ? task.bandwidth_limit + ' MB/s' : '无限制'}}</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>🗂️ 切片阈值</h4>
                                            <div class="value">${{task.chunk_threshold || 100}} MB</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>📦 切片大小</h4>
                                            <div class="value">${{task.chunk_size || 10}} MB</div>
                                        </div>
                                        
                                        <div class="view-item">
                                            <h4>🗑️ 删除多余文件</h4>
                                            <div class="value boolean ${{task.delete_extra ? 'true' : 'false'}}">${{task.delete_extra ? '是' : '否'}}</div>
                                        </div>
                                        
                                        <div class="view-item full-width">
                                            <h4>🔍 文件过滤规则</h4>
                                            <div class="value">${{task.file_filter || '无过滤规则'}}</div>
                                        </div>
                                        
                                        <div class="view-item full-width">
                                            <h4>🚫 排除文件规则</h4>
                                            <div class="value">${{task.exclude_filter || '无排除规则'}}</div>
                                        </div>
                                        
                                        ${{task.last_run ? `
                                        <div class="view-item full-width">
                                            <h4>🕒 最后运行时间</h4>
                                            <div class="value">${{task.last_run}}</div>
                                        </div>
                                        ` : ''}}
                                    </div>
                                `;
                                
                                document.getElementById('task-view-content').innerHTML = content;
                                showPage('view-task');
                            }})
                            .catch(console.error);
                        }}
                        
                        function getScheduleTypeText(scheduleType) {{
                            const types = {{
                                'manual': '手动执行',
                                'minutely': '每分钟执行',
                                'hourly': '每小时执行',
                                'daily': '每日执行',
                                'weekly': '每周执行',
                                'interval': '自定义间隔'
                            }};
                            return types[scheduleType] || '未知';
                        }}
                        
                        function getStatusText(status) {{
                            const statuses = {{
                                'running': '运行中',
                                'completed': '已完成',
                                'failed': '失败',
                                'idle': '空闲'
                            }};
                            return statuses[status] || '未知';
                        }}
                        
                        function editCurrentTask() {{
                            if (currentViewTaskId) {{
                                editTask(currentViewTaskId);
                            }}
                        }}
                        
                        function runCurrentTask() {{
                            if (currentViewTaskId) {{
                                runTask(currentViewTaskId);
                            }}
                        }}
                        
                        function editTask(id) {{
                            currentEditId = id;
                            
                            // 加载数据源和目标选项
                            Promise.all([
                                fetch('/api/sources').then(r => r.json()),
                                fetch('/api/targets').then(r => r.json()),
                                fetch('/api/tasks').then(r => r.json())
                            ]).then(([sources, targets, tasks]) => {{
                                const task = tasks[id];
                                if (!task) {{
                                    alert('任务不存在');
                                    return;
                                }}
                                
                                // 填充数据源选项
                                const sourceSelect = document.getElementById('task-source');
                                sourceSelect.innerHTML = '<option value="">请选择数据源</option>';
                                Object.entries(sources).forEach(([sourceId, source]) => {{
                                    const option = document.createElement('option');
                                    option.value = sourceId;
                                    option.textContent = source.name;
                                    if (sourceId === task.source_id) option.selected = true;
                                    sourceSelect.appendChild(option);
                                }});
                                
                                // 填充目标选项
                                const targetSelect = document.getElementById('task-target');
                                targetSelect.innerHTML = '<option value="">请选择同步目标</option>';
                                Object.entries(targets).forEach(([targetId, target]) => {{
                                    const option = document.createElement('option');
                                    option.value = targetId;
                                    option.textContent = target.name;
                                    if (targetId === task.target_id) option.selected = true;
                                    targetSelect.appendChild(option);
                                }});
                                
                                // 填充表单
                                document.getElementById('task-name').value = task.name || '';
                                document.getElementById('task-description').value = task.description || '';
                                document.getElementById('task-prefix').value = task.prefix || '';
                                document.getElementById('task-schedule-type').value = task.schedule_type || 'manual';
                                document.getElementById('task-schedule-time').value = task.schedule_time || '02:00';
                                document.getElementById('task-schedule-interval').value = task.schedule_interval || 1;
                                document.getElementById('task-sync-mode-select').value = task.sync_mode || 'incremental';
                                document.getElementById('task-max-workers').value = task.max_workers || 20;
                                document.getElementById('task-file-filter').value = task.file_filter || '';
                                document.getElementById('task-exclude-filter').value = task.exclude_filter || '';
                                document.getElementById('task-bandwidth-limit').value = task.bandwidth_limit || 0;
                                document.getElementById('task-retry-times').value = task.retry_times || 5;
                                document.getElementById('task-retry-delay').value = task.retry_delay || 3;
                                document.getElementById('task-chunk-threshold').value = task.chunk_threshold || 100;
                                document.getElementById('task-chunk-size').value = task.chunk_size || 10;
                                document.getElementById('task-verify-integrity').value = task.verify_integrity ? 'true' : 'false';
                                document.getElementById('task-delete-extra').value = task.delete_extra ? 'true' : 'false';
                                document.getElementById('task-enabled').checked = task.enabled || false;
                                
                                // 显示编辑页面
                                showPage('edit-task');
                            }})
                            .catch(console.error);
                        }}
                        
                        function deleteTask(id) {{
                            if (confirm('确定要删除这个任务吗？')) {{
                                fetch(`/api/tasks/${{id}}`, {{ method: 'DELETE' }})
                                    .then(r => r.json())
                                    .then(result => {{
                                        if (result.success) {{
                                            loadTasks();
                                        }} else {{
                                            alert('删除失败: ' + result.message);
                                        }}
                                    }})
                                    .catch(console.error);
                            }}
                        }}
                        
                        function showAddSourceModal() {{
                            // 清空表单
                            document.getElementById('source-add-form').reset();
                            // 清空配置字段
                            document.getElementById('add-source-config-fields').innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">请先选择存储类型</p>';
                            showPage('add-source');
                        }}

                        function updateSourceConfigFields() {{
                            const storageType = document.getElementById('add-source-type').value;
                            const configContainer = document.getElementById('add-source-config-fields');

                            if (!storageType) {{
                                configContainer.innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">请先选择存储类型</p>';
                                return;
                            }}

                            let fieldsHtml = '';

                            switch (storageType) {{
                                case 's3':
                                    fieldsHtml = `
                                        <div class="form-group">
                                            <label for="add-source-endpoint">端点URL *</label>
                                            <input type="url" id="add-source-endpoint" name="endpoint" required placeholder="https://oss-cn-shanghai.aliyuncs.com">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-region">区域 *</label>
                                            <input type="text" id="add-source-region" name="region" required placeholder="cn-shanghai">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-access-key">Access Key *</label>
                                            <input type="text" id="add-source-access-key" name="access_key" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-secret-key">Secret Key *</label>
                                            <input type="password" id="add-source-secret-key" name="secret_key" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-bucket">存储桶名称 *</label>
                                            <div style="display: flex; gap: 10px;">
                                                <input type="text" id="add-source-bucket" name="bucket" required style="flex: 1;">
                                                <button type="button" onclick="listSourceBuckets()" class="btn-secondary" style="white-space: nowrap;">📋 列出桶</button>
                                            </div>
                                        </div>
                                    `;
                                    break;
                                case 'sftp':
                                    fieldsHtml = `
                                        <div class="form-group">
                                            <label for="add-source-hostname">主机名 *</label>
                                            <input type="text" id="add-source-hostname" name="hostname" required placeholder="*************">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-port">端口</label>
                                            <input type="number" id="add-source-port" name="port" value="22" placeholder="22">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-username">用户名 *</label>
                                            <input type="text" id="add-source-username" name="username" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-password">密码 *</label>
                                            <input type="password" id="add-source-password" name="password" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-root-path">根路径</label>
                                            <input type="text" id="add-source-root-path" name="root_path" value="/" placeholder="/">
                                        </div>
                                    `;
                                    break;
                                case 'smb':
                                    fieldsHtml = `
                                        <div class="form-group">
                                            <label for="add-source-hostname">主机名 *</label>
                                            <input type="text" id="add-source-hostname" name="hostname" required placeholder="*************">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-share-name">共享名 *</label>
                                            <input type="text" id="add-source-share-name" name="share_name" required placeholder="shared">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-username">用户名</label>
                                            <input type="text" id="add-source-username" name="username" placeholder="可选">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-password">密码</label>
                                            <input type="password" id="add-source-password" name="password" placeholder="可选">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-domain">域</label>
                                            <input type="text" id="add-source-domain" name="domain" placeholder="可选">
                                        </div>
                                    `;
                                    break;
                                case 'ftp':
                                    fieldsHtml = `
                                        <div class="form-group">
                                            <label for="add-source-hostname">主机名 *</label>
                                            <input type="text" id="add-source-hostname" name="hostname" required placeholder="ftp.example.com">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-port">端口</label>
                                            <input type="number" id="add-source-port" name="port" value="21" placeholder="21">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-username">用户名 *</label>
                                            <input type="text" id="add-source-username" name="username" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-password">密码 *</label>
                                            <input type="password" id="add-source-password" name="password" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="add-source-use-tls">使用TLS</label>
                                            <select id="add-source-use-tls" name="use_tls">
                                                <option value="false">否</option>
                                                <option value="true">是</option>
                                            </select>
                                        </div>
                                    `;
                                    break;
                                case 'local':
                                    fieldsHtml = `
                                        <div class="form-group">
                                            <label for="add-source-root-path">根路径 *</label>
                                            <input type="text" id="add-source-root-path" name="root_path" required placeholder="D:\\data">
                                        </div>
                                    `;
                                    break;
                            }}

                            configContainer.innerHTML = fieldsHtml;
                        }}
                        
                        function showAddTargetModal() {{
                            // 清空表单
                            document.getElementById('target-add-form').reset();
                            // 清空配置字段
                            document.getElementById('add-target-config-fields').innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">请先选择存储类型</p>';
                            showPage('add-target');
                        }}

                        function updateTargetConfigFields() {{
                            const storageType = document.getElementById('add-target-type').value;
                            const configContainer = document.getElementById('add-target-config-fields');

                            if (!storageType) {{
                                configContainer.innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">请先选择存储类型</p>';
                                return;
                            }}

                            let fieldsHtml = '';

                            switch (storageType) {{
                                case 's3':
                                    fieldsHtml = `
                                        <div class="form-group">
                                            <label for="add-target-endpoint">端点URL *</label>
                                            <input type="url" id="add-target-endpoint" name="endpoint" required placeholder="https://cos.ap-beijing.myqcloud.com">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-region">区域 *</label>
                                            <input type="text" id="add-target-region" name="region" required placeholder="ap-beijing">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-access-key">Access Key *</label>
                                            <input type="text" id="add-target-access-key" name="access_key" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-secret-key">Secret Key *</label>
                                            <input type="password" id="add-target-secret-key" name="secret_key" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-bucket">存储桶名称 *</label>
                                            <div style="display: flex; gap: 10px;">
                                                <input type="text" id="add-target-bucket" name="bucket" required style="flex: 1;">
                                                <button type="button" onclick="listTargetBuckets()" class="btn-secondary" style="white-space: nowrap;">📋 列出桶</button>
                                            </div>
                                        </div>
                                    `;
                                    break;
                                case 'sftp':
                                    fieldsHtml = `
                                        <div class="form-group">
                                            <label for="add-target-hostname">主机名 *</label>
                                            <input type="text" id="add-target-hostname" name="hostname" required placeholder="*************">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-port">端口</label>
                                            <input type="number" id="add-target-port" name="port" value="22" placeholder="22">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-username">用户名 *</label>
                                            <input type="text" id="add-target-username" name="username" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-password">密码 *</label>
                                            <input type="password" id="add-target-password" name="password" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-root-path">根路径</label>
                                            <input type="text" id="add-target-root-path" name="root_path" value="/" placeholder="/">
                                        </div>
                                    `;
                                    break;
                                case 'smb':
                                    fieldsHtml = `
                                        <div class="form-group">
                                            <label for="add-target-hostname">主机名 *</label>
                                            <input type="text" id="add-target-hostname" name="hostname" required placeholder="*************">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-share-name">共享名 *</label>
                                            <input type="text" id="add-target-share-name" name="share_name" required placeholder="shared">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-username">用户名</label>
                                            <input type="text" id="add-target-username" name="username" placeholder="可选">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-password">密码</label>
                                            <input type="password" id="add-target-password" name="password" placeholder="可选">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-domain">域</label>
                                            <input type="text" id="add-target-domain" name="domain" placeholder="可选">
                                        </div>
                                    `;
                                    break;
                                case 'ftp':
                                    fieldsHtml = `
                                        <div class="form-group">
                                            <label for="add-target-hostname">主机名 *</label>
                                            <input type="text" id="add-target-hostname" name="hostname" required placeholder="ftp.example.com">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-port">端口</label>
                                            <input type="number" id="add-target-port" name="port" value="21" placeholder="21">
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-username">用户名 *</label>
                                            <input type="text" id="add-target-username" name="username" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-password">密码 *</label>
                                            <input type="password" id="add-target-password" name="password" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="add-target-use-tls">使用TLS</label>
                                            <select id="add-target-use-tls" name="use_tls">
                                                <option value="false">否</option>
                                                <option value="true">是</option>
                                            </select>
                                        </div>
                                    `;
                                    break;
                                case 'local':
                                    fieldsHtml = `
                                        <div class="form-group">
                                            <label for="add-target-root-path">根路径 *</label>
                                            <input type="text" id="add-target-root-path" name="root_path" required placeholder="D:\\backup">
                                        </div>
                                    `;
                                    break;
                            }}

                            configContainer.innerHTML = fieldsHtml;
                        }}
                        
                        function showAddTaskModal() {{
                            // 加载数据源和目标选项
                            Promise.all([
                                fetch('/api/sources').then(r => r.json()),
                                fetch('/api/targets').then(r => r.json())
                            ]).then(([sources, targets]) => {{
                                const sourceIds = Object.keys(sources);
                                const targetIds = Object.keys(targets);
                                
                                if (sourceIds.length === 0) {{
                                    alert('请先添加数据源');
                                    return;
                                }}
                                
                                if (targetIds.length === 0) {{
                                    alert('请先添加同步目标');
                                    return;
                                }}
                                
                                // 填充数据源选项
                                const sourceSelect = document.getElementById('add-task-source');
                                sourceSelect.innerHTML = '<option value="">请选择数据源</option>';
                                Object.entries(sources).forEach(([sourceId, source]) => {{
                                    const option = document.createElement('option');
                                    option.value = sourceId;
                                    option.textContent = source.name;
                                    sourceSelect.appendChild(option);
                                }});
                                
                                // 填充目标选项
                                const targetSelect = document.getElementById('add-task-target');
                                targetSelect.innerHTML = '<option value="">请选择目标</option>';
                                Object.entries(targets).forEach(([targetId, target]) => {{
                                    const option = document.createElement('option');
                                    option.value = targetId;
                                    option.textContent = target.name;
                                    targetSelect.appendChild(option);
                                }});
                                
                                // 清空表单并显示页面
                                document.getElementById('task-add-form').reset();
                                document.getElementById('add-task-enabled').checked = true;
                                showPage('add-task');
                            }}).catch(console.error);
                        }}
                        
                        // 表单提交处理
                        document.addEventListener('DOMContentLoaded', function() {{
                            // 数据源编辑表单
                            document.getElementById('source-edit-form').addEventListener('submit', function(e) {{
                                e.preventDefault();
                                const formData = new FormData(e.target);
                                const data = Object.fromEntries(formData.entries());
                                
                                fetch(`/api/sources/${{currentEditId}}`, {{
                                    method: 'PUT',
                                    headers: {{ 'Content-Type': 'application/json' }},
                                    body: JSON.stringify(data)
                                }})
                                .then(r => r.json())
                                .then(result => {{
                                    if (result.success) {{
                                        alert('数据源更新成功');
                                        showPage('source-config');
                                        loadSources();
                                    }} else {{
                                        alert('更新失败: ' + result.message);
                                    }}
                                }})
                                .catch(console.error);
                            }});
                            
                            // 目标编辑表单
                            document.getElementById('target-edit-form').addEventListener('submit', function(e) {{
                                e.preventDefault();
                                const formData = new FormData(e.target);
                                const data = Object.fromEntries(formData.entries());
                                
                                fetch(`/api/targets/${{currentEditId}}`, {{
                                    method: 'PUT',
                                    headers: {{ 'Content-Type': 'application/json' }},
                                    body: JSON.stringify(data)
                                }})
                                .then(r => r.json())
                                .then(result => {{
                                    if (result.success) {{
                                        alert('目标更新成功');
                                        showPage('target-config');
                                        loadTargets();
                                    }} else {{
                                        alert('更新失败: ' + result.message);
                                    }}
                                }})
                                .catch(console.error);
                            }});
                            
                            // 任务编辑表单
                            document.getElementById('task-edit-form').addEventListener('submit', function(e) {{
                                e.preventDefault();
                                const formData = new FormData(e.target);
                                const data = Object.fromEntries(formData.entries());
                                data.enabled = document.getElementById('task-enabled').checked;
                                data.verify_integrity = data.verify_integrity === 'true';
                                data.delete_extra = data.delete_extra === 'true';
                                
                                fetch(`/api/tasks/${{currentEditId}}`, {{
                                    method: 'PUT',
                                    headers: {{ 'Content-Type': 'application/json' }},
                                    body: JSON.stringify(data)
                                }})
                                .then(r => r.json())
                                .then(result => {{
                                    if (result.success) {{
                                        alert('任务更新成功');
                                        showPage('task-config');
                                        loadTasks();
                                    }} else {{
                                        alert('更新失败: ' + result.message);
                                    }}
                                }})
                                .catch(console.error);
                            }});
                            
                            // 数据源添加表单
                            document.getElementById('source-add-form').addEventListener('submit', function(e) {{
                                e.preventDefault();
                                const formData = new FormData(e.target);
                                const data = Object.fromEntries(formData.entries());
                                
                                fetch('/api/sources', {{
                                    method: 'POST',
                                    headers: {{ 'Content-Type': 'application/json' }},
                                    body: JSON.stringify(data)
                                }})
                                .then(r => r.json())
                                .then(result => {{
                                    if (result.success) {{
                                        alert('数据源添加成功');
                                        showPage('source-config');
                                        loadSources();
                                    }} else {{
                                        alert('添加失败: ' + result.message);
                                    }}
                                }})
                                .catch(console.error);
                            }});
                            
                            // 目标添加表单
                            document.getElementById('target-add-form').addEventListener('submit', function(e) {{
                                e.preventDefault();
                                const formData = new FormData(e.target);
                                const data = Object.fromEntries(formData.entries());
                                
                                fetch('/api/targets', {{
                                    method: 'POST',
                                    headers: {{ 'Content-Type': 'application/json' }},
                                    body: JSON.stringify(data)
                                }})
                                .then(r => r.json())
                                .then(result => {{
                                    if (result.success) {{
                                        alert('目标添加成功');
                                        showPage('target-config');
                                        loadTargets();
                                    }} else {{
                                        alert('添加失败: ' + result.message);
                                    }}
                                }})
                                .catch(console.error);
                            }});
                            
                            // 任务添加表单
                            document.getElementById('task-add-form').addEventListener('submit', function(e) {{
                                e.preventDefault();
                                const formData = new FormData(e.target);
                                const data = Object.fromEntries(formData.entries());
                                data.enabled = document.getElementById('add-task-enabled').checked;
                                data.verify_integrity = data.verify_integrity === 'true';
                                data.delete_extra = data.delete_extra === 'true';
                                
                                fetch('/api/tasks', {{
                                    method: 'POST',
                                    headers: {{ 'Content-Type': 'application/json' }},
                                    body: JSON.stringify(data)
                                }})
                                .then(r => r.json())
                                .then(result => {{
                                    if (result.success) {{
                                        alert('任务创建成功');
                                        showPage('task-config');
                                        loadTasks();
                                    }} else {{
                                        alert('创建失败: ' + result.message);
                                    }}
                                }})
                                .catch(console.error);
                            }});
                        }});
                        
                        // 取消编辑函数
                        function cancelSourceEdit() {{
                            showPage('source-config');
                        }}
                        
                        function cancelTargetEdit() {{
                            showPage('target-config');
                        }}
                        
                        function cancelTaskEdit() {{
                            showPage('task-config');
                        }}
                        
                        function cancelSourceAdd() {{
                            showPage('source-config');
                        }}
                        
                        function cancelTargetAdd() {{
                            showPage('target-config');
                        }}
                        
                        function cancelTaskAdd() {{
                            showPage('task-config');
                        }}
                        
                        // 连接测试函数
                        function testSourceConnection() {{
                            const data = {{
                                endpoint: document.getElementById('source-endpoint').value,
                                access_key: document.getElementById('source-access-key').value,
                                secret_key: document.getElementById('source-secret-key').value,
                                bucket: document.getElementById('source-bucket').value,
                                region: document.getElementById('source-region').value
                            }};
                            
                            if (!data.endpoint || !data.access_key || !data.secret_key || !data.bucket || !data.region) {{
                                alert('请填写完整的连接信息');
                                return;
                            }}
                            
                            fetch('/api/test-connection', {{
                                method: 'POST',
                                headers: {{ 'Content-Type': 'application/json' }},
                                body: JSON.stringify(data)
                            }})
                            .then(r => r.json())
                            .then(result => {{
                                if (result.success) {{
                                    alert('连接测试成功！');
                                }} else {{
                                    alert('连接测试失败: ' + result.message);
                                }}
                            }})
                            .catch(console.error);
                        }}
                        
                        function testTargetConnection() {{
                            const data = {{
                                endpoint: document.getElementById('target-endpoint').value,
                                access_key: document.getElementById('target-access-key').value,
                                secret_key: document.getElementById('target-secret-key').value,
                                bucket: document.getElementById('target-bucket').value,
                                region: document.getElementById('target-region').value
                            }};
                            
                            if (!data.endpoint || !data.access_key || !data.secret_key || !data.bucket || !data.region) {{
                                alert('请填写完整的连接信息');
                                return;
                            }}
                            
                            fetch('/api/test-connection', {{
                                method: 'POST',
                                headers: {{ 'Content-Type': 'application/json' }},
                                body: JSON.stringify(data)
                            }})
                            .then(r => r.json())
                            .then(result => {{
                                if (result.success) {{
                                    alert('连接测试成功！');
                                }} else {{
                                    alert('连接测试失败: ' + result.message);
                                }}
                            }})
                            .catch(console.error);
                        }}
                        
                        function testAddSourceConnection() {{
                            const data = {{
                                endpoint: document.getElementById('add-source-endpoint').value,
                                access_key: document.getElementById('add-source-access-key').value,
                                secret_key: document.getElementById('add-source-secret-key').value,
                                bucket: document.getElementById('add-source-bucket').value,
                                region: document.getElementById('add-source-region').value
                            }};
                            
                            if (!data.endpoint || !data.access_key || !data.secret_key || !data.bucket || !data.region) {{
                                alert('请填写完整的连接信息');
                                return;
                            }}
                            
                            fetch('/api/test-connection', {{
                                method: 'POST',
                                headers: {{ 'Content-Type': 'application/json' }},
                                body: JSON.stringify(data)
                            }})
                            .then(r => r.json())
                            .then(result => {{
                                if (result.success) {{
                                    alert('连接测试成功！');
                                }} else {{
                                    alert('连接测试失败: ' + result.message);
                                }}
                            }})
                            .catch(console.error);
                        }}
                        
                        function testAddTargetConnection() {{
                            const data = {{
                                endpoint: document.getElementById('add-target-endpoint').value,
                                access_key: document.getElementById('add-target-access-key').value,
                                secret_key: document.getElementById('add-target-secret-key').value,
                                bucket: document.getElementById('add-target-bucket').value,
                                region: document.getElementById('add-target-region').value
                            }};
                            
                            if (!data.endpoint || !data.access_key || !data.secret_key || !data.bucket || !data.region) {{
                                alert('请填写完整的连接信息');
                                return;
                            }}
                            
                            fetch('/api/test-connection', {{
                                method: 'POST',
                                headers: {{ 'Content-Type': 'application/json' }},
                                body: JSON.stringify(data)
                            }})
                            .then(r => r.json())
                            .then(result => {{
                                if (result.success) {{
                                    alert('连接测试成功！');
                                }} else {{
                                    alert('连接测试失败: ' + result.message);
                                }}
                            }})
                            .catch(console.error);
                        }}
                        
                        // 列出桶的函数
                        function listSourceBuckets() {{
                            const data = {{
                                endpoint: document.getElementById('add-source-endpoint').value,
                                access_key: document.getElementById('add-source-access-key').value,
                                secret_key: document.getElementById('add-source-secret-key').value,
                                region: document.getElementById('add-source-region').value
                            }};
                            
                            if (!data.endpoint || !data.access_key || !data.secret_key || !data.region) {{
                                alert('请先填写端点URL、Access Key、Secret Key和区域信息');
                                return;
                            }}
                            
                            // 显示加载状态
                            showBucketLoadingModal();
                            
                            fetch('/api/list-buckets', {{
                                method: 'POST',
                                headers: {{ 'Content-Type': 'application/json' }},
                                body: JSON.stringify(data)
                            }})
                            .then(r => r.json())
                            .then(result => {{
                                closeBucketModal();
                                if (result.success && result.buckets.length > 0) {{
                                    showBucketSelectionModal(result.buckets, 'add-source-bucket');
                                }} else {{
                                    alert('获取存储桶列表失败: ' + (result.message || '未找到存储桶'));
                                }}
                            }})
                            .catch(error => {{
                                closeBucketModal();
                                console.error(error);
                                alert('获取存储桶列表失败，请检查网络连接');
                            }});
                        }}
                        
                        function listTargetBuckets() {{
                            const data = {{
                                endpoint: document.getElementById('add-target-endpoint').value,
                                access_key: document.getElementById('add-target-access-key').value,
                                secret_key: document.getElementById('add-target-secret-key').value,
                                region: document.getElementById('add-target-region').value
                            }};
                            
                            if (!data.endpoint || !data.access_key || !data.secret_key || !data.region) {{
                                alert('请先填写端点URL、Access Key、Secret Key和区域信息');
                                return;
                            }}
                            
                            showBucketLoadingModal();
                            
                            fetch('/api/list-buckets', {{
                                method: 'POST',
                                headers: {{ 'Content-Type': 'application/json' }},
                                body: JSON.stringify(data)
                            }})
                            .then(r => r.json())
                            .then(result => {{
                                closeBucketModal();
                                if (result.success && result.buckets.length > 0) {{
                                    showBucketSelectionModal(result.buckets, 'add-target-bucket');
                                }} else {{
                                    alert('获取存储桶列表失败: ' + (result.message || '未找到存储桶'));
                                }}
                            }})
                            .catch(error => {{
                                closeBucketModal();
                                console.error(error);
                                alert('获取存储桶列表失败，请检查网络连接');
                            }});
                        }}
                        
                        function listEditSourceBuckets() {{
                            const data = {{
                                endpoint: document.getElementById('source-endpoint').value,
                                access_key: document.getElementById('source-access-key').value,
                                secret_key: document.getElementById('source-secret-key').value,
                                region: document.getElementById('source-region').value
                            }};
                            
                            if (!data.endpoint || !data.access_key || !data.secret_key || !data.region) {{
                                alert('请先填写端点URL、Access Key、Secret Key和区域信息');
                                return;
                            }}
                            
                            showBucketLoadingModal();
                            
                            fetch('/api/list-buckets', {{
                                method: 'POST',
                                headers: {{ 'Content-Type': 'application/json' }},
                                body: JSON.stringify(data)
                            }})
                            .then(r => r.json())
                            .then(result => {{
                                closeBucketModal();
                                if (result.success && result.buckets.length > 0) {{
                                    showBucketSelectionModal(result.buckets, 'source-bucket');
                                }} else {{
                                    alert('获取存储桶列表失败: ' + (result.message || '未找到存储桶'));
                                }}
                            }})
                            .catch(error => {{
                                closeBucketModal();
                                console.error(error);
                                alert('获取存储桶列表失败，请检查网络连接');
                            }});
                        }}
                        
                        function listEditTargetBuckets() {{
                            const data = {{
                                endpoint: document.getElementById('target-endpoint').value,
                                access_key: document.getElementById('target-access-key').value,
                                secret_key: document.getElementById('target-secret-key').value,
                                region: document.getElementById('target-region').value
                            }};
                            
                            if (!data.endpoint || !data.access_key || !data.secret_key || !data.region) {{
                                alert('请先填写端点URL、Access Key、Secret Key和区域信息');
                                return;
                            }}
                            
                            showBucketLoadingModal();
                            
                            fetch('/api/list-buckets', {{
                                method: 'POST',
                                headers: {{ 'Content-Type': 'application/json' }},
                                body: JSON.stringify(data)
                            }})
                            .then(r => r.json())
                            .then(result => {{
                                closeBucketModal();
                                if (result.success && result.buckets.length > 0) {{
                                    showBucketSelectionModal(result.buckets, 'target-bucket');
                                }} else {{
                                    alert('获取存储桶列表失败: ' + (result.message || '未找到存储桶'));
                                }}
                            }})
                            .catch(error => {{
                                closeBucketModal();
                                console.error(error);
                                alert('获取存储桶列表失败，请检查网络连接');
                            }});
                        }}
                        
                        // 桶选择模态框相关函数
                        function showBucketLoadingModal() {{
                            const modal = document.createElement('div');
                            modal.id = 'bucket-modal';
                            modal.style.cssText = `
                                position: fixed;
                                top: 0;
                                left: 0;
                                width: 100%;
                                height: 100%;
                                background: rgba(0, 0, 0, 0.5);
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                z-index: 10000;
                            `;
                            
                            modal.innerHTML = `
                                <div style="
                                    background: white;
                                    border-radius: 10px;
                                    padding: 30px;
                                    text-align: center;
                                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                                    min-width: 300px;
                                ">
                                    <div style="
                                        width: 40px;
                                        height: 40px;
                                        border: 4px solid #f3f3f3;
                                        border-top: 4px solid #ff6b35;
                                        border-radius: 50%;
                                        animation: spin 1s linear infinite;
                                        margin: 0 auto 20px auto;
                                    "></div>
                                    <h3 style="color: #333; margin: 0 0 10px 0;">正在获取存储桶列表</h3>
                                    <p style="color: #666; margin: 0;">请稍候...</p>
                                </div>
                            `;
                            
                            // 添加旋转动画
                            const style = document.createElement('style');
                            style.textContent = `
                                @keyframes spin {{
                                    0% {{ transform: rotate(0deg); }}
                                    100% {{ transform: rotate(360deg); }}
                                }}
                            `;
                            document.head.appendChild(style);
                            
                            document.body.appendChild(modal);
                        }}
                        
                        function showBucketSelectionModal(buckets, targetInputId) {{
                            const modal = document.createElement('div');
                            modal.id = 'bucket-modal';
                            modal.style.cssText = `
                                position: fixed;
                                top: 0;
                                left: 0;
                                width: 100%;
                                height: 100%;
                                background: rgba(0, 0, 0, 0.5);
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                z-index: 10000;
                            `;
                            
                            const bucketListHtml = buckets.map(bucket => `
                                <div style="
                                    padding: 12px 20px;
                                    border: 1px solid #e0e0e0;
                                    border-radius: 8px;
                                    margin: 8px 0;
                                    cursor: pointer;
                                    transition: all 0.3s ease;
                                    background: white;
                                " 
                                onmouseover="this.style.background='#f0f8ff'; this.style.borderColor='#ff6b35';"
                                onmouseout="this.style.background='white'; this.style.borderColor='#e0e0e0';"
                                onclick="selectBucket('${{bucket}}', '${{targetInputId}}')">
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div style="
                                            width: 32px;
                                            height: 32px;
                                            background: linear-gradient(135deg, #ff6b35, #ff8c42);
                                            border-radius: 6px;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            color: white;
                                            font-weight: bold;
                                            font-size: 14px;
                                        ">🪣</div>
                                        <div>
                                            <div style="font-weight: 600; color: #333; font-size: 16px;">${{bucket}}</div>
                                            <div style="color: #666; font-size: 12px;">点击选择此存储桶</div>
                                        </div>
                                    </div>
                                </div>
                            `).join('');
                            
                            modal.innerHTML = `
                                <div style="
                                    background: white;
                                    border-radius: 15px;
                                    width: 90%;
                                    max-width: 500px;
                                    max-height: 80vh;
                                    overflow: hidden;
                                    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
                                ">
                                    <div style="
                                        background: linear-gradient(135deg, #ff6b35, #ff8c42);
                                        color: white;
                                        padding: 20px;
                                        text-align: center;
                                    ">
                                        <h3 style="margin: 0 0 5px 0; font-size: 18px;">选择存储桶</h3>
                                        <p style="margin: 0; opacity: 0.9; font-size: 14px;">找到 ${{buckets.length}} 个可用的存储桶</p>
                                    </div>
                                    
                                    <div style="
                                        padding: 20px;
                                        max-height: 400px;
                                        overflow-y: auto;
                                    ">
                                        ${{bucketListHtml}}
                                    </div>
                                    
                                    <div style="
                                        padding: 15px 20px;
                                        border-top: 1px solid #e0e0e0;
                                        text-align: right;
                                        background: #f8f9fa;
                                    ">
                                        <button onclick="closeBucketModal()" style="
                                            background: #6c757d;
                                            color: white;
                                            border: none;
                                            padding: 8px 20px;
                                            border-radius: 6px;
                                            cursor: pointer;
                                            font-size: 14px;
                                        ">取消</button>
                                    </div>
                                </div>
                            `;
                            
                            document.body.appendChild(modal);
                        }}
                        
                        function selectBucket(bucketName, targetInputId) {{
                            document.getElementById(targetInputId).value = bucketName;
                            closeBucketModal();
                        }}
                        
                        function closeBucketModal() {{
                            const modal = document.getElementById('bucket-modal');
                            if (modal) {{
                                modal.remove();
                            }}
                        }}
                    </script>
                </body>
                </html>
                """
                
                # 格式化HTML模板
                html = html_template.format(
                    logo_base64=logo_base64,
                    sources_count=stats['sources_count'],
                    targets_count=stats['targets_count'],
                    tasks_count=stats['tasks_count'],
                    active_tasks=stats['active_tasks'],
                    total_executions=stats['total_executions'],
                    today_executions=stats['today_executions'],
                    total_files=stats['total_files'],
                    total_size_formatted=stats['total_size_formatted'],
                    success_rate=stats.get('success_rate', 0),
                    avg_speed_formatted=stats.get('avg_speed_formatted', '0 B/s'),
                    running_tasks=stats.get('running_tasks', 0),
                    recent_executions_html=self._get_recent_executions_html(),
                    js_stats=js_stats
                )
                
                self.send_response(200)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _get_dashboard_stats(self):
                """获取仪表盘统计数据"""
                sources = config_manager.get_all_sources()
                targets = config_manager.get_all_targets()
                tasks = task_manager.get_all_tasks()
                
                # 基础统计
                stats = {
                    'sources_count': len(sources),
                    'targets_count': len(targets),
                    'tasks_count': len(tasks),
                    'active_tasks': sum(1 for task in tasks.values() if task.get('enabled', True)),
                }
                
                # 数据库统计
                if db_manager:
                    db_stats = db_manager.get_statistics()
                    stats.update({
                        'total_executions': db_stats.get('total_executions', 0),
                        'today_executions': db_stats.get('today_executions', 0),
                        'total_files': db_stats.get('total_files', 0),
                        'total_size': db_stats.get('total_size', 0),
                        'total_size_formatted': self._format_size(db_stats.get('total_size', 0)),
                        'success_rate': db_stats.get('success_rate', 0),
                        'avg_speed': db_stats.get('avg_speed', 0),
                        'avg_speed_formatted': self._format_speed(db_stats.get('avg_speed', 0)),
                        'running_tasks': db_stats.get('running_tasks', 0),
                        'failed_tasks_today': db_stats.get('failed_tasks_today', 0),
                        'peak_hour': db_stats.get('peak_hour', '未知'),
                        'largest_file_size': self._format_size(db_stats.get('largest_file_size', 0)),
                        'total_errors': db_stats.get('total_errors', 0),
                    })
                    
                    # 任务状态分布
                    task_status_chart = {}
                    for task in tasks.values():
                        status = task.get('status', 'idle')
                        task_status_chart[status] = task_status_chart.get(status, 0) + 1
                    stats['task_status_chart'] = task_status_chart
                    
                    # 最近7天执行趋势
                    execution_trend = db_manager.get_execution_trend(7)
                    stats['execution_trend'] = execution_trend
                    
                    # 存储类型分布
                    storage_types = {}
                    for source in sources.values():
                        endpoint = source.get('endpoint', '')
                        if 'aliyun' in endpoint:
                            storage_types['阿里云OSS'] = storage_types.get('阿里云OSS', 0) + 1
                        elif 'amazonaws' in endpoint:
                            storage_types['AWS S3'] = storage_types.get('AWS S3', 0) + 1
                        elif 'myqcloud' in endpoint:
                            storage_types['腾讯云COS'] = storage_types.get('腾讯云COS', 0) + 1
                        else:
                            storage_types['其他'] = storage_types.get('其他', 0) + 1
                    stats['storage_types'] = storage_types
                    
                    # 文件类型分布
                    file_types = db_stats.get('file_types', {})
                    stats['file_types'] = file_types
                    
                else:
                    stats.update({
                        'total_executions': 0,
                        'today_executions': 0,
                        'total_files': 0,
                        'total_size': 0,
                        'total_size_formatted': '0 B',
                        'task_status_chart': {'idle': len(tasks)},
                        'execution_trend': {}
                    })
                
                return stats
            
            def _get_recent_executions_html(self):
                """获取最近执行记录的HTML"""
                if not db_manager:
                    return '<div class="recent-item">暂无执行记录</div>'
                
                executions = db_manager.get_recent_executions(5)
                if not executions:
                    return '<div class="recent-item">暂无执行记录</div>'
                
                html = ''
                for execution in executions:
                    status_class = f"status-{execution.get('status', 'idle')}"
                    status_text = {
                        'completed': '已完成',
                        'running': '运行中',
                        'failed': '失败',
                        'idle': '空闲'
                    }.get(execution.get('status', 'idle'), '未知')
                    
                    execution_id = execution.get('id', '')
                    html += f'''
                    <div class="recent-item" onclick="showExecutionDetails({execution_id})" style="cursor: pointer;">
                        <div class="recent-info">
                            <div class="recent-name">{execution.get('task_name', '未知任务')}</div>
                            <div class="recent-details">
                                执行时间: {execution.get('start_time', '未知')} | 
                                文件: {execution.get('success_files', 0)}/{execution.get('total_files', 0)} | 
                                大小: {self._format_size(execution.get('transferred_size', 0))}
                            </div>
                        </div>
                        <div class="recent-status {status_class}">{status_text}</div>
                    </div>
                    '''
                
                return html
            
            def _format_size(self, size_bytes):
                """格式化文件大小"""
                if size_bytes == 0:
                    return "0 B"
                
                size_names = ["B", "KB", "MB", "GB", "TB"]
                import math
                i = int(math.floor(math.log(size_bytes, 1024)))
                p = math.pow(1024, i)
                s = round(size_bytes / p, 2)
                return f"{s} {size_names[i]}"
            
            def _format_speed(self, speed_bytes_per_sec):
                """格式化传输速度"""
                if speed_bytes_per_sec == 0:
                    return "0 B/s"
                
                size_names = ["B/s", "KB/s", "MB/s", "GB/s"]
                import math
                i = int(math.floor(math.log(speed_bytes_per_sec, 1024)))
                p = math.pow(1024, i)
                s = round(speed_bytes_per_sec / p, 2)
                return f"{s} {size_names[i]}"
            
            def _get_manual_html(self):
                """生成用户手册页面HTML"""
                return """
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Lightrek S3 同步工具 - 用户手册</title>
                    <style>
                        * { margin: 0; padding: 0; box-sizing: border-box; }
                        body { 
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            min-height: 100vh;
                        }
                        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
                        .header { text-align: center; color: white; margin-bottom: 30px; }
                        .header h1 { font-size: 2.5rem; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
                        .nav-bar {
                            background: rgba(255, 255, 255, 0.1);
                            backdrop-filter: blur(10px);
                            border-radius: 15px;
                            padding: 15px;
                            margin-bottom: 30px;
                            display: flex;
                            justify-content: center;
                            gap: 20px;
                            flex-wrap: wrap;
                        }
                        .nav-item {
                            color: white;
                            text-decoration: none;
                            padding: 10px 20px;
                            border-radius: 25px;
                            transition: all 0.3s ease;
                        }
                        .nav-item:hover { background: rgba(255, 255, 255, 0.2); }
                        .nav-item.active { background: rgba(255, 255, 255, 0.3); }
                        .manual-container {
                            background: rgba(255, 255, 255, 0.95);
                            border-radius: 15px;
                            padding: 40px;
                            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                        }
                        .section { margin-bottom: 40px; }
                        .section h2 { 
                            color: #333; 
                            margin-bottom: 20px; 
                            padding-bottom: 10px; 
                            border-bottom: 2px solid #667eea;
                        }
                        .section h3 { 
                            color: #555; 
                            margin: 20px 0 10px 0;
                        }
                        .section p, .section li { 
                            line-height: 1.6; 
                            color: #666; 
                            margin-bottom: 10px;
                        }
                        .section ul, .section ol { 
                            margin-left: 20px; 
                            margin-bottom: 15px;
                        }
                        .feature-grid {
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                            gap: 20px;
                            margin: 20px 0;
                        }
                        .feature-card {
                            background: #f8f9fa;
                            padding: 20px;
                            border-radius: 10px;
                            border-left: 4px solid #667eea;
                        }
                        .feature-card h4 {
                            color: #333;
                            margin-bottom: 10px;
                        }
                        .code-block {
                            background: #f4f4f4;
                            border: 1px solid #ddd;
                            border-radius: 5px;
                            padding: 15px;
                            font-family: 'Courier New', monospace;
                            margin: 10px 0;
                            overflow-x: auto;
                        }
                        .warning {
                            background: #fff3cd;
                            border: 1px solid #ffeaa7;
                            border-radius: 5px;
                            padding: 15px;
                            margin: 15px 0;
                            color: #856404;
                        }
                        .info {
                            background: #d1ecf1;
                            border: 1px solid #bee5eb;
                            border-radius: 5px;
                            padding: 15px;
                            margin: 15px 0;
                            color: #0c5460;
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1>📖 Lightrek S3 同步工具用户手册</h1>
                            <p>专业的S3对象存储同步解决方案</p>
                        </div>
                        
                        <div class="nav-bar">
                            <a href="/dashboard" class="nav-item">🏠 仪表盘</a>
                            <a href="/logs" class="nav-item">📊 任务日志</a>
                            <a href="/manual" class="nav-item active">📖 用户手册</a>
                        </div>
                        
                        <div class="manual-container">
                            <div class="section">
                                <h2>🚀 快速开始</h2>
                                <p>Lightrek S3 同步工具是一个功能强大的对象存储同步解决方案，支持多种S3兼容的存储服务，具备完整性验证、性能优化、智能缓存等企业级功能。</p>
                                
                                <h3>核心特性</h3>
                                <ul>
                                    <li>🛡️ <strong>完整性验证</strong> - 文件大小和ETag双重校验，1KB智能容错</li>
                                    <li>⚡ <strong>性能优化</strong> - 并行扫描、智能缓存，速度提升90%+</li>
                                    <li>📋 <strong>任务配置查看</strong> - 详细的任务配置展示和管理</li>
                                    <li>🔄 <strong>多种调度模式</strong> - 支持分钟、小时、每日、每周、间隔调度</li>
                                </ul>
                                
                                <h3>系统要求</h3>
                                <ul>
                                    <li>Python 3.7 或更高版本</li>
                                    <li>支持的操作系统：Windows、macOS、Linux</li>
                                    <li>网络连接到目标S3存储服务</li>
                                </ul>
                                
                                <h3>安装步骤</h3>
                                <ol>
                                    <li>下载并解压 Lightrek S3 同步工具</li>
                                    <li>确保系统已安装 Python 3.7+</li>
                                    <li>运行启动脚本开始使用</li>
                                </ol>
                                
                                <div class="code-block">
# macOS/Linux
./start.sh

# Windows  
start.bat

# 直接运行Python版本
python3 start_lightrek.py
                                </div>
                            </div>
                            
                            <div class="section">
                                <h2>⚙️ 配置管理</h2>
                                
                                <h3>数据源配置</h3>
                                <p>配置您要同步的源S3存储：</p>
                                <ul>
                                    <li><strong>名称</strong>：为数据源起一个易识别的名称</li>
                                    <li><strong>访问密钥</strong>：S3 Access Key ID</li>
                                    <li><strong>密钥</strong>：S3 Secret Access Key</li>
                                    <li><strong>端点</strong>：S3服务端点URL</li>
                                    <li><strong>区域</strong>：存储桶所在区域</li>
                                    <li><strong>存储桶</strong>：源存储桶名称</li>
                                </ul>
                                
                                <h3>目标配置</h3>
                                <p>配置同步的目标S3存储，参数与数据源类似。</p>
                                
                                <div class="info">
                                    <strong>提示</strong>：支持不同厂商之间的S3存储同步，如阿里云OSS、腾讯云COS、AWS S3等。
                                </div>
                            </div>
                            
                            <div class="section">
                                <h2>📋 任务管理</h2>
                                
                                <h3>创建同步任务</h3>
                                <p>配置完数据源和目标后，您可以创建同步任务：</p>
                                
                                <div class="feature-grid">
                                    <div class="feature-card">
                                        <h4>🔄 同步模式</h4>
                                        <ul>
                                            <li><strong>增量同步</strong>：只同步新增和修改的文件</li>
                                            <li><strong>完整同步</strong>：同步所有文件</li>
                                            <li><strong>镜像同步</strong>：保持目标与源完全一致</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="feature-card">
                                        <h4>📅 调度选项</h4>
                                        <ul>
                                            <li><strong>手动执行</strong>：按需执行同步</li>
                                            <li><strong>每分钟执行</strong>：适用于高频同步</li>
                                            <li><strong>每小时执行</strong>：定时同步</li>
                                            <li><strong>每日执行</strong>：日常备份</li>
                                            <li><strong>每周执行</strong>：定期同步</li>
                                            <li><strong>间隔执行</strong>：自定义间隔时间</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="feature-card">
                                        <h4>🔧 高级选项</h4>
                                        <ul>
                                            <li><strong>文件过滤</strong>：支持通配符过滤</li>
                                            <li><strong>排除规则</strong>：排除不需要同步的文件</li>
                                            <li><strong>带宽限制</strong>：控制同步速度</li>
                                            <li><strong>重试机制</strong>：自动重试失败的传输</li>
                                            <li><strong>完整性验证</strong>：确保数据传输安全</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <h3>任务配置查看</h3>
                                <p>新增的任务配置查看功能让您可以：</p>
                                <ul>
                                    <li>📋 <strong>详细配置展示</strong>：查看任务的所有配置参数</li>
                                    <li>🔍 <strong>分类显示</strong>：按基本信息、同步设置、性能参数等分类</li>
                                    <li>⚡ <strong>快速操作</strong>：从查看页面直接编辑或运行任务</li>
                                    <li>🔄 <strong>状态指示</strong>：显示各项配置的启用状态</li>
                                </ul>
                                
                                <div class="info">
                                    <strong>使用提示</strong>：在任务列表中点击"查看"按钮即可查看任务的详细配置信息。
                                </div>
                            </div>
                            
                            <div class="section">
                                <h2>📊 监控与日志</h2>
                                
                                <h3>实时监控</h3>
                                <p>在仪表盘中可以查看：</p>
                                <ul>
                                    <li>任务运行状态</li>
                                    <li>同步进度和速度</li>
                                    <li>传输数据量统计</li>
                                    <li>错误和警告信息</li>
                                </ul>
                                
                                <h3>日志查看</h3>
                                <p>任务日志功能提供：</p>
                                <ul>
                                    <li>详细的执行记录</li>
                                    <li>错误诊断信息</li>
                                    <li>性能分析数据</li>
                                    <li>按任务和时间过滤</li>
                                </ul>
                            </div>
                            
                            <div class="section">
                                <h2>🔧 高级功能</h2>
                                
                                <h3>性能优化系统</h3>
                                <p>全面的性能优化功能：</p>
                                <ul>
                                    <li>⚡ <strong>并行扫描</strong>：多线程文件扫描，提升50%+速度</li>
                                    <li>💾 <strong>智能缓存</strong>：元数据缓存，二次扫描提升90%+</li>
                                    
                                    <li>🌊 <strong>流式处理</strong>：大量文件的分批处理</li>
                                    <li>🔧 <strong>配置工具</strong>：独立的配置管理命令行工具</li>
                                </ul>
                                
                                <h3>完整性验证与安全</h3>
                                <p>确保数据传输的安全性：</p>
                                <ul>
                                    <li>🛡️ <strong>双重校验</strong>：文件大小和ETag双重验证</li>
                                    <li>🔍 <strong>智能容错</strong>：1KB容错机制，处理微小差异</li>
                                    <li>🔄 <strong>自动重试</strong>：指数退避重试机制</li>
                                    <li>📊 <strong>完整性报告</strong>：详细的验证结果统计</li>
                                </ul>
                                
                                <h3>大文件传输</h3>
                                <p>针对大文件优化：</p>
                                <ul>
                                    <li>分片上传支持（可配置阈值和大小）</li>
                                    <li>断点续传功能</li>
                                    <li>并发传输优化</li>
                                    <li>完整性校验</li>
                                </ul>
                                
                                <h3>安全特性</h3>
                                <ul>
                                    <li>SSL/TLS 加密传输</li>
                                    <li>访问密钥安全存储</li>
                                    <li>操作日志记录</li>
                                    <li>权限验证机制</li>
                                </ul>
                                
                                <div class="warning">
                                    <strong>安全提醒</strong>：请妥善保管您的访问密钥，避免在不安全的环境中暴露。建议启用完整性验证以确保数据安全。
                                </div>
                            </div>
                            
                            <div class="section">
                                <h2>❓ 常见问题</h2>
                                
                                <h3>连接问题</h3>
                                <p><strong>Q: 连接测试失败怎么办？</strong></p>
                                <p>A: 请检查：</p>
                                <ul>
                                    <li>网络连接是否正常</li>
                                    <li>访问密钥是否正确</li>
                                    <li>端点URL是否正确</li>
                                    <li>存储桶是否存在且有权限访问</li>
                                </ul>
                                
                                <h3>同步问题</h3>
                                <p><strong>Q: 同步速度较慢怎么优化？</strong></p>
                                <p>A: 可以尝试：</p>
                                <ul>
                                    <li>使用配置工具启用高性能方案</li>
                                    <li>增加并发工作线程数（1-100）</li>
                                    <li>启用并行扫描和智能缓存</li>
                                    <li>调整分片大小和阈值</li>
                                    <li>检查网络带宽并设置合理限制</li>
                                    <li>优化文件过滤规则</li>
                                </ul>
                                
                                <h3>完整性验证问题</h3>
                                <p><strong>Q: 出现文件大小不匹配错误怎么办？</strong></p>
                                <p>A: 系统已内置解决方案：</p>
                                <ul>
                                    <li>1KB容错机制：自动处理微小差异</li>
                                    <li>智能重试：自动重新尝试传输</li>
                                    <li>如果问题持续，检查网络稳定性</li>
                                    <li>可在任务配置中临时禁用完整性验证</li>
                                </ul>
                                
                                <h3>配置管理</h3>
                                <p><strong>Q: 如何优化性能配置？</strong></p>
                                <p>A: 使用配置工具：</p>
                                <ul>
                                    <li>运行 <code>python3 lightrek_config_tool.py</code></li>
                                    <li>选择适合的预设方案（高性能/平衡/兼容）</li>
                                    <li>所有参数都经过验证，确保有效</li>
                                    <li>可实时查看优化效果</li>
                                </ul>
                                

                                
                                <h3>技术支持</h3>
                                <p>如需更多帮助，请查看：</p>
                                <ul>
                                    <li>项目文档和README</li>
                                    <li>GitHub Issues</li>
                                    <li>用户社区讨论</li>
                                    <li>配置工具的在线帮助</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </body>
                </html>
                """
            
            def _serve_sources(self):
                sources = config_manager.get_all_sources()
                self._send_json(sources)

            def _serve_targets(self):
                targets = config_manager.get_all_targets()
                self._send_json(targets)

            def _serve_tasks(self):
                tasks = {}
                for task_id, task in task_manager.get_all_tasks().items():
                    tasks[task_id] = {
                        'name': task.name,
                        'description': task.description,
                        'source_id': task.source_id,
                        'target_id': task.target_id,
                        'status': getattr(task, 'last_status', 'idle'),
                        'last_run': getattr(task, 'last_run', None)
                    }
                self._send_json(tasks)
            
            def _serve_task_status(self):
                status = task_manager.get_all_task_status()
                self._send_json(status)
            
            def _serve_task_executions(self):
                """提供任务执行历史"""
                executions = db_manager.get_task_executions() if db_manager else []
                self._send_json(executions)
            
            def _serve_statistics(self):
                """提供统计信息"""
                try:
                    statistics = db_manager.get_task_statistics() if db_manager else {}
                    self._send_json(statistics)
                except Exception as e:
                    self._send_json({'error': str(e)})
            
            def _serve_optimization_config(self):
                """提供优化配置"""
                try:
                    optimization_settings = config_manager.get_optimization_settings()
                    if not optimization_settings:
                        # 返回默认配置
                        optimization_settings = {
                            "enabled": True,
                            "parallel_scanning": {"enabled": True, "max_workers": 5},
                            "cache_management": {"enabled": True, "cache_validity_minutes": 60, "max_cache_age_days": 7, "cache_database": "lightrek_cache.db"},

                            "streaming_processing": {"enabled": True, "batch_size": 5000}
                        }
                    self._send_json(optimization_settings)
                except Exception as e:
                    self._send_json({'error': str(e)})
            
            def _save_optimization_config(self, data):
                """保存优化配置"""
                try:
                    success = config_manager.update_optimization_settings(data)
                    if success:
                        self._send_json({'success': True, 'message': '配置保存成功'})
                    else:
                        self._send_json({'success': False, 'message': '配置保存失败'})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _add_source(self, data):
                try:
                    import uuid
                    source_id = str(uuid.uuid4())
                    storage_type = data.get('storage_type', 's3')

                    # 根据存储类型构建配置
                    source_config = {
                        'name': data.get('name', ''),
                        'description': data.get('description', '')
                    }

                    # 添加特定存储类型的配置字段
                    if storage_type == 's3':
                        source_config.update({
                            'access_key': data.get('access_key', ''),
                            'secret_key': data.get('secret_key', ''),
                            'endpoint': data.get('endpoint', ''),
                            'region': data.get('region', ''),
                            'bucket': data.get('bucket', '')
                        })
                    elif storage_type == 'sftp':
                        source_config.update({
                            'hostname': data.get('hostname', ''),
                            'port': int(data.get('port', 22)),
                            'username': data.get('username', ''),
                            'password': data.get('password', ''),
                            'root_path': data.get('root_path', '/')
                        })
                    elif storage_type == 'smb':
                        source_config.update({
                            'hostname': data.get('hostname', ''),
                            'share_name': data.get('share_name', ''),
                            'username': data.get('username', ''),
                            'password': data.get('password', ''),
                            'domain': data.get('domain', '')
                        })
                    elif storage_type == 'ftp':
                        source_config.update({
                            'hostname': data.get('hostname', ''),
                            'port': int(data.get('port', 21)),
                            'username': data.get('username', ''),
                            'password': data.get('password', ''),
                            'use_tls': data.get('use_tls', 'false') == 'true'
                        })
                    elif storage_type == 'local':
                        source_config.update({
                            'root_path': data.get('root_path', '')
                        })

                    if config_manager.add_source(source_id, storage_type, source_config):
                        self._send_json({'success': True, 'id': source_id})
                    else:
                        self._send_json({'success': False, 'message': '保存失败'})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _add_target(self, data):
                try:
                    import uuid
                    target_id = str(uuid.uuid4())
                    storage_type = data.get('storage_type', 's3')

                    # 根据存储类型构建配置
                    target_config = {
                        'name': data.get('name', ''),
                        'description': data.get('description', '')
                    }

                    # 添加特定存储类型的配置字段
                    if storage_type == 's3':
                        target_config.update({
                            'access_key': data.get('access_key', ''),
                            'secret_key': data.get('secret_key', ''),
                            'endpoint': data.get('endpoint', ''),
                            'region': data.get('region', ''),
                            'bucket': data.get('bucket', '')
                        })
                    elif storage_type == 'sftp':
                        target_config.update({
                            'hostname': data.get('hostname', ''),
                            'port': int(data.get('port', 22)),
                            'username': data.get('username', ''),
                            'password': data.get('password', ''),
                            'root_path': data.get('root_path', '/')
                        })
                    elif storage_type == 'smb':
                        target_config.update({
                            'hostname': data.get('hostname', ''),
                            'share_name': data.get('share_name', ''),
                            'username': data.get('username', ''),
                            'password': data.get('password', ''),
                            'domain': data.get('domain', '')
                        })
                    elif storage_type == 'ftp':
                        target_config.update({
                            'hostname': data.get('hostname', ''),
                            'port': int(data.get('port', 21)),
                            'username': data.get('username', ''),
                            'password': data.get('password', ''),
                            'use_tls': data.get('use_tls', 'false') == 'true'
                        })
                    elif storage_type == 'local':
                        target_config.update({
                            'root_path': data.get('root_path', '')
                        })

                    if config_manager.add_target(target_id, storage_type, target_config):
                        self._send_json({'success': True, 'id': target_id})
                    else:
                        self._send_json({'success': False, 'message': '保存失败'})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _add_task(self, data):
                try:
                    # 提取必需参数
                    name = data.get('name', '')
                    description = data.get('description', '')
                    source_id = data.get('source_id', '')
                    target_id = data.get('target_id', '')

                    # 提取可选参数
                    kwargs = {
                        'prefix': data.get('prefix', ''),
                        'sync_mode': data.get('sync_mode', 'incremental'),
                        'delete_extra': data.get('delete_extra', False),
                        'file_filter': data.get('file_filter', ''),
                        'exclude_filter': data.get('exclude_filter', ''),
                        'max_workers': int(data.get('max_workers', 20)),
                        'retry_times': int(data.get('retry_times', 5)),
                        'retry_delay': int(data.get('retry_delay', 3)),
                        'verify_integrity': data.get('verify_integrity', True),
                        'bandwidth_limit': int(data.get('bandwidth_limit', 0)),
                        'chunk_threshold': int(data.get('chunk_threshold', 100)),
                        'chunk_size': int(data.get('chunk_size', 10)),
                        'schedule_type': data.get('schedule_type', 'manual'),
                        'schedule_interval': int(data.get('schedule_interval', 1)),
                        'schedule_time': data.get('schedule_time', '00:00'),
                        'enabled': data.get('enabled', True)
                    }

                    task_id = task_manager.create_task(name, description, source_id, target_id, **kwargs)
                    self._send_json({'success': True, 'id': task_id})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _test_connection(self, data):
                try:
                    # 使用统一存储系统进行连接测试
                    storage_type = 's3'  # 默认为S3，后续可扩展
                    temp_id = 'temp_test'

                    # 创建临时存储配置进行测试
                    if config_manager.add_source(temp_id, storage_type, data):
                        success, message = config_manager.test_storage_connection(temp_id, is_source=True)
                        # 清理临时配置
                        config_manager.remove_source(temp_id)
                        self._send_json({'success': success, 'message': message})
                    else:
                        self._send_json({'success': False, 'message': '配置验证失败'})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _list_buckets(self, data):
                """列出存储桶"""
                try:
                    # 使用统一存储系统列出存储桶
                    storage_type = 's3'  # 默认为S3
                    temp_id = 'temp_list_buckets'

                    # 创建临时存储配置
                    if config_manager.add_source(temp_id, storage_type, data):
                        # 尝试获取存储桶列表（简化版本，返回空列表）
                        buckets = []  # 简化版本，后续可实现具体的列表功能
                        config_manager.remove_source(temp_id)
                        self._send_json({'success': True, 'buckets': buckets})
                    else:
                        self._send_json({'success': False, 'message': '配置验证失败', 'buckets': []})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'buckets': []})
            
            def _run_task(self, task_id):
                try:
                    if task_manager.run_task(task_id):
                        self._send_json({'success': True, 'message': '任务已启动'})
                    else:
                        self._send_json({'success': False, 'message': '任务启动失败'})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _stop_task(self, task_id):
                try:
                    if task_manager.stop_task(task_id):
                        self._send_json({'success': True, 'message': '任务已停止'})
                    else:
                        self._send_json({'success': False, 'message': '任务停止失败'})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _update_source(self, source_id, data):
                """更新数据源"""
                try:
                    source_config = {
                        'name': data.get('name', ''),
                        'description': data.get('description', ''),
                        'access_key': data.get('access_key', ''),
                        'secret_key': data.get('secret_key', ''),
                        'endpoint': data.get('endpoint', ''),
                        'region': data.get('region', ''),
                        'bucket': data.get('bucket', '')
                    }
                    
                    if config_manager.add_source(source_id, source_config):
                        self._send_json({'success': True})
                    else:
                        self._send_json({'success': False, 'message': '更新失败'})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _update_target(self, target_id, data):
                """更新目标"""
                try:
                    target_config = {
                        'name': data.get('name', ''),
                        'description': data.get('description', ''),
                        'access_key': data.get('access_key', ''),
                        'secret_key': data.get('secret_key', ''),
                        'endpoint': data.get('endpoint', ''),
                        'region': data.get('region', ''),
                        'bucket': data.get('bucket', '')
                    }
                    
                    if config_manager.add_target(target_id, target_config):
                        self._send_json({'success': True})
                    else:
                        self._send_json({'success': False, 'message': '更新失败'})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _update_task(self, task_id, data):
                """更新任务"""
                try:
                    task = task_manager.get_task(task_id)
                    if not task:
                        self._send_json({'success': False, 'message': '任务不存在'})
                        return

                    # 简化版本，使用task_manager的更新方法
                    success = task_manager.update_task(task_id, **data)
                    self._send_json({'success': success})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _delete_source(self, source_id):
                try:
                    success = config_manager.remove_source(source_id)
                    self._send_json({'success': success, 'message': '数据源删除成功' if success else '数据源不存在'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除失败: {str(e)}'})

            def _delete_target(self, target_id):
                try:
                    success = config_manager.remove_target(target_id)
                    self._send_json({'success': success, 'message': '目标删除成功' if success else '目标不存在'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除失败: {str(e)}'})

            def _delete_task(self, task_id):
                try:
                    success = task_manager.delete_task(task_id)
                    self._send_json({'success': success, 'message': '任务删除成功' if success else '任务不存在'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除失败: {str(e)}'})
            
            def _send_json(self, data):
                self.send_response(200)
                self.send_header('Content-Type', 'application/json; charset=utf-8')
                self.end_headers()
                self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
            
            def _serve_404(self):
                self.send_response(404)
                self.send_header('Content-Type', 'text/plain')
                self.end_headers()
                self.wfile.write(b'Not Found')
            
            def log_message(self, format, *args):
                pass
        
        return RequestHandler

def main():
    """主函数 - 简化版本用于测试"""
    print("LightRek 统一存储同步工具 - 完整Web界面")
    print("=" * 50)

    # 导入所有存储适配器以注册到工厂
    try:
        import s3_storage_adapter
        import sftp_storage_adapter
        import smb_storage_adapter
        import ftp_storage_adapter
        import local_storage_adapter
        print("存储适配器加载成功")
    except ImportError as e:
        print(f"部分存储适配器加载失败: {e}")

    # 初始化组件
    config_manager = UnifiedConfigManager()
    task_manager = UnifiedTaskManager(config_manager)

    # 启动Web界面
    web_interface = CompleteWebInterface(config_manager, task_manager, port=8001)
    web_interface.start()

    print("Web管理界面: http://localhost:8001")
    print("功能特性:")
    print("  - 多种存储类型支持 (S3, SFTP, SMB, FTP, 本地)")
    print("  - 统一配置管理")
    print("  - 任务创建和调度")
    print("  - 实时状态监控")
    print("按 Ctrl+C 退出程序")

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在停止程序...")
        web_interface.stop()
        print("程序已退出")

if __name__ == "__main__":
    main()