# 🚀 LightRek 统一存储同步系统

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20macOS%20%7C%20Linux-lightgrey.svg)](https://github.com/your-repo/lightrek-unified-sync)

一个高性能的统一存储同步工具，支持多种存储类型之间的智能数据迁移和同步。从原有的S3专用同步工具扩展为支持SFTP、SMB、FTP、本地文件系统等多种存储协议的通用同步解决方案。

## ✨ 核心特性

### 🌐 多协议支持
- **S3兼容存储** - AWS S3、阿里云OSS、腾讯云COS、MinIO等
- **SFTP** - SSH文件传输协议，支持密钥和密码认证
- **SMB/CIFS** - Windows网络共享，支持域认证
- **FTP/FTPS** - 文件传输协议，支持TLS加密
- **本地文件系统** - 本地磁盘、挂载的NAS设备

### 🚀 高性能优化
- **并行处理** - 多线程并发传输，提升同步速度
- **智能缓存** - 元数据缓存机制，避免重复扫描
- **分片传输** - 大文件自动分片，支持断点续传
- **带宽控制** - 可配置带宽限制，避免网络拥塞

### 🔧 灵活配置
- **多种同步模式** - 增量、全量、镜像同步
- **文件过滤** - 支持通配符和正则表达式过滤
- **定时任务** - 支持定时自动同步
- **完整性验证** - 文件哈希校验确保数据完整性

### 🎯 易于使用
- **统一接口** - 所有存储类型使用相同的API
- **Web管理界面** - 直观的配置和监控界面
- **详细日志** - 完整的操作日志和错误追踪
- **状态监控** - 实时任务状态和进度显示

## 📦 安装

### 基础安装
```bash
# 克隆仓库
git clone https://github.com/your-repo/lightrek-unified-sync.git
cd lightrek-unified-sync

# 安装基础依赖
pip install -r requirements_unified.txt
```

### 可选依赖
根据需要使用的存储类型安装对应依赖：

```bash
# SFTP支持
pip install paramiko

# SMB支持
pip install smbprotocol
# 或者
pip install pysmb

# 其他可选依赖
pip install python-magic tqdm colorlog
```

## 🚀 快速开始

### 1. 基本使用示例

```python
from unified_config_manager import UnifiedConfigManager
from unified_task_manager import UnifiedTaskManager

# 创建配置管理器
config_manager = UnifiedConfigManager()

# 配置S3源存储
s3_config = {
    'name': '阿里云OSS',
    'access_key': 'your_access_key',
    'secret_key': 'your_secret_key',
    'endpoint': 'https://oss-cn-hangzhou.aliyuncs.com',
    'region': 'cn-hangzhou',
    'bucket': 'source-bucket'
}
config_manager.add_source('s3_source', 's3', s3_config)

# 配置本地目标存储
local_config = {
    'name': '本地备份目录',
    'root_path': '/backup/data'
}
config_manager.add_target('local_target', 'local', local_config)

# 创建同步任务
task_manager = UnifiedTaskManager(config_manager)
task_id = task_manager.create_task(
    name="S3到本地同步",
    description="将S3数据同步到本地备份",
    source_id="s3_source",
    target_id="local_target",
    sync_mode="incremental"
)

# 启动同步
task_manager.start_task(task_id)
```

### 2. 支持的存储类型配置

#### S3兼容存储
```python
s3_config = {
    'name': 'AWS S3存储',
    'access_key': 'AKIAIOSFODNN7EXAMPLE',
    'secret_key': 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
    'endpoint': 'https://s3.amazonaws.com',
    'region': 'us-east-1',
    'bucket': 'my-bucket'
}
```

#### SFTP存储
```python
sftp_config = {
    'name': 'SFTP服务器',
    'hostname': 'sftp.example.com',
    'port': 22,
    'username': 'user',
    'password': 'password',  # 或使用私钥
    'private_key_path': '/path/to/private_key',
    'root_path': '/data'
}
```

#### SMB网络共享
```python
smb_config = {
    'name': 'Windows共享',
    'hostname': '*************',
    'username': 'domain\\user',
    'password': 'password',
    'share_name': 'SharedFolder',
    'root_path': '/backup'
}
```

#### FTP服务器
```python
ftp_config = {
    'name': 'FTP服务器',
    'hostname': 'ftp.example.com',
    'port': 21,
    'username': 'ftpuser',
    'password': 'ftppass',
    'use_tls': True,  # FTPS
    'passive_mode': True
}
```

#### 本地文件系统
```python
local_config = {
    'name': '本地存储',
    'root_path': '/data/storage'
}
```

## 🔧 高级配置

### 同步任务配置
```python
task_config = {
    'name': '高级同步任务',
    'description': '带有高级配置的同步任务',
    'source_id': 'source_storage',
    'target_id': 'target_storage',
    'prefix': 'data/',  # 只同步特定前缀的文件
    'sync_mode': 'incremental',  # incremental, full, mirror
    'max_workers': 10,  # 并发线程数
    'chunk_threshold': 100,  # 大文件阈值(MB)
    'chunk_size': 10,  # 分片大小(MB)
    'bandwidth_limit': 50,  # 带宽限制(MB/s)
    'verify_integrity': True,  # 完整性验证
    'file_filter': '*.jpg,*.png',  # 文件过滤
    'exclude_filter': '*.tmp,*.log',  # 排除文件
    'delete_extra': False,  # 是否删除目标多余文件
    'retry_times': 3,  # 重试次数
    'retry_delay': 5  # 重试延迟(秒)
}
```

### 定时任务
```python
# 每日定时同步
schedule_config = {
    'schedule_type': 'daily',
    'schedule_time': '02:00',  # 凌晨2点执行
    'enabled': True
}

# 每小时同步
schedule_config = {
    'schedule_type': 'hourly',
    'schedule_interval': 2,  # 每2小时
    'enabled': True
}
```

## 🌟 架构设计

### 存储抽象层
系统采用统一的存储抽象层设计，所有存储类型都实现相同的接口：

```python
class StorageAdapter(ABC):
    def test_connection(self) -> Tuple[bool, str]
    def list_files(self, prefix: str = "", max_keys: int = 1000) -> ListResult
    def get_file(self, key: str) -> Optional[bytes]
    def put_file(self, key: str, data: bytes) -> bool
    def delete_file(self, key: str) -> bool
    def get_file_metadata(self, key: str) -> Optional[FileMetadata]
    def file_exists(self, key: str) -> bool
```

### 工厂模式
使用工厂模式动态创建不同类型的存储适配器：

```python
# 自动根据配置类型创建适配器
adapter = StorageFactory.create_adapter(storage_config)
```

## 📊 性能优化

### 并行处理
- 多线程文件扫描
- 并发文件传输
- 异步I/O操作

### 智能缓存
- 文件元数据缓存
- 增量扫描优化
- 缓存失效检测

### 网络优化
- 连接池复用
- 分片并行传输
- 带宽自适应控制

## 🔍 监控和日志

### 实时状态监控
```python
# 获取任务状态
status = task_manager.get_task_status(task_id)
print(f"状态: {status['status']}")
print(f"进度: {status['progress']}%")
print(f"消息: {status['message']}")
```

### 详细日志
- 操作日志记录
- 错误追踪
- 性能统计
- 审计日志

## 🛠️ 开发和扩展

### 添加新的存储类型
1. 继承 `StorageAdapter` 基类
2. 实现所有抽象方法
3. 注册到 `StorageFactory`

```python
class CustomStorageAdapter(StorageAdapter):
    def __init__(self, config: CustomStorageConfig):
        super().__init__(config)
    
    def test_connection(self) -> Tuple[bool, str]:
        # 实现连接测试
        pass
    
    # 实现其他方法...

# 注册适配器
StorageFactory.register_adapter(StorageType.CUSTOM, CustomStorageAdapter)
```

## 📝 更新日志

### v2.0.0 (统一存储版本)
- ✨ 新增SFTP、SMB、FTP、本地文件系统支持
- 🔧 重构为统一存储抽象层架构
- 🚀 优化性能和并发处理
- 🎯 改进Web管理界面
- 📊 增强监控和日志功能

### v1.x.x (S3专用版本)
- 原有S3对象存储同步功能
- 基础的并行处理和缓存优化

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有贡献者和开源社区的支持！

---

**注意**: 这是从S3专用同步工具扩展而来的统一存储同步系统。原有的S3功能完全保留，同时新增了对多种存储协议的支持。
