#!/usr/bin/env python3
"""
LightRek S3同步工具 - Linux编译脚本（简化版）
支持本地编译当前架构版本
"""

import os
import sys
import platform
import subprocess
import shutil
import argparse
from pathlib import Path

def run_command(cmd, cwd=None):
    """执行命令并返回结果"""
    print(f"执行命令: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"命令执行失败: {result.stderr}")
            return False
        print(f"命令执行成功: {result.stdout}")
        return True
    except Exception as e:
        print(f"命令执行异常: {e}")
        return False

def check_system():
    """检查系统兼容性"""
    print("🔍 检查系统兼容性...")
    
    system = platform.system()
    if system != "Linux":
        print("❌ 此脚本仅支持Linux系统")
        return False
    
    print(f"✅ 系统: {system}")
    print(f"✅ 当前架构: {platform.machine()}")
    return True

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖包...")
    
    if not run_command("pip3 install -r requirements.txt"):
        print("❌ 依赖安装失败")
        return False
    
    if not run_command("pip3 install pyinstaller"):
        print("❌ PyInstaller安装失败")
        return False
    
    print("✅ 依赖安装成功")
    return True

def create_spec_file(arch):
    """创建PyInstaller规格文件"""
    print(f"📝 创建PyInstaller规格文件（{arch}）...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['start_lightrek.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('lightrek_config.json', '.'),
        ('lightrek logo 64px.png', '.'),
        ('lightrek logo 32px.png', '.'),
    ],
    hiddenimports=[
        'schedule',
        'sqlite3',
        'threading',
        'concurrent.futures',
        'xml.etree.ElementTree',
        'mimetypes',
        'fnmatch',
        'urllib.request',
        'urllib.parse',
        'urllib.error',
        'hashlib',
        'hmac',
        'base64',
        'time',
        'datetime',
        'json',
        'uuid',
        'logging',
        'dataclasses',
        'http.server',
        'socketserver'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='lightrek',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    spec_filename = f'lightrek_linux_{arch}.spec'
    with open(spec_filename, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"✅ 规格文件创建成功: {spec_filename}")
    return spec_filename

def build_executable(arch):
    """编译可执行文件"""
    print(f"🔨 开始编译 {arch} 版本...")
    
    spec_file = create_spec_file(arch)
    cmd = f"python3 -m PyInstaller --clean {spec_file}"
    
    if not run_command(cmd):
        print("❌ 编译失败")
        return False
    
    print("✅ 编译成功")
    return True

def verify_executable(exe_path, expected_arch):
    """验证可执行文件架构"""
    print(f"🔍 验证可执行文件架构: {exe_path}")
    
    if not Path(exe_path).exists():
        print(f"❌ 可执行文件不存在: {exe_path}")
        return False
    
    # 使用file命令检查架构
    cmd = f"file {exe_path}"
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        output = result.stdout.lower()
        print(f"文件信息: {result.stdout}")
        
        if expected_arch == "arm64" and ("aarch64" in output or "arm64" in output):
            print("✅ 确认为ARM64架构")
            return True
        elif expected_arch == "x64" and ("x86-64" in output or "x86_64" in output):
            print("✅ 确认为x86_64架构")
            return True
        else:
            print(f"⚠️  架构信息不匹配，期望: {expected_arch}")
            return True  # 继续处理，可能是检测问题
    else:
        print("❌ 无法验证文件架构")
        return False

def package_release(arch, exe_path):
    """打包发布文件"""
    print(f"📦 打包 {arch} 版本发布文件...")
    
    # 创建发布目录
    release_name = f"lightrek-linux-{arch}"
    release_dir = Path("releases") / release_name
    
    if release_dir.exists():
        shutil.rmtree(release_dir)
    
    release_dir.mkdir(parents=True, exist_ok=True)
    
    # 复制可执行文件
    if Path(exe_path).exists():
        shutil.copy2(exe_path, release_dir / "lightrek")
        os.chmod(release_dir / "lightrek", 0o755)
    else:
        print(f"❌ 可执行文件不存在: {exe_path}")
        return False
    
    # 复制配置文件和资源文件
    config_files = [
        "lightrek_config.json",
        "README.md",
        "lightrek logo 64px.png",
        "lightrek logo 32px.png"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            shutil.copy2(config_file, release_dir / config_file)
            print(f"✅ 复制文件: {config_file}")
        else:
            print(f"⚠️ 文件不存在: {config_file}")
    
    # 创建启动脚本
    start_script = release_dir / "start.sh"
    with open(start_script, 'w') as f:
        f.write(f'''#!/bin/bash
# LightRek S3同步工具启动脚本 (Linux {arch}版本)

echo "🚀 启动 LightRek S3同步工具..."
echo "平台: Linux {arch}"
echo "版本: v2.1.0"
echo ""

# 检查权限
if [ ! -x "./lightrek" ]; then
    echo "设置执行权限..."
    chmod +x ./lightrek
fi

# 启动程序
./lightrek
''')
    os.chmod(start_script, 0o755)
    
    # 创建专门的README
    readme_content = f"""# LightRek S3同步工具 - Linux {arch}版本

## 系统信息
- 平台: Linux
- 架构: {arch}
- 版本: v2.1.0

## 系统要求
- Linux内核 3.10+
- glibc 2.17+
- 64位系统

## 使用说明

### 启动程序
```bash
# 方式1: 使用启动脚本
./start.sh

# 方式2: 直接运行
./lightrek
```

### 功能特性
- ✅ 多云存储支持（阿里云OSS、腾讯云COS、AWS S3等）
- ✅ 可视化Web管理界面
- ✅ 多任务并发同步
- ✅ 增量/全量/镜像同步模式
- ✅ 大文件分片传输（>100MB）
- ✅ 定时任务调度
- ✅ 实时进度监控
- ✅ 详细日志记录
- ✅ 连接测试和存储桶自动发现

### 性能优化
- 🚀 并行扫描 - 提升文件扫描速度50%+
- 💾 智能缓存 - 二次扫描速度提升90%+
- 🌊 流式处理 - 大量文件分批处理
- 🔧 配置工具 - 独立的配置管理

### 访问地址
程序启动后，请访问: http://localhost:8001

### 配置文件
- `lightrek_config.json`: 主配置文件
- `lightrek_optimization_config.json`: 性能优化配置
- `lightrek_data.db`: SQLite数据库文件（自动创建）

### 注意事项
1. 确保网络连接正常
2. 配置正确的访问密钥和端点
3. 防火墙允许8001端口访问
4. 大文件传输需要足够的磁盘空间

### 技术支持
如有问题，请检查日志文件：`lightrek_sync.log`
"""

    with open(release_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ {arch} 版本发布包创建成功: {release_dir}")
    return True

def clean_build():
    """清理构建文件"""
    print("🧹 清理构建文件...")
    
    clean_dirs = ["build", "dist", "__pycache__"]
    clean_files = []
    
    # 清理spec文件
    for spec_file in Path(".").glob("lightrek_linux_*.spec"):
        clean_files.append(str(spec_file))
    
    for clean_dir in clean_dirs:
        if Path(clean_dir).exists():
            shutil.rmtree(clean_dir)
            print(f"删除目录: {clean_dir}")
    
    for clean_file in clean_files:
        if Path(clean_file).exists():
            os.remove(clean_file)
            print(f"删除文件: {clean_file}")
    
    print("✅ 清理完成")

def main():
    """主函数"""
    print("🚀 LightRek S3同步工具 - Linux编译脚本")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 检查系统兼容性
    if not check_system():
        sys.exit(1)
    
    # 检查必要文件
    required_files = ["start_lightrek.py", "lightrek_task_manager.py", "lightrek_database.py"]
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少必要文件: {file}")
            sys.exit(1)
    
    # 确定当前架构
    current_arch = platform.machine()
    if current_arch in ["x86_64", "amd64"]:
        arch = "x64"
    elif current_arch in ["aarch64", "arm64"]:
        arch = "arm64"
    else:
        print(f"❌ 不支持的架构: {current_arch}")
        sys.exit(1)
    
    print(f"📋 目标架构: {arch}")
    
    try:
        # 安装依赖
        if not install_dependencies():
            sys.exit(1)
        
        # 编译可执行文件
        if not build_executable(arch):
            sys.exit(1)
        
        # 验证架构
        exe_path = "dist/lightrek"
        if not verify_executable(exe_path, arch):
            sys.exit(1)
        
        # 打包发布
        if not package_release(arch, exe_path):
            sys.exit(1)
        
        print(f"\n🎉 Linux {arch} 版本编译完成！")
        print("📁 发布文件位于 releases/ 目录")
        
        # 创建压缩包
        print("📦 创建压缩包...")
        zip_path = f"releases/lightrek-linux-{arch}.tar.gz"
        cmd = f"cd releases && tar -czf lightrek-linux-{arch}.tar.gz lightrek-linux-{arch}/"
        if run_command(cmd):
            print(f"✅ 创建压缩包: {zip_path}")
        
    except KeyboardInterrupt:
        print("\n⚠️  编译被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 编译过程中发生错误: {e}")
        sys.exit(1)
    finally:
        # 清理构建文件
        clean_build()

if __name__ == "__main__":
    main() 