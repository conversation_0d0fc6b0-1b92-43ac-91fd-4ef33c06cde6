"""
统一任务管理器 - 支持多种存储类型的同步任务管理
"""

import threading
import time
import uuid
from dataclasses import dataclass
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import logging

from storage_abstraction import StorageFactory, StorageAdapter, FileMetadata
from unified_config_manager import UnifiedConfigManager


@dataclass
class UnifiedSyncTask:
    """统一同步任务配置"""
    task_id: str
    name: str
    description: str
    source_id: str
    target_id: str
    prefix: str = ""
    max_workers: int = 20
    retry_times: int = 5
    retry_delay: int = 3
    verify_integrity: bool = True
    incremental_sync: bool = True
    sync_mode: str = "incremental"  # incremental, full, mirror
    delete_extra: bool = False
    file_filter: str = ""
    exclude_filter: str = ""
    bandwidth_limit: int = 0  # MB/s
    chunk_threshold: int = 100  # MB
    chunk_size: int = 10  # MB
    schedule_type: str = "manual"
    schedule_interval: int = 1
    schedule_time: str = "00:00"
    enabled: bool = True
    created_at: str = ""
    updated_at: str = ""
    last_run: str = ""
    last_status: str = "未运行"


class UnifiedTaskManager:
    """统一任务管理器"""
    
    def __init__(self, config_manager: UnifiedConfigManager, db_manager=None):
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.running_tasks: Dict[str, threading.Thread] = {}
        self.task_status: Dict[str, Dict[str, Any]] = {}
        self.logger = logging.getLogger(__name__)
        
        # 设置日志
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def create_task(self, name: str, description: str, source_id: str, target_id: str, **kwargs) -> str:
        """创建同步任务"""
        task_id = str(uuid.uuid4())
        
        task_config = {
            'task_id': task_id,
            'name': name,
            'description': description,
            'source_id': source_id,
            'target_id': target_id,
            **kwargs
        }
        
        if self.config_manager.add_task(task_id, task_config):
            return task_id
        else:
            raise Exception("创建任务失败")
    
    def get_task(self, task_id: str) -> Optional[UnifiedSyncTask]:
        """获取任务配置"""
        task_config = self.config_manager.get_task(task_id)
        if task_config:
            return UnifiedSyncTask(**task_config)
        return None
    
    def get_all_tasks(self) -> Dict[str, UnifiedSyncTask]:
        """获取所有任务"""
        tasks = {}
        for task_id, task_config in self.config_manager.get_all_tasks().items():
            try:
                tasks[task_id] = UnifiedSyncTask(**task_config)
            except Exception as e:
                self.logger.error(f"解析任务配置失败 {task_id}: {e}")
        return tasks
    
    def update_task(self, task_id: str, **kwargs) -> bool:
        """更新任务配置"""
        return self.config_manager.update_task(task_id, kwargs)
    
    def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        # 如果任务正在运行，先停止
        if task_id in self.running_tasks:
            self.stop_task(task_id)
        
        return self.config_manager.remove_task(task_id)
    
    def start_task(self, task_id: str) -> bool:
        """启动同步任务"""
        if task_id in self.running_tasks:
            self.logger.warning(f"任务 {task_id} 已在运行中")
            return False
        
        task = self.get_task(task_id)
        if not task:
            self.logger.error(f"任务 {task_id} 不存在")
            return False
        
        # 验证源和目标配置
        source_config = self.config_manager.get_source(task.source_id)
        target_config = self.config_manager.get_target(task.target_id)
        
        if not source_config or not target_config:
            self.logger.error(f"任务 {task.name} 的源或目标配置不存在")
            return False
        
        # 创建并启动同步线程
        sync_thread = threading.Thread(
            target=self._run_sync_task,
            args=(task, source_config, target_config),
            daemon=True
        )
        
        self.running_tasks[task_id] = sync_thread
        self.task_status[task_id] = {
            'status': 'starting',
            'start_time': datetime.now().isoformat(),
            'progress': 0,
            'message': '正在启动...'
        }
        
        sync_thread.start()
        return True
    
    def stop_task(self, task_id: str) -> bool:
        """停止同步任务"""
        if task_id not in self.running_tasks:
            return False
        
        # 设置停止标志
        if task_id in self.task_status:
            self.task_status[task_id]['status'] = 'stopping'
            self.task_status[task_id]['message'] = '正在停止...'
        
        # 等待线程结束（最多等待10秒）
        thread = self.running_tasks[task_id]
        thread.join(timeout=10)
        
        # 清理
        if task_id in self.running_tasks:
            del self.running_tasks[task_id]
        
        if task_id in self.task_status:
            self.task_status[task_id]['status'] = 'stopped'
            self.task_status[task_id]['message'] = '已停止'
        
        return True
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        return self.task_status.get(task_id)
    
    def get_all_task_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务状态"""
        return self.task_status.copy()
    
    def _run_sync_task(self, task: UnifiedSyncTask, source_config, target_config):
        """运行同步任务"""
        task_id = task.task_id
        execution_id = str(uuid.uuid4())
        
        try:
            self.logger.info(f"开始执行任务: {task.name}")
            
            # 更新任务状态
            self.task_status[task_id].update({
                'status': 'running',
                'message': '正在连接存储...',
                'execution_id': execution_id
            })
            
            # 创建存储适配器
            source_adapter = StorageFactory.create_adapter(source_config)
            target_adapter = StorageFactory.create_adapter(target_config)
            
            # 测试连接
            source_ok, source_msg = source_adapter.test_connection()
            if not source_ok:
                raise Exception(f"源存储连接失败: {source_msg}")
            
            target_ok, target_msg = target_adapter.test_connection()
            if not target_ok:
                raise Exception(f"目标存储连接失败: {target_msg}")
            
            self.logger.info(f"存储连接成功 - 源: {source_msg}, 目标: {target_msg}")
            
            # 开始同步
            self._perform_sync(task, source_adapter, target_adapter, execution_id)
            
            # 任务完成
            self.task_status[task_id].update({
                'status': 'completed',
                'message': '同步完成',
                'end_time': datetime.now().isoformat(),
                'progress': 100
            })
            
            # 更新任务的最后运行时间
            self.config_manager.update_task(task_id, {
                'last_run': datetime.now().isoformat(),
                'last_status': '成功'
            })
            
            self.logger.info(f"任务 {task.name} 执行完成")
        
        except Exception as e:
            self.logger.error(f"任务 {task.name} 执行失败: {str(e)}")
            
            # 更新任务状态
            if task_id in self.task_status:
                self.task_status[task_id].update({
                    'status': 'failed',
                    'message': f'执行失败: {str(e)}',
                    'end_time': datetime.now().isoformat()
                })
            
            # 更新任务的最后运行状态
            self.config_manager.update_task(task_id, {
                'last_run': datetime.now().isoformat(),
                'last_status': f'失败: {str(e)}'
            })
        
        finally:
            # 清理运行中的任务记录
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
    
    def _perform_sync(self, task: UnifiedSyncTask, source_adapter: StorageAdapter, 
                     target_adapter: StorageAdapter, execution_id: str):
        """执行同步操作"""
        task_id = task.task_id
        
        # 更新状态
        self.task_status[task_id]['message'] = '正在扫描文件...'
        
        # 扫描源文件
        self.logger.info(f"开始扫描源文件，前缀: {task.prefix}")
        source_files = self._scan_all_files(source_adapter, task.prefix, task_id)

        self.logger.info(f"源文件扫描完成，共 {len(source_files)} 个文件")

        # 根据同步模式处理
        if task.sync_mode == "full":
            # 全量同步：重新上传所有文件
            files_to_sync = source_files
        else:
            # 增量同步：只同步有变化的文件
            self.task_status[task_id]['message'] = '正在扫描目标文件...'
            target_files = self._scan_all_files(target_adapter, task.prefix, task_id)

            self.task_status[task_id]['message'] = '正在比较文件差异...'
            files_to_sync = self._compare_files(source_files, target_files, task.verify_integrity)
        
        self.logger.info(f"需要同步 {len(files_to_sync)} 个文件")
        
        if not files_to_sync:
            self.logger.info("没有需要同步的文件")
            return
        
        # 执行文件同步
        self._sync_files(task, source_adapter, target_adapter, files_to_sync, execution_id)
    
    def _scan_all_files(self, adapter: StorageAdapter, prefix: str = "", task_id: str = None) -> List[FileMetadata]:
        """扫描所有文件，支持实时进度显示"""
        all_files = []
        continuation_token = None
        batch_count = 0

        while True:
            try:
                result = adapter.list_files(prefix=prefix, max_keys=1000, continuation_token=continuation_token)
                all_files.extend(result.files)
                batch_count += 1

                # 更新扫描进度
                if task_id and task_id in self.task_status:
                    files_count = len(all_files)
                    self.task_status[task_id]['message'] = f'正在扫描文件... 已发现 {files_count} 个文件 (批次 {batch_count})'
                    self.task_status[task_id]['files_scanned'] = files_count
                    self.logger.info(f"文件扫描进度: 已扫描 {files_count} 个文件")

                if not result.is_truncated:
                    break

                continuation_token = result.next_token
            except Exception as e:
                self.logger.error(f"扫描文件失败: {e}")
                break

        # 扫描完成
        if task_id and task_id in self.task_status:
            final_count = len(all_files)
            self.task_status[task_id]['message'] = f'文件扫描完成，共发现 {final_count} 个文件'
            self.logger.info(f"文件扫描完成: 总共 {final_count} 个文件")

        return all_files
    
    def _compare_files(self, source_files: List[FileMetadata], target_files: List[FileMetadata], 
                      verify_integrity: bool = True) -> List[FileMetadata]:
        """比较文件差异"""
        # 创建目标文件映射
        target_map = {f.key: f for f in target_files}
        
        files_to_sync = []
        
        for source_file in source_files:
            target_file = target_map.get(source_file.key)
            
            if not target_file:
                # 目标文件不存在
                files_to_sync.append(source_file)
            elif source_file.size != target_file.size:
                # 文件大小不同
                files_to_sync.append(source_file)
            elif verify_integrity and source_file.etag and target_file.etag:
                # 验证文件完整性（如果有ETag）
                if source_file.etag != target_file.etag:
                    files_to_sync.append(source_file)
            elif source_file.last_modified > target_file.last_modified:
                # 源文件更新
                files_to_sync.append(source_file)
        
        return files_to_sync
    
    def _sync_files(self, task: UnifiedSyncTask, source_adapter: StorageAdapter, 
                   target_adapter: StorageAdapter, files_to_sync: List[FileMetadata], execution_id: str):
        """同步文件"""
        task_id = task.task_id
        total_files = len(files_to_sync)
        completed_files = 0
        
        for i, file_meta in enumerate(files_to_sync):
            # 检查是否需要停止
            if task_id in self.task_status and self.task_status[task_id]['status'] == 'stopping':
                break
            
            try:
                # 更新进度
                progress = int((i / total_files) * 100)
                self.task_status[task_id].update({
                    'progress': progress,
                    'message': f'正在同步: {file_meta.key} ({i+1}/{total_files})'
                })
                
                # 下载文件
                file_data = source_adapter.get_file(file_meta.key)
                if file_data is None:
                    self.logger.warning(f"无法下载文件: {file_meta.key}")
                    continue
                
                # 上传文件
                if file_meta.size > task.chunk_threshold * 1024 * 1024:
                    # 大文件分片上传
                    success = target_adapter.put_file_chunked(
                        file_meta.key, file_data, task.chunk_size,
                        file_meta.content_type or 'binary/octet-stream'
                    )
                else:
                    # 普通上传
                    success = target_adapter.put_file(
                        file_meta.key, file_data,
                        file_meta.content_type or 'binary/octet-stream'
                    )
                
                if success:
                    completed_files += 1
                    self.logger.debug(f"文件同步成功: {file_meta.key}")
                else:
                    self.logger.warning(f"文件同步失败: {file_meta.key}")
                
                # 带宽限制
                if task.bandwidth_limit > 0:
                    time.sleep(file_meta.size / (task.bandwidth_limit * 1024 * 1024))
            
            except Exception as e:
                self.logger.error(f"同步文件 {file_meta.key} 时出错: {e}")
        
        self.logger.info(f"文件同步完成: {completed_files}/{total_files}")
