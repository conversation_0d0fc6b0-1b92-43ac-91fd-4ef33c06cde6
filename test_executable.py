#!/usr/bin/env python3
"""
可执行文件测试脚本
测试编译后的统一存储同步工具
"""

import os
import sys
import time
import tempfile
import shutil
import subprocess
import platform
import requests
from pathlib import Path


def find_executable():
    """查找可执行文件"""
    system = platform.system().lower()
    arch = platform.machine().lower()
    
    # 确定平台名称
    if system == "darwin":
        platform_name = "macos"
    elif system == "linux":
        platform_name = "linux"
    elif system == "windows":
        platform_name = "windows"
    else:
        platform_name = system
    
    # 确定架构名称
    if arch in ["x86_64", "amd64"]:
        arch_name = "x64"
    elif arch in ["arm64", "aarch64"]:
        arch_name = "arm64"
    else:
        arch_name = arch
    
    # 查找可执行文件
    release_name = f"lightrek-unified-{platform_name}-{arch_name}"
    release_dir = Path("releases") / release_name
    
    exe_name = "lightrek-unified.exe" if system == "windows" else "lightrek-unified"
    exe_path = release_dir / exe_name
    
    if exe_path.exists():
        return exe_path, release_dir
    else:
        print(f"❌ 可执行文件不存在: {exe_path}")
        return None, None


def test_executable_basic():
    """测试可执行文件基本功能"""
    print("🧪 测试可执行文件基本功能...")
    
    exe_path, release_dir = find_executable()
    if not exe_path:
        return False
    
    try:
        # 切换到发布目录
        original_cwd = os.getcwd()
        os.chdir(release_dir)
        
        # 启动程序（非阻塞）
        print(f"启动程序: {exe_path.name}")
        process = subprocess.Popen([f"./{exe_path.name}"], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        # 等待程序启动
        print("等待程序启动...")
        time.sleep(10)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 程序启动成功")
            
            # 测试Web界面
            success = test_web_interface()
            
            # 终止程序
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
            
            return success
        else:
            # 程序已退出，获取输出
            stdout, stderr = process.communicate()
            print(f"❌ 程序启动失败")
            print(f"标准输出: {stdout}")
            print(f"错误输出: {stderr}")
            return False
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
    finally:
        os.chdir(original_cwd)


def test_web_interface():
    """测试Web界面"""
    print("🌐 测试Web界面...")
    
    base_url = "http://localhost:8001"
    
    try:
        # 测试主页
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ Web界面主页访问成功")
        else:
            print(f"⚠️ Web界面主页返回状态码: {response.status_code}")
        
        # 测试API接口
        api_tests = [
            "/api/storage_types",
            "/api/storages",
            "/api/tasks"
        ]
        
        for api_path in api_tests:
            try:
                response = requests.get(f"{base_url}{api_path}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ API接口 {api_path} 访问成功")
                else:
                    print(f"⚠️ API接口 {api_path} 返回状态码: {response.status_code}")
            except Exception as e:
                print(f"⚠️ API接口 {api_path} 测试失败: {e}")
        
        return True
    
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Web界面，程序可能未正常启动")
        return False
    except Exception as e:
        print(f"❌ Web界面测试失败: {e}")
        return False


def test_config_functionality():
    """测试配置功能"""
    print("⚙️ 测试配置功能...")
    
    exe_path, release_dir = find_executable()
    if not exe_path:
        return False
    
    try:
        # 创建测试配置
        test_config = {
            "version": "2.0",
            "sources": {
                "test_local": {
                    "storage_type": "local",
                    "name": "测试本地源",
                    "root_path": str(tempfile.gettempdir())
                }
            },
            "targets": {
                "test_local_target": {
                    "storage_type": "local", 
                    "name": "测试本地目标",
                    "root_path": str(tempfile.gettempdir())
                }
            },
            "tasks": {},
            "global_settings": {
                "default_max_workers": 5
            }
        }
        
        # 保存测试配置
        import json
        config_file = release_dir / "lightrek_unified_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, indent=2, ensure_ascii=False)
        
        print("✅ 测试配置创建成功")
        return True
    
    except Exception as e:
        print(f"❌ 配置功能测试失败: {e}")
        return False


def test_file_operations():
    """测试文件操作功能"""
    print("📁 测试文件操作功能...")
    
    try:
        # 创建临时测试目录
        test_source = tempfile.mkdtemp(prefix="lightrek_test_source_")
        test_target = tempfile.mkdtemp(prefix="lightrek_test_target_")
        
        # 创建测试文件
        test_files = {
            "test1.txt": b"Hello World",
            "subdir/test2.txt": b"Test file content",
            "test3.bin": b"\x00\x01\x02\x03\x04\x05"
        }
        
        for file_path, content in test_files.items():
            full_path = Path(test_source) / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            with open(full_path, 'wb') as f:
                f.write(content)
        
        print(f"✅ 测试文件创建成功: {len(test_files)} 个文件")
        print(f"源目录: {test_source}")
        print(f"目标目录: {test_target}")
        
        # 清理
        shutil.rmtree(test_source, ignore_errors=True)
        shutil.rmtree(test_target, ignore_errors=True)
        
        return True
    
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        return False


def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始综合测试...")
    print("=" * 50)
    
    test_results = []
    
    # 测试1: 检查可执行文件
    print("\n📋 测试1: 检查可执行文件")
    exe_path, release_dir = find_executable()
    if exe_path:
        print(f"✅ 找到可执行文件: {exe_path}")
        test_results.append(("可执行文件检查", True))
    else:
        print("❌ 未找到可执行文件")
        test_results.append(("可执行文件检查", False))
        return test_results
    
    # 测试2: 配置功能
    print("\n📋 测试2: 配置功能")
    config_result = test_config_functionality()
    test_results.append(("配置功能", config_result))
    
    # 测试3: 文件操作
    print("\n📋 测试3: 文件操作")
    file_result = test_file_operations()
    test_results.append(("文件操作", file_result))
    
    # 测试4: 可执行文件基本功能
    print("\n📋 测试4: 可执行文件运行")
    exec_result = test_executable_basic()
    test_results.append(("可执行文件运行", exec_result))
    
    return test_results


def print_test_summary(test_results):
    """打印测试总结"""
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！可执行文件工作正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查问题")
        return False


def main():
    """主函数"""
    print("🧪 LightRek 统一存储同步工具 - 可执行文件测试")
    print("=" * 60)
    
    # 检查是否存在发布目录
    if not Path("releases").exists():
        print("❌ 未找到releases目录，请先运行编译脚本")
        sys.exit(1)
    
    try:
        # 运行综合测试
        test_results = run_comprehensive_test()
        
        # 打印测试总结
        success = print_test_summary(test_results)
        
        if success:
            print("\n🎯 测试建议:")
            print("1. 手动启动程序并访问 http://localhost:8001")
            print("2. 测试配置不同类型的存储")
            print("3. 创建并运行同步任务")
            print("4. 检查日志文件和错误处理")
        
        sys.exit(0 if success else 1)
    
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
