#!/usr/bin/env python3
"""
LightRek S3同步工具 - Linux标准编译脚本
使用标准Python镜像进行编译
"""

import os
import sys
import platform
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, cwd=None):
    """执行命令并返回结果"""
    print(f"执行命令: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd)
        if result.returncode != 0:
            print(f"命令执行失败")
            return False
        print(f"命令执行成功")
        return True
    except Exception as e:
        print(f"命令执行异常: {e}")
        return False

def create_spec_file():
    """创建PyInstaller规格文件"""
    print("📝 创建PyInstaller规格文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['start_lightrek.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('lightrek_config.json', '.'),
        ('lightrek logo 64px.png', '.'),
        ('lightrek logo 32px.png', '.'),
    ],
    hiddenimports=[
        'schedule',
        'sqlite3',
        'threading',
        'concurrent.futures',
        'xml.etree.ElementTree',
        'mimetypes',
        'fnmatch',
        'urllib.request',
        'urllib.parse',
        'urllib.error',
        'hashlib',
        'hmac',
        'base64',
        'time',
        'datetime',
        'json',
        'uuid',
        'logging',
        'dataclasses',
        'http.server',
        'socketserver'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='lightrek',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('lightrek_linux.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 规格文件创建成功")
    return True

def build_with_docker(arch):
    """使用Docker编译指定架构版本"""
    print(f"🔨 使用Docker编译 {arch} 版本...")
    
    # 使用标准Python镜像，通过platform参数指定架构
    platform_flag = "linux/arm64" if arch == "arm64" else "linux/amd64"
    
    # 创建Dockerfile
    dockerfile_content = f'''FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y gcc g++ binutils upx-ucl file && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY . .

# 安装Python依赖
RUN pip3 install --no-cache-dir -r requirements.txt
RUN pip3 install --no-cache-dir pyinstaller

# 编译
RUN python3 -m PyInstaller --clean lightrek_linux.spec

# 验证文件
RUN file dist/lightrek

CMD ["echo", "编译完成"]
'''
    
    dockerfile_path = f"Dockerfile.{arch}"
    with open(dockerfile_path, 'w') as f:
        f.write(dockerfile_content)
    
    # 构建镜像
    image_name = f"lightrek-builder-{arch}"
    build_cmd = f"docker build --platform {platform_flag} -f {dockerfile_path} -t {image_name} ."
    if not run_command(build_cmd):
        print("❌ Docker镜像构建失败")
        return False
    
    # 运行容器
    container_name = f"lightrek-build-{arch}"
    run_command(f"docker rm -f {container_name}")
    
    run_cmd = f"docker run --platform {platform_flag} --name {container_name} {image_name}"
    if not run_command(run_cmd):
        print("❌ Docker容器运行失败")
        return False
    
    # 创建输出目录
    os.makedirs("dist", exist_ok=True)
    
    # 复制编译结果
    copy_cmd = f"docker cp {container_name}:/app/dist/lightrek ./dist/lightrek_{arch}"
    if not run_command(copy_cmd):
        print("❌ 复制编译结果失败")
        return False
    
    # 清理容器
    run_command(f"docker rm {container_name}")
    
    print(f"✅ Docker编译 {arch} 版本成功")
    return True

def package_release(arch, exe_path):
    """打包发布文件"""
    print(f"📦 打包 {arch} 版本发布文件...")
    
    # 创建发布目录
    release_name = f"lightrek-linux-{arch}"
    release_dir = Path("releases") / release_name
    
    if release_dir.exists():
        shutil.rmtree(release_dir)
    
    release_dir.mkdir(parents=True, exist_ok=True)
    
    # 复制可执行文件
    if Path(exe_path).exists():
        shutil.copy2(exe_path, release_dir / "lightrek")
        os.chmod(release_dir / "lightrek", 0o755)
    else:
        print(f"❌ 可执行文件不存在: {exe_path}")
        return False
    
    # 复制配置文件和资源文件
    config_files = [
        "lightrek_config.json",
        "README.md",
        "lightrek logo 64px.png",
        "lightrek logo 32px.png"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            shutil.copy2(config_file, release_dir / config_file)
            print(f"✅ 复制文件: {config_file}")
        else:
            print(f"⚠️ 文件不存在: {config_file}")
    
    # 创建启动脚本
    start_script = release_dir / "start.sh"
    with open(start_script, 'w') as f:
        f.write(f'''#!/bin/bash
# LightRek S3同步工具启动脚本 (Linux {arch}版本)

echo "🚀 启动 LightRek S3同步工具..."
echo "平台: Linux {arch}"
echo "版本: v2.1.0"
echo ""

# 检查权限
if [ ! -x "./lightrek" ]; then
    echo "设置执行权限..."
    chmod +x ./lightrek
fi

# 启动程序
./lightrek
''')
    os.chmod(start_script, 0o755)
    
    # 创建README
    readme_content = f"""# LightRek S3同步工具 - Linux {arch}版本

## 系统信息
- 平台: Linux
- 架构: {arch}
- 版本: v2.1.0

## 系统要求
- Linux内核 3.10+
- glibc 2.17+
- 64位系统

## 使用说明

### 启动程序
```bash
# 方式1: 使用启动脚本
./start.sh

# 方式2: 直接运行
./lightrek
```

### 功能特性
- ✅ 多云存储支持（阿里云OSS、腾讯云COS、AWS S3等）
- ✅ 可视化Web管理界面
- ✅ 多任务并发同步
- ✅ 增量/全量/镜像同步模式
- ✅ 大文件分片传输（>100MB）
- ✅ 定时任务调度
- ✅ 实时进度监控
- ✅ 详细日志记录
- ✅ 连接测试和存储桶自动发现

### 性能优化
- 🚀 并行扫描 - 提升文件扫描速度50%+
- 💾 智能缓存 - 二次扫描速度提升90%+
- 🌊 流式处理 - 大量文件分批处理
- 🔧 配置工具 - 独立的配置管理

### 访问地址
程序启动后，请访问: http://localhost:8001

### 配置文件
- `lightrek_config.json`: 主配置文件
- `lightrek_optimization_config.json`: 性能优化配置
- `lightrek_data.db`: SQLite数据库文件（自动创建）

### 注意事项
1. 确保网络连接正常
2. 配置正确的访问密钥和端点
3. 防火墙允许8001端口访问
4. 大文件传输需要足够的磁盘空间

### 技术支持
如有问题，请检查日志文件：`lightrek_sync.log`
"""

    with open(release_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ {arch} 版本发布包创建成功: {release_dir}")
    return True

def main():
    """主函数"""
    print("🚀 LightRek S3同步工具 - Linux标准编译脚本")
    print("=" * 50)
    
    # 检查必要文件
    required_files = ["start_lightrek.py", "lightrek_task_manager.py", "lightrek_database.py"]
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少必要文件: {file}")
            sys.exit(1)
    
    # 创建spec文件
    if not create_spec_file():
        sys.exit(1)
    
    # 编译两个架构版本
    target_archs = ['arm64', 'x64']
    success_count = 0
    
    for arch in target_archs:
        print(f"\n🔨 开始编译 {arch} 版本...")
        
        if build_with_docker(arch):
            exe_path = f"dist/lightrek_{arch}"
            if package_release(arch, exe_path):
                success_count += 1
                print(f"✅ {arch} 版本编译成功")
            else:
                print(f"❌ {arch} 版本打包失败")
        else:
            print(f"❌ {arch} 版本编译失败")
    
    print(f"\n🎉 编译完成！成功编译 {success_count}/{len(target_archs)} 个版本")
    
    if success_count > 0:
        print("📁 发布文件位于 releases/ 目录")
        
        # 创建压缩包
        print("📦 创建压缩包...")
        for arch in target_archs:
            release_dir = Path("releases") / f"lightrek-linux-{arch}"
            if release_dir.exists():
                zip_path = f"releases/lightrek-linux-{arch}.tar.gz"
                cmd = f"cd releases && tar -czf lightrek-linux-{arch}.tar.gz lightrek-linux-{arch}/"
                if run_command(cmd):
                    print(f"✅ 创建压缩包: {zip_path}")
    
    # 清理构建文件
    clean_files = ["lightrek_linux.spec", "Dockerfile.arm64", "Dockerfile.x64"]
    clean_dirs = ["build", "dist", "__pycache__"]
    
    for clean_file in clean_files:
        if Path(clean_file).exists():
            os.remove(clean_file)
    
    for clean_dir in clean_dirs:
        if Path(clean_dir).exists():
            shutil.rmtree(clean_dir)
    
    print("🧹 清理完成")

if __name__ == "__main__":
    main() 