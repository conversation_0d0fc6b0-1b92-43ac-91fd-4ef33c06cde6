#!/usr/bin/env python3
"""
统一存储系统Web界面 - 基于原来的http.server实现，不依赖Flask
"""

import json
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from unified_config_manager import UnifiedConfigManager
from unified_task_manager import UnifiedTaskManager


class UnifiedWebInterface:
    """统一存储系统Web界面"""
    
    def __init__(self, config_manager: UnifiedConfigManager, task_manager: UnifiedTaskManager, port: int = 8001):
        self.config_manager = config_manager
        self.task_manager = task_manager
        self.port = port
        self.server = None
        self.server_thread = None
    
    def start(self):
        """启动Web服务器"""
        handler = self._create_handler()
        self.server = HTTPServer(('localhost', self.port), handler)
        self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
        self.server_thread.start()
        print(f"Web界面已启动: http://localhost:{self.port}")
    
    def stop(self):
        """停止Web服务器"""
        if self.server:
            self.server.shutdown()
    
    def _create_handler(self):
        """创建请求处理器"""
        config_manager = self.config_manager
        task_manager = self.task_manager
        
        class RequestHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/' or self.path == '/dashboard':
                    self._serve_dashboard()
                elif self.path == '/api/storage_types':
                    self._serve_storage_types()
                elif self.path == '/api/storages':
                    self._serve_storages()
                elif self.path == '/api/tasks':
                    self._serve_tasks()
                elif self.path == '/api/task_status':
                    self._serve_task_status()
                else:
                    self._serve_404()
            
            def do_POST(self):
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length)
                
                try:
                    data = json.loads(post_data.decode('utf-8')) if post_data else {}
                except:
                    data = {}
                
                if self.path == '/api/test_connection':
                    self._test_connection(data)
                elif self.path == '/api/save_storage':
                    self._save_storage(data)
                elif self.path == '/api/create_task':
                    self._create_task(data)
                elif self.path.startswith('/api/tasks/') and self.path.endswith('/start'):
                    task_id = self.path.split('/')[-2]
                    self._start_task(task_id)
                elif self.path.startswith('/api/tasks/') and self.path.endswith('/stop'):
                    task_id = self.path.split('/')[-2]
                    self._stop_task(task_id)
                else:
                    self._serve_404()
            
            def _serve_dashboard(self):
                """提供仪表盘页面"""
                html = self._get_dashboard_html()
                self.send_response(200)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _serve_storage_types(self):
                """提供支持的存储类型"""
                storage_types = config_manager.get_supported_storage_types()
                self._send_json(storage_types)
            
            def _serve_storages(self):
                """提供存储配置"""
                data = {
                    'sources': config_manager.get_all_sources(),
                    'targets': config_manager.get_all_targets()
                }
                self._send_json(data)
            
            def _serve_tasks(self):
                """提供任务列表"""
                tasks = {}
                for task_id, task in task_manager.get_all_tasks().items():
                    tasks[task_id] = {
                        'name': task.name,
                        'description': task.description,
                        'source_id': task.source_id,
                        'target_id': task.target_id,
                        'status': task.last_status,
                        'last_run': task.last_run
                    }
                self._send_json(tasks)
            
            def _serve_task_status(self):
                """提供任务状态"""
                status = task_manager.get_all_task_status()
                self._send_json(status)
            
            def _test_connection(self, data):
                """测试存储连接"""
                try:
                    storage_type = data.get('storage_type')
                    config_data = data.get('config')
                    
                    # 创建临时存储配置进行测试
                    temp_id = 'temp_test'
                    if config_manager.add_source(temp_id, storage_type, config_data):
                        success, message = config_manager.test_storage_connection(temp_id, is_source=True)
                        # 清理临时配置
                        config_manager.remove_source(temp_id)
                        self._send_json({'success': success, 'message': message})
                    else:
                        self._send_json({'success': False, 'message': '配置验证失败'})
                
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _save_storage(self, data):
                """保存存储配置"""
                try:
                    storage_id = data.get('storage_id')
                    storage_type = data.get('storage_type')
                    config_data = data.get('config')
                    is_source = data.get('is_source', True)
                    
                    if is_source:
                        success = config_manager.add_source(storage_id, storage_type, config_data)
                    else:
                        success = config_manager.add_target(storage_id, storage_type, config_data)
                    
                    self._send_json({'success': success})
                
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _create_task(self, data):
                """创建同步任务"""
                try:
                    task_id = task_manager.create_task(
                        name=data.get('name', ''),
                        description=data.get('description', ''),
                        source_id=data.get('source_id', ''),
                        target_id=data.get('target_id', ''),
                        **{k: v for k, v in data.items() if k not in ['name', 'description', 'source_id', 'target_id']}
                    )
                    self._send_json({'success': True, 'task_id': task_id})
                
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _start_task(self, task_id):
                """启动任务"""
                try:
                    success = task_manager.start_task(task_id)
                    self._send_json({'success': success})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _stop_task(self, task_id):
                """停止任务"""
                try:
                    success = task_manager.stop_task(task_id)
                    self._send_json({'success': success})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _get_dashboard_html(self):
                """获取仪表盘HTML"""
                return '''<!DOCTYPE html>
<html>
<head>
    <title>LightRek 统一存储同步工具</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; }
        button:hover { background: #0056b3; }
        .success { color: green; }
        .error { color: red; }
        .hidden { display: none; }
        .storage-config { margin-top: 20px; }
        .task-list { margin-top: 20px; }
        .task-item { padding: 10px; border: 1px solid #ddd; margin-bottom: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>LightRek 统一存储同步工具</h1>
            <p>支持 S3、SFTP、SMB、FTP、本地文件系统</p>
        </div>
        
        <div class="section">
            <h2>存储配置</h2>
            <div class="form-group">
                <label for="storage_type">存储类型:</label>
                <select id="storage_type" onchange="showStorageConfig()">
                    <option value="">请选择存储类型</option>
                    <option value="s3">S3对象存储</option>
                    <option value="sftp">SFTP</option>
                    <option value="smb">SMB/CIFS</option>
                    <option value="ftp">FTP/FTPS</option>
                    <option value="local">本地文件系统</option>
                </select>
            </div>
            
            <div id="storage_config" class="storage-config hidden">
                <div class="form-group">
                    <label for="storage_name">存储名称:</label>
                    <input type="text" id="storage_name" placeholder="存储名称">
                </div>
                <div class="form-group">
                    <label for="storage_desc">描述:</label>
                    <textarea id="storage_desc" placeholder="存储描述"></textarea>
                </div>
                <div id="config_fields"></div>
                <button onclick="testConnection()">测试连接</button>
                <button onclick="saveStorage()">保存配置</button>
            </div>
            
            <div id="result"></div>
        </div>
        
        <div class="section">
            <h2>同步任务</h2>
            <div class="form-group">
                <label for="task_name">任务名称:</label>
                <input type="text" id="task_name" placeholder="任务名称">
            </div>
            <div class="form-group">
                <label for="source_id">数据源:</label>
                <select id="source_id"></select>
            </div>
            <div class="form-group">
                <label for="target_id">目标存储:</label>
                <select id="target_id"></select>
            </div>
            <button onclick="createTask()">创建任务</button>
            
            <div class="task-list" id="task_list">
                <h3>任务列表</h3>
                <div id="tasks"></div>
            </div>
        </div>
    </div>
    
    <script>
        const storageConfigs = {
            's3': [
                {name: 'access_key', label: '访问密钥', type: 'text'},
                {name: 'secret_key', label: '密钥', type: 'password'},
                {name: 'endpoint', label: '端点URL', type: 'text'},
                {name: 'region', label: '区域', type: 'text'},
                {name: 'bucket', label: '存储桶', type: 'text'}
            ],
            'local': [
                {name: 'root_path', label: '根路径', type: 'text'}
            ],
            'sftp': [
                {name: 'hostname', label: '主机名', type: 'text'},
                {name: 'port', label: '端口', type: 'number', default: 22},
                {name: 'username', label: '用户名', type: 'text'},
                {name: 'password', label: '密码', type: 'password'},
                {name: 'root_path', label: '根路径', type: 'text', default: '/'}
            ]
        };
        
        function showStorageConfig() {
            const storageType = document.getElementById('storage_type').value;
            const configDiv = document.getElementById('storage_config');
            const fieldsDiv = document.getElementById('config_fields');
            
            if (storageType && storageConfigs[storageType]) {
                configDiv.classList.remove('hidden');
                fieldsDiv.innerHTML = '';
                
                storageConfigs[storageType].forEach(field => {
                    const div = document.createElement('div');
                    div.className = 'form-group';
                    div.innerHTML = `
                        <label for="${field.name}">${field.label}:</label>
                        <input type="${field.type}" id="${field.name}" placeholder="${field.label}" value="${field.default || ''}">
                    `;
                    fieldsDiv.appendChild(div);
                });
            } else {
                configDiv.classList.add('hidden');
            }
        }
        
        function testConnection() {
            const config = getStorageConfig();
            if (!config) return;
            
            fetch('/api/test_connection', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                const result = document.getElementById('result');
                if (data.success) {
                    result.innerHTML = '<p class="success">✓ 连接测试成功: ' + data.message + '</p>';
                } else {
                    result.innerHTML = '<p class="error">✗ 连接测试失败: ' + data.message + '</p>';
                }
            });
        }
        
        function saveStorage() {
            const config = getStorageConfig();
            if (!config) return;
            
            const storageId = prompt('请输入存储ID:');
            if (!storageId) return;
            
            const isSource = confirm('是否作为数据源？(取消=目标存储)');
            
            fetch('/api/save_storage', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    storage_id: storageId,
                    storage_type: config.storage_type,
                    config: config.config,
                    is_source: isSource
                })
            })
            .then(response => response.json())
            .then(data => {
                const result = document.getElementById('result');
                if (data.success) {
                    result.innerHTML = '<p class="success">✓ 配置保存成功</p>';
                    loadStorages();
                } else {
                    result.innerHTML = '<p class="error">✗ 配置保存失败: ' + (data.message || '')</p>';
                }
            });
        }
        
        function getStorageConfig() {
            const storageType = document.getElementById('storage_type').value;
            if (!storageType) {
                alert('请选择存储类型');
                return null;
            }
            
            const config = {
                name: document.getElementById('storage_name').value,
                description: document.getElementById('storage_desc').value
            };
            
            if (storageConfigs[storageType]) {
                storageConfigs[storageType].forEach(field => {
                    const element = document.getElementById(field.name);
                    if (element) {
                        config[field.name] = element.value;
                    }
                });
            }
            
            return {storage_type: storageType, config: config};
        }
        
        function loadStorages() {
            fetch('/api/storages')
            .then(response => response.json())
            .then(data => {
                const sourceSelect = document.getElementById('source_id');
                const targetSelect = document.getElementById('target_id');
                
                sourceSelect.innerHTML = '<option value="">请选择数据源</option>';
                targetSelect.innerHTML = '<option value="">请选择目标存储</option>';
                
                Object.keys(data.sources || {}).forEach(id => {
                    const option = document.createElement('option');
                    option.value = id;
                    option.textContent = data.sources[id].name || id;
                    sourceSelect.appendChild(option);
                });
                
                Object.keys(data.targets || {}).forEach(id => {
                    const option = document.createElement('option');
                    option.value = id;
                    option.textContent = data.targets[id].name || id;
                    targetSelect.appendChild(option);
                });
            });
        }
        
        function createTask() {
            const taskName = document.getElementById('task_name').value;
            const sourceId = document.getElementById('source_id').value;
            const targetId = document.getElementById('target_id').value;
            
            if (!taskName || !sourceId || !targetId) {
                alert('请填写完整的任务信息');
                return;
            }
            
            fetch('/api/create_task', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    name: taskName,
                    description: '通过Web界面创建的任务',
                    source_id: sourceId,
                    target_id: targetId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('任务创建成功');
                    loadTasks();
                } else {
                    alert('任务创建失败: ' + (data.message || ''));
                }
            });
        }
        
        function loadTasks() {
            fetch('/api/tasks')
            .then(response => response.json())
            .then(data => {
                const tasksDiv = document.getElementById('tasks');
                tasksDiv.innerHTML = '';
                
                Object.keys(data).forEach(taskId => {
                    const task = data[taskId];
                    const div = document.createElement('div');
                    div.className = 'task-item';
                    div.innerHTML = `
                        <h4>${task.name}</h4>
                        <p>${task.description}</p>
                        <p>源: ${task.source_id} → 目标: ${task.target_id}</p>
                        <p>状态: ${task.status}</p>
                        <button onclick="startTask('${taskId}')">启动</button>
                        <button onclick="stopTask('${taskId}')">停止</button>
                    `;
                    tasksDiv.appendChild(div);
                });
            });
        }
        
        function startTask(taskId) {
            fetch(`/api/tasks/${taskId}/start`, {method: 'POST'})
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('任务启动成功');
                } else {
                    alert('任务启动失败: ' + (data.message || ''));
                }
            });
        }
        
        function stopTask(taskId) {
            fetch(`/api/tasks/${taskId}/stop`, {method: 'POST'})
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('任务停止成功');
                } else {
                    alert('任务停止失败: ' + (data.message || ''));
                }
            });
        }
        
        // 页面加载时初始化
        window.onload = function() {
            loadStorages();
            loadTasks();
        };
    </script>
</body>
</html>'''
            
            def _send_json(self, data):
                self.send_response(200)
                self.send_header('Content-Type', 'application/json; charset=utf-8')
                self.end_headers()
                self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
            
            def _serve_404(self):
                self.send_response(404)
                self.send_header('Content-Type', 'text/plain')
                self.end_headers()
                self.wfile.write(b'Not Found')
            
            def log_message(self, format, *args):
                pass
        
        return RequestHandler
