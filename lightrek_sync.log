2025-06-16 15:12:23,090 - INFO - 启动任务: 每日备份任务
2025-06-16 15:12:23,299 - ERROR - 任务 每日备份任务 失败: 源连接失败: 存储桶不存在或无权限访问
2025-06-16 15:24:49,095 - INFO - 启动任务: 每日备份任务
2025-06-16 15:24:49,412 - ERROR - 任务 每日备份任务 失败: 源连接失败: 存储桶 'jayce-s3test' 不存在或无权限访问
2025-06-16 16:17:50,836 - INFO - 定时调度器开始运行...
2025-06-16 16:17:50,837 - INFO - 设置定时任务: 测试定时任务, 类型: daily, 间隔: 1, 时间: 16:19
2025-06-16 16:17:50,837 - INFO - 每日任务已设置: Job(interval=1, unit=days, do=_run_scheduled_task, args=('e5c55fc7-2b83-450d-9ad8-0087fd566573',), kwargs={}), 执行时间: 16:19
2025-06-16 16:17:50,837 - INFO - 任务 测试定时任务 的调度作业: [Every 1 day at 16:19:00 do _run_scheduled_task('e5c55fc7-2b83-450d-9ad8-0087fd566573') (last run: [never], next run: 2025-06-16 16:19:00)]
2025-06-16 16:17:50,837 - INFO - 创建任务: 测试定时任务 (e5c55fc7-2b83-450d-9ad8-0087fd566573)
2025-06-16 16:20:55,019 - INFO - 定时调度器开始运行...
2025-06-16 16:20:55,019 - INFO - 设置定时任务: 每日备份任务, 类型: daily, 间隔: 1, 时间: 02:00
2025-06-16 16:20:55,020 - INFO - 每日任务已设置: Job(interval=1, unit=days, do=_run_scheduled_task, args=('task1',), kwargs={}), 执行时间: 02:00
2025-06-16 16:20:55,020 - INFO - 任务 每日备份任务 的调度作业: [Every 1 day at 02:00:00 do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-17 02:00:00)]
2025-06-16 16:20:55,020 - INFO - 重新加载任务调度: 每日备份任务
2025-06-16 16:21:10,989 - INFO - 定时调度器开始运行...
2025-06-16 16:21:10,989 - INFO - 设置定时任务: 每日备份任务, 类型: daily, 间隔: 1, 时间: 02:00
2025-06-16 16:21:10,990 - INFO - 每日任务已设置: Job(interval=1, unit=days, do=_run_scheduled_task, args=('task1',), kwargs={}), 执行时间: 02:00
2025-06-16 16:21:10,990 - INFO - 任务 每日备份任务 的调度作业: [Every 1 day at 02:00:00 do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-17 02:00:00)]
2025-06-16 16:21:10,990 - INFO - 重新加载任务调度: 每日备份任务
2025-06-16 16:21:55,638 - INFO - 设置定时任务: 每日备份任务, 类型: daily, 间隔: 1, 时间: 16:22
2025-06-16 16:21:55,639 - INFO - 每日任务已设置: Job(interval=1, unit=days, do=_run_scheduled_task, args=('task1',), kwargs={}), 执行时间: 16:22
2025-06-16 16:21:55,639 - INFO - 任务 每日备份任务 的调度作业: [Every 1 day at 16:22:00 do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-16 16:22:00)]
2025-06-16 16:22:10,994 - INFO - 定时任务触发: task1
2025-06-16 16:22:10,994 - INFO - 开始执行定时任务: task1
2025-06-16 16:22:10,996 - INFO - 启动任务: 每日备份任务
2025-06-16 16:22:11,650 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-16 16:22:11,889 - INFO - 任务 每日备份任务 需要同步 0 个文件，总大小 0 B
2025-06-16 16:22:11,895 - INFO - 任务 每日备份任务 完成: 成功 0, 失败 0, 传输 0 B
2025-06-16 16:24:35,193 - INFO - 启动任务: 每日备份任务
2025-06-16 16:24:35,655 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-16 16:24:36,100 - INFO - 任务 每日备份任务 需要同步 1 个文件，总大小 8.09 MB
2025-06-16 16:24:37,426 - INFO - 任务 每日备份任务 完成: 成功 1, 失败 0, 传输 8.09 MB
2025-06-16 17:51:34,972 - INFO - 定时调度器开始运行...
2025-06-16 17:51:34,972 - INFO - 设置定时任务: 每日备份任务, 类型: daily, 间隔: 1, 时间: 16:22
2025-06-16 17:51:34,972 - INFO - 每日任务已设置: Job(interval=1, unit=days, do=_run_scheduled_task, args=('task1',), kwargs={}), 执行时间: 16:22
2025-06-16 17:51:34,972 - INFO - 任务 每日备份任务 的调度作业: [Every 1 day at 16:22:00 do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-17 16:22:00)]
2025-06-16 17:51:34,972 - INFO - 重新加载任务调度: 每日备份任务
2025-06-16 17:56:33,239 - INFO - 定时调度器开始运行...
2025-06-16 17:56:33,239 - INFO - 设置定时任务: 每日备份任务, 类型: daily, 间隔: 1, 时间: 16:22
2025-06-16 17:56:33,240 - INFO - 每日任务已设置: Job(interval=1, unit=days, do=_run_scheduled_task, args=('task1',), kwargs={}), 执行时间: 16:22
2025-06-16 17:56:33,240 - INFO - 任务 每日备份任务 的调度作业: [Every 1 day at 16:22:00 do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-17 16:22:00)]
2025-06-16 17:56:33,240 - INFO - 重新加载任务调度: 每日备份任务
2025-06-16 18:51:43,615 - INFO - 定时调度器开始运行...
2025-06-16 18:51:43,615 - INFO - 设置定时任务: 每日备份任务, 类型: daily, 间隔: 1, 时间: 16:22
2025-06-16 18:51:43,616 - INFO - 每日任务已设置: Job(interval=1, unit=days, do=_run_scheduled_task, args=('task1',), kwargs={}), 执行时间: 16:22
2025-06-16 18:51:43,616 - INFO - 任务 每日备份任务 的调度作业: [Every 1 day at 16:22:00 do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-17 16:22:00)]
2025-06-16 18:51:43,616 - INFO - 重新加载任务调度: 每日备份任务
2025-06-16 18:59:25,016 - INFO - 定时调度器开始运行...
2025-06-16 18:59:25,016 - INFO - 设置定时任务: 每日备份任务, 类型: daily, 间隔: 1, 时间: 16:22
2025-06-16 18:59:25,016 - INFO - 每日任务已设置: Job(interval=1, unit=days, do=_run_scheduled_task, args=('task1',), kwargs={}), 执行时间: 16:22
2025-06-16 18:59:25,016 - INFO - 任务 每日备份任务 的调度作业: [Every 1 day at 16:22:00 do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-17 16:22:00)]
2025-06-16 18:59:25,016 - INFO - 重新加载任务调度: 每日备份任务
2025-06-16 19:12:39,949 - INFO - 定时调度器开始运行...
2025-06-16 19:12:39,949 - INFO - 设置定时任务: 每日备份任务, 类型: daily, 间隔: 1, 时间: 16:22
2025-06-16 19:12:39,949 - INFO - 每日任务已设置: Job(interval=1, unit=days, do=_run_scheduled_task, args=('task1',), kwargs={}), 执行时间: 16:22
2025-06-16 19:12:39,949 - INFO - 任务 每日备份任务 的调度作业: [Every 1 day at 16:22:00 do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-17 16:22:00)]
2025-06-16 19:12:39,949 - INFO - 重新加载任务调度: 每日备份任务
2025-06-16 19:29:55,249 - INFO - 定时调度器开始运行...
2025-06-16 19:29:55,249 - INFO - 设置定时任务: 每日备份任务, 类型: daily, 间隔: 1, 时间: 16:22
2025-06-16 19:29:55,251 - INFO - 每日任务已设置: Job(interval=1, unit=days, do=_run_scheduled_task, args=('task1',), kwargs={}), 执行时间: 16:22
2025-06-16 19:29:55,251 - INFO - 任务 每日备份任务 的调度作业: [Every 1 day at 16:22:00 do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-17 16:22:00)]
2025-06-16 19:29:55,251 - INFO - 重新加载任务调度: 每日备份任务
2025-06-16 19:32:48,193 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-16 19:41:36,698 - INFO - 定时调度器开始运行...
2025-06-16 19:41:36,698 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-16 19:41:36,699 - INFO - 重新加载任务调度: 每日备份任务
2025-06-16 20:50:23,669 - INFO - 定时调度器开始运行...
2025-06-16 20:50:23,669 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-16 20:50:23,669 - INFO - 重新加载任务调度: 每日备份任务
2025-06-16 20:51:37,181 - INFO - 启动任务: 每日备份任务
2025-06-16 20:51:37,759 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-16 20:51:38,027 - INFO - 任务 每日备份任务 需要同步 0 个文件，跳过 6 个文件，总大小 0 B
2025-06-16 20:51:38,035 - INFO - 任务 每日备份任务 完成: 成功 0, 失败 0, 传输 0 B
2025-06-16 21:48:38,509 - INFO - 定时调度器开始运行...
2025-06-16 21:48:38,509 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-16 21:48:38,509 - INFO - 重新加载任务调度: 每日备份任务
2025-06-16 21:52:51,440 - INFO - 定时调度器开始运行...
2025-06-16 21:52:51,440 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-16 21:52:51,441 - INFO - 重新加载任务调度: 每日备份任务
2025-06-16 21:55:50,174 - INFO - 定时调度器开始运行...
2025-06-16 21:55:50,174 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-16 21:55:50,174 - INFO - 重新加载任务调度: 每日备份任务
2025-06-16 22:04:42,580 - INFO - 定时调度器开始运行...
2025-06-16 22:04:42,580 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-16 22:04:42,581 - INFO - 重新加载任务调度: 每日备份任务
2025-06-17 11:01:18,706 - INFO - 定时调度器开始运行...
2025-06-17 11:01:18,707 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-17 11:01:18,707 - INFO - 重新加载任务调度: 每日备份任务
2025-06-17 11:03:49,044 - INFO - 定时调度器开始运行...
2025-06-17 11:03:49,044 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-17 11:03:49,045 - INFO - 重新加载任务调度: 每日备份任务
2025-06-17 11:07:45,779 - INFO - 定时调度器开始运行...
2025-06-17 11:07:45,779 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-17 11:07:45,779 - INFO - 重新加载任务调度: 每日备份任务
2025-06-17 11:09:14,167 - INFO - 定时调度器开始运行...
2025-06-17 11:09:14,167 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-17 11:09:14,167 - INFO - 重新加载任务调度: 每日备份任务
2025-06-17 11:15:11,738 - INFO - 定时调度器开始运行...
2025-06-17 11:15:11,738 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-17 11:15:11,738 - INFO - 重新加载任务调度: 每日备份任务
2025-06-17 12:32:30,941 - INFO - 定时调度器开始运行...
2025-06-17 12:32:30,941 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-17 12:32:30,941 - INFO - 重新加载任务调度: 每日备份任务
2025-06-17 13:00:59,138 - INFO - 定时调度器开始运行...
2025-06-17 13:00:59,138 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-17 13:00:59,139 - INFO - 重新加载任务调度: 每日备份任务
2025-06-17 13:05:01,314 - INFO - 定时调度器开始运行...
2025-06-17 13:05:01,314 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-17 13:05:01,314 - INFO - 重新加载任务调度: 每日备份任务
2025-06-17 13:16:08,215 - INFO - 定时调度器开始运行...
2025-06-17 13:16:08,215 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-17 13:16:08,215 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 10:26:21,554 - INFO - 定时调度器开始运行...
2025-06-18 10:26:21,554 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 10:26:21,554 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 10:29:20,290 - INFO - 定时调度器开始运行...
2025-06-18 10:29:20,291 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 10:29:20,291 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 10:30:21,374 - INFO - 定时调度器开始运行...
2025-06-18 10:30:21,374 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 10:30:21,374 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 10:30:21,376 - INFO - 启动任务: 每日备份任务
2025-06-18 10:30:26,721 - INFO - 定时调度器开始运行...
2025-06-18 10:30:26,721 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 10:30:26,721 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 10:48:51,696 - INFO - 定时调度器开始运行...
2025-06-18 10:48:51,696 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 10:48:51,696 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 10:55:57,675 - INFO - 定时调度器开始运行...
2025-06-18 10:55:57,675 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 10:55:57,675 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 10:58:55,085 - INFO - 定时调度器开始运行...
2025-06-18 10:58:55,085 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 10:58:55,085 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 11:05:55,607 - INFO - 定时调度器开始运行...
2025-06-18 11:05:55,607 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 11:05:55,607 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:19:38,958 - INFO - 定时调度器开始运行...
2025-06-18 12:19:38,961 - INFO - 已启用同步优化功能：并行扫描 + 时间戳预过滤 + 元数据缓存
2025-06-18 12:19:38,961 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:19:38,961 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:30:26,404 - INFO - 定时调度器开始运行...
2025-06-18 12:30:26,404 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:30:26,404 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:30:26,404 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:32:14,407 - INFO - 定时调度器开始运行...
2025-06-18 12:32:14,407 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:32:14,407 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:32:14,407 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:32:44,840 - INFO - 启动任务: 每日备份任务
2025-06-18 12:32:45,383 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 12:32:45,392 - ERROR - 任务 每日备份任务 失败: local variable 'time' referenced before assignment
2025-06-18 12:33:15,553 - INFO - 定时调度器开始运行...
2025-06-18 12:33:15,553 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:33:15,553 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:33:15,553 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:34:50,823 - INFO - 定时调度器开始运行...
2025-06-18 12:34:50,824 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:34:50,824 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:34:50,824 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:34:50,824 - INFO - 启动任务: 每日备份任务
2025-06-18 12:35:10,541 - INFO - 启动任务: 每日备份任务
2025-06-18 12:35:11,098 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 12:35:11,106 - ERROR - 任务 每日备份任务 失败: local variable 'time' referenced before assignment
2025-06-18 12:35:36,690 - INFO - 定时调度器开始运行...
2025-06-18 12:35:36,691 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:35:36,691 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:35:36,691 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:35:45,091 - INFO - 启动任务: 每日备份任务
2025-06-18 12:35:45,523 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 12:35:45,533 - ERROR - 任务 每日备份任务 失败: local variable 'time' referenced before assignment
2025-06-18 12:48:57,379 - INFO - 定时调度器开始运行...
2025-06-18 12:48:57,380 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:48:57,380 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:48:57,380 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:49:04,932 - INFO - 启动任务: 每日备份任务
2025-06-18 12:49:05,571 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 12:49:05,581 - ERROR - 任务 每日备份任务 失败: local variable 'time' referenced before assignment
2025-06-18 12:50:23,073 - INFO - 定时调度器开始运行...
2025-06-18 12:50:23,073 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:50:23,073 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:50:23,073 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:50:23,074 - INFO - 启动任务: 每日备份任务
2025-06-18 12:50:44,461 - INFO - 定时调度器开始运行...
2025-06-18 12:50:44,462 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:50:44,462 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:50:44,462 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:50:52,806 - INFO - 定时调度器开始运行...
2025-06-18 12:50:52,807 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:50:52,807 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:50:52,807 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:51:03,345 - INFO - 启动任务: 每日备份任务
2025-06-18 12:51:03,880 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 12:51:03,898 - INFO - 任务 每日备份任务 扫描完成 - 耗时: 0.01秒, 需要同步: 1, 跳过: 19, 总大小: 287.0 B
2025-06-18 12:51:19,592 - INFO - 任务 每日备份任务 完成: 成功 0, 失败 1, 传输 0 B
2025-06-18 12:52:24,761 - INFO - 定时调度器开始运行...
2025-06-18 12:52:24,761 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:52:24,761 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:52:24,761 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:54:45,245 - INFO - 定时调度器开始运行...
2025-06-18 12:54:45,245 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:54:45,245 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:54:45,245 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:55:14,057 - INFO - 定时调度器开始运行...
2025-06-18 12:55:14,058 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:55:14,058 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:55:14,058 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:55:43,079 - INFO - 定时调度器开始运行...
2025-06-18 12:55:43,079 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:55:43,079 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:55:43,079 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:56:30,503 - INFO - 定时调度器开始运行...
2025-06-18 12:56:30,504 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:56:30,504 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:56:30,504 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:56:40,157 - INFO - 启动任务: 每日备份任务
2025-06-18 12:56:40,696 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 12:56:40,791 - INFO - 任务 每日备份任务 扫描完成 - 耗时: 0.08秒, 需要同步: 6, 跳过: 19, 总大小: 4.26 MB
2025-06-18 12:56:41,585 - INFO - 任务 每日备份任务 完成: 成功 6, 失败 0, 传输 4.26 MB
2025-06-18 12:59:08,138 - INFO - 定时调度器开始运行...
2025-06-18 12:59:08,138 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 12:59:08,138 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=daily
2025-06-18 12:59:08,138 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 12:59:15,254 - INFO - 启动任务: 每日备份任务
2025-06-18 12:59:15,723 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 12:59:15,837 - INFO - 任务 每日备份任务 扫描完成 - 耗时: 0.10秒, 需要同步: 6, 跳过: 19, 总大小: 4.26 MB
2025-06-18 12:59:16,576 - INFO - 任务 每日备份任务 完成: 成功 6, 失败 0, 传输 4.26 MB
2025-06-18 12:59:37,957 - INFO - 任务 每日备份任务 不需要调度: enabled=False, schedule_type=minutely
2025-06-18 12:59:43,006 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 12:59:43,006 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 12:59:43,007 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 13:09:43)]
2025-06-18 13:10:08,244 - INFO - 定时任务触发: task1
2025-06-18 13:10:08,251 - INFO - 开始执行定时任务: task1
2025-06-18 13:10:08,253 - INFO - 启动任务: 每日备份任务
2025-06-18 13:10:08,889 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 13:10:09,001 - INFO - 任务 每日备份任务 扫描完成 - 耗时: 0.09秒, 需要同步: 6, 跳过: 19, 总大小: 4.26 MB
2025-06-18 13:10:09,576 - INFO - 任务 每日备份任务 完成: 成功 6, 失败 0, 传输 4.26 MB
2025-06-18 13:20:08,385 - INFO - 定时任务触发: task1
2025-06-18 13:20:08,392 - INFO - 开始执行定时任务: task1
2025-06-18 13:20:08,392 - INFO - 启动任务: 每日备份任务
2025-06-18 13:20:08,868 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 13:20:08,984 - INFO - 任务 每日备份任务 扫描完成 - 耗时: 0.11秒, 需要同步: 0, 跳过: 19, 总大小: 0 B
2025-06-18 13:20:08,985 - INFO - 任务 每日备份任务 完成: 成功 0, 失败 0, 传输 0 B
2025-06-18 13:30:08,528 - INFO - 定时任务触发: task1
2025-06-18 13:30:08,536 - INFO - 开始执行定时任务: task1
2025-06-18 13:30:08,539 - INFO - 启动任务: 每日备份任务
2025-06-18 13:30:09,188 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 13:30:09,307 - INFO - 任务 每日备份任务 扫描完成 - 耗时: 0.10秒, 需要同步: 7, 跳过: 19, 总大小: 29.11 MB
2025-06-18 13:30:12,349 - INFO - 任务 每日备份任务 完成: 成功 7, 失败 0, 传输 29.11 MB
2025-06-18 13:40:08,700 - INFO - 定时任务触发: task1
2025-06-18 13:40:08,707 - INFO - 开始执行定时任务: task1
2025-06-18 13:40:08,709 - INFO - 启动任务: 每日备份任务
2025-06-18 13:40:10,281 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 13:40:10,569 - INFO - 任务 每日备份任务 扫描完成 - 耗时: 0.28秒, 需要同步: 0, 跳过: 26, 总大小: 0 B
2025-06-18 13:40:10,569 - INFO - 任务 每日备份任务 完成: 成功 0, 失败 0, 传输 0 B
2025-06-18 13:50:08,845 - INFO - 定时任务触发: task1
2025-06-18 13:50:08,852 - INFO - 开始执行定时任务: task1
2025-06-18 13:50:08,855 - INFO - 启动任务: 每日备份任务
2025-06-18 13:50:09,561 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 13:50:09,689 - INFO - 任务 每日备份任务 扫描完成 - 耗时: 0.12秒, 需要同步: 0, 跳过: 26, 总大小: 0 B
2025-06-18 13:50:09,689 - INFO - 任务 每日备份任务 完成: 成功 0, 失败 0, 传输 0 B
2025-06-18 14:00:09,029 - INFO - 定时任务触发: task1
2025-06-18 14:00:09,037 - INFO - 开始执行定时任务: task1
2025-06-18 14:00:09,040 - INFO - 启动任务: 每日备份任务
2025-06-18 14:00:09,562 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 14:00:09,678 - INFO - 任务 每日备份任务 扫描完成 - 耗时: 0.11秒, 需要同步: 0, 跳过: 26, 总大小: 0 B
2025-06-18 14:00:09,678 - INFO - 任务 每日备份任务 完成: 成功 0, 失败 0, 传输 0 B
2025-06-18 14:09:49,729 - INFO - 定时调度器开始运行...
2025-06-18 14:09:49,730 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 14:09:49,730 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 14:09:49,730 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 14:09:49,730 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 14:19:49)]
2025-06-18 14:09:49,730 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 14:10:09,124 - INFO - 定时任务触发: task1
2025-06-18 14:10:09,124 - INFO - 开始执行定时任务: task1
2025-06-18 14:10:09,132 - INFO - 启动任务: 每日备份任务
2025-06-18 14:10:09,622 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 14:10:09,740 - INFO - 任务 每日备份任务 扫描完成 - 耗时: 0.10秒, 需要同步: 0, 跳过: 26, 总大小: 0 B
2025-06-18 14:10:09,741 - INFO - 任务 每日备份任务 完成: 成功 0, 失败 0, 传输 0 B
2025-06-18 14:10:25,125 - INFO - 定时调度器开始运行...
2025-06-18 14:10:25,125 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 14:10:25,125 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 14:10:25,125 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 14:10:25,126 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 14:20:25)]
2025-06-18 14:10:25,126 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 14:20:25,209 - INFO - 定时任务触发: task1
2025-06-18 14:20:25,210 - INFO - 开始执行定时任务: task1
2025-06-18 14:20:25,211 - INFO - 启动任务: 每日备份任务
2025-06-18 14:20:25,800 - INFO - 任务 每日备份任务 开始同步 - 模式: mirror
2025-06-18 14:20:25,936 - INFO - 任务 每日备份任务 扫描完成 - 耗时: 0.13秒, 需要同步: 0, 跳过: 26, 总大小: 0 B
2025-06-18 14:20:25,936 - INFO - 任务 每日备份任务 完成: 成功 0, 失败 0, 传输 0 B
2025-06-18 14:42:38,965 - INFO - 定时调度器开始运行...
2025-06-18 14:42:38,966 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 14:42:38,967 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 14:42:38,967 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 14:42:38,967 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 14:52:38)]
2025-06-18 14:42:38,967 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 14:43:02,178 - INFO - 定时调度器开始运行...
2025-06-18 14:43:02,180 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 14:43:02,180 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 14:43:02,180 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 14:43:02,180 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 14:53:02)]
2025-06-18 14:43:02,180 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 14:48:41,275 - INFO - 定时调度器开始运行...
2025-06-18 14:48:41,275 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 14:48:41,275 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 14:48:41,275 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 14:48:41,275 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 14:58:41)]
2025-06-18 14:48:41,275 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 14:52:39,565 - INFO - 定时调度器开始运行...
2025-06-18 14:52:39,569 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 14:52:39,569 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 14:52:39,569 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 14:52:39,569 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 15:02:39)]
2025-06-18 14:52:39,570 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 14:52:39,570 - INFO - 启动任务: 每日备份任务
2025-06-18 14:53:08,084 - INFO - 定时调度器开始运行...
2025-06-18 14:53:08,085 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 14:53:08,086 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 14:53:08,086 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 14:53:08,086 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 15:03:08)]
2025-06-18 14:53:08,086 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 14:53:08,086 - INFO - 启动任务: 每日备份任务
2025-06-18 15:14:55,732 - INFO - 定时调度器开始运行...
2025-06-18 15:14:55,736 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 15:14:55,736 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 15:14:55,736 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 15:14:55,737 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 15:24:55)]
2025-06-18 15:14:55,737 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 15:14:55,737 - INFO - 启动任务: 每日备份任务
2025-06-18 15:20:24,665 - INFO - 定时调度器开始运行...
2025-06-18 15:20:24,667 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 15:20:24,667 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 15:20:24,667 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 15:20:24,667 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 15:30:24)]
2025-06-18 15:20:24,667 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 15:20:24,668 - INFO - 启动任务: 每日备份任务
2025-06-18 15:24:49,647 - INFO - 定时调度器开始运行...
2025-06-18 15:24:49,654 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 15:24:49,654 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 15:24:49,654 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 15:24:49,654 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 15:34:49)]
2025-06-18 15:24:49,654 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 15:24:49,655 - INFO - 启动任务: 每日备份任务
2025-06-18 15:30:57,343 - INFO - 定时调度器开始运行...
2025-06-18 15:30:57,344 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 15:30:57,344 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 15:30:57,344 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 15:30:57,344 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 15:40:57)]
2025-06-18 15:30:57,344 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 15:30:57,345 - INFO - 启动任务: 每日备份任务
2025-06-18 15:43:52,532 - INFO - 定时调度器开始运行...
2025-06-18 15:43:52,534 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 15:43:52,534 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 15:43:52,534 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 15:43:52,534 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 15:53:52)]
2025-06-18 15:43:52,534 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 15:44:57,905 - INFO - 定时调度器开始运行...
2025-06-18 15:44:57,906 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 15:44:57,906 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 15:44:57,906 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 15:44:57,906 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 15:54:57)]
2025-06-18 15:44:57,906 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 15:47:43,425 - INFO - 定时调度器开始运行...
2025-06-18 15:47:43,425 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 15:47:43,425 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 15:47:43,425 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 15:47:43,426 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 15:57:43)]
2025-06-18 15:47:43,426 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 15:49:27,072 - INFO - 启动任务: 每日备份任务
2025-06-18 15:49:27,622 - INFO - 任务 每日备份任务 开始同步 - 模式: incremental
2025-06-18 15:49:27,774 - INFO - 任务 每日备份任务 扫描完成 - 耗时: 0.14秒, 需要同步: 0, 跳过: 26, 总大小: 0 B
2025-06-18 15:49:27,775 - INFO - 任务 每日备份任务 完成: 成功 0, 失败 0, 传输 0 B
2025-06-18 15:57:43,520 - INFO - 定时任务触发: task1
2025-06-18 15:57:43,523 - INFO - 开始执行定时任务: task1
2025-06-18 15:57:43,526 - INFO - 启动任务: 每日备份任务
2025-06-18 15:57:44,016 - INFO - 任务 每日备份任务 开始同步 - 模式: incremental
2025-06-18 15:57:44,220 - INFO - 任务 每日备份任务 扫描完成 - 耗时: 0.19秒, 需要同步: 0, 跳过: 26, 总大小: 0 B
2025-06-18 15:57:44,222 - INFO - 任务 每日备份任务 完成: 成功 0, 失败 0, 传输 0 B
2025-06-18 16:02:14,596 - INFO - 定时调度器开始运行...
2025-06-18 16:02:14,596 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 16:02:14,596 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 16:02:14,596 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 16:02:14,596 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 16:12:14)]
2025-06-18 16:02:14,596 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 16:02:57,496 - INFO - 定时调度器开始运行...
2025-06-18 16:02:57,496 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理 + 时间戳预过滤
2025-06-18 16:02:57,496 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 16:02:57,496 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 16:02:57,496 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 16:12:57)]
2025-06-18 16:02:57,496 - INFO - 重新加载任务调度: 每日备份任务
2025-06-18 16:27:15,699 - INFO - 定时调度器开始运行...
2025-06-18 16:27:15,700 - INFO - 已启用同步优化功能：并行扫描 + 元数据缓存 + 流式处理
2025-06-18 16:27:15,700 - INFO - 设置定时任务: 每日备份任务, 类型: minutely, 间隔: 10, 时间: 13:00
2025-06-18 16:27:15,700 - INFO - 每分钟任务已设置: Job(interval=10, unit=minutes, do=_run_scheduled_task, args=('task1',), kwargs={}), 间隔: 10分钟
2025-06-18 16:27:15,700 - INFO - 任务 每日备份任务 的调度作业: [Every 10 minutes do _run_scheduled_task('task1') (last run: [never], next run: 2025-06-18 16:37:15)]
2025-06-18 16:27:15,700 - INFO - 重新加载任务调度: 每日备份任务
