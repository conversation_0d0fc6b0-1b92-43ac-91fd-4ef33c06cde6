import json
import os

class ConfigManager:
    def __init__(self, config_file='sync_profiles.json'):
        self.config_file = config_file
        self.configs = self.load_configs()

    def load_configs(self):
        """加载配置文件，如果文件不存在则创建一个空的。"""
        if not os.path.exists(self.config_file):
            return {}
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            # 如果文件损坏或为空，返回空字典
            return {}

    def save_configs(self):
        """将当前配置保存到文件。"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.configs, f, indent=4, ensure_ascii=False)
        except IOError as e:
            print(f"Error saving config file: {e}")

    def get_config(self, profile_name):
        """获取指定名称的配置。"""
        return self.configs.get(profile_name)

    def get_all_profiles(self):
        """获取所有配置的名称列表。"""
        return list(self.configs.keys())

    def add_or_update_config(self, profile_name, config_data):
        """添加或更新一个配置。"""
        self.configs[profile_name] = config_data
        self.save_configs()

    def delete_config(self, profile_name):
        """删除一个配置。"""
        if profile_name in self.configs:
            del self.configs[profile_name]
            self.save_configs()
            return True
        return False

# 示例：创建一个默认配置，以便初次运行时有数据
def create_default_config_if_not_exists():
    config_manager = ConfigManager()
    if not config_manager.get_all_profiles():
        default_profile = {
            "profile_name": "示例配置-阿里云OSS",
            "provider": "Alibaba Cloud OSS",
            "endpoint": "oss-cn-hangzhou.aliyuncs.com",
            "access_key": "YOUR_ACCESS_KEY",
            "secret_key": "YOUR_SECRET_KEY",
            "source_bucket": "my-source-bucket",
            "source_path": "data/",
            "target_bucket": "my-target-bucket",
            "target_path": "backup/",
            "region": "cn-hangzhou",
            "sync_options": {
                "strategy": "sync",
                "delete_on_target": False,
                "compare_by": "modtime_and_size",
                "bandwidth_limit_mbps": 0
            }
        }
        config_manager.add_or_update_config("示例配置-阿里云OSS", default_profile)
        print("Created default config profile.")

if __name__ == '__main__':
    create_default_config_if_not_exists()
    cm = ConfigManager()
    print("Available profiles:", cm.get_all_profiles())
    print("Default config:", cm.get_config("示例配置-阿里云OSS")) 