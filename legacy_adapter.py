"""
遗留系统适配器 - 将新的统一存储系统与现有功能模块集成
"""

from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import threading

from storage_abstraction import StorageAdapter, StorageFactory, FileMetadata
from unified_config_manager import UnifiedConfigManager
from unified_task_manager import UnifiedTaskManager, UnifiedSyncTask

# 导入现有模块
try:
    from lightrek_task_manager import S3Config, S3Client, SyncTask
    from lightrek_parallel_scanner import ParallelScanner
    from lightrek_cache_manager import CacheManager
    from lightrek_delta_sync import DeltaSyncManager
    LEGACY_MODULES_AVAILABLE = True
except ImportError:
    LEGACY_MODULES_AVAILABLE = False
    print("警告: 无法导入遗留模块，某些功能可能不可用")


class LegacyS3ClientAdapter:
    """S3Client适配器 - 将新的存储适配器包装为旧的S3Client接口"""
    
    def __init__(self, storage_adapter: StorageAdapter):
        self.storage_adapter = storage_adapter
        self.config = storage_adapter.config
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试连接"""
        return self.storage_adapter.test_connection()
    
    def list_objects(self, prefix: str = "", max_keys: int = 1000, 
                    continuation_token: str = None) -> Dict[str, Any]:
        """列出对象 - 转换为旧格式"""
        result = self.storage_adapter.list_files(prefix, max_keys, continuation_token)
        
        # 转换为旧的格式
        contents = []
        for file_meta in result.files:
            contents.append(file_meta.to_dict())
        
        return {
            'Contents': contents,
            'IsTruncated': result.is_truncated,
            'NextContinuationToken': result.next_token
        }
    
    def get_object(self, key: str) -> Optional[bytes]:
        """获取对象"""
        return self.storage_adapter.get_file(key)
    
    def put_object(self, key: str, data: bytes, content_type: str = 'binary/octet-stream', 
                  original_metadata: Dict[str, Any] = None) -> bool:
        """上传对象"""
        metadata = {}
        if original_metadata:
            for k, v in original_metadata.items():
                metadata[k.lower().replace('_', '-')] = str(v)
        
        return self.storage_adapter.put_file(key, data, content_type, metadata)
    
    def put_object_chunked(self, key: str, data: bytes, chunk_size_mb: int = 10, 
                          content_type: str = 'binary/octet-stream', 
                          original_metadata: Dict[str, Any] = None) -> bool:
        """分片上传对象"""
        metadata = {}
        if original_metadata:
            for k, v in original_metadata.items():
                metadata[k.lower().replace('_', '-')] = str(v)
        
        return self.storage_adapter.put_file_chunked(key, data, chunk_size_mb, content_type, metadata)
    
    def head_object(self, key: str) -> Optional[Dict[str, Any]]:
        """获取对象元数据"""
        file_meta = self.storage_adapter.get_file_metadata(key)
        if file_meta:
            return file_meta.to_dict()
        return None
    
    def bucket_exists(self) -> bool:
        """检查存储桶是否存在"""
        success, _ = self.storage_adapter.test_connection()
        return success


class LegacyTaskManagerAdapter:
    """任务管理器适配器 - 提供与旧系统兼容的接口"""
    
    def __init__(self, unified_task_manager: UnifiedTaskManager):
        self.unified_manager = unified_task_manager
        self.config_manager = unified_task_manager.config_manager
        self.running_tasks = {}
        
        # 如果有遗留模块，初始化它们
        if LEGACY_MODULES_AVAILABLE:
            self.cache_manager = CacheManager()
            self.parallel_scanner = ParallelScanner(self.cache_manager)
            self.delta_sync_manager = DeltaSyncManager()
    
    def get_sources(self) -> Dict[str, Dict[str, Any]]:
        """获取数据源配置"""
        sources = {}
        for source_id, source_config in self.config_manager.get_all_sources().items():
            # 转换为旧格式
            if source_config.get('storage_type') == 's3':
                sources[source_id] = {
                    'name': source_config.get('name', ''),
                    'description': source_config.get('description', ''),
                    'access_key': source_config.get('access_key', ''),
                    'secret_key': source_config.get('secret_key', ''),
                    'endpoint': source_config.get('endpoint', ''),
                    'region': source_config.get('region', ''),
                    'bucket': source_config.get('bucket', '')
                }
        return sources
    
    def get_targets(self) -> Dict[str, Dict[str, Any]]:
        """获取目标配置"""
        targets = {}
        for target_id, target_config in self.config_manager.get_all_targets().items():
            # 转换为旧格式
            if target_config.get('storage_type') == 's3':
                targets[target_id] = {
                    'name': target_config.get('name', ''),
                    'description': target_config.get('description', ''),
                    'access_key': target_config.get('access_key', ''),
                    'secret_key': target_config.get('secret_key', ''),
                    'endpoint': target_config.get('endpoint', ''),
                    'region': target_config.get('region', ''),
                    'bucket': target_config.get('bucket', '')
                }
        return targets
    
    def get_tasks(self) -> Dict[str, Dict[str, Any]]:
        """获取任务配置"""
        tasks = {}
        for task_id, task in self.unified_manager.get_all_tasks().items():
            # 转换为旧格式
            tasks[task_id] = {
                'task_id': task.task_id,
                'name': task.name,
                'description': task.description,
                'source_id': task.source_id,
                'target_id': task.target_id,
                'prefix': task.prefix,
                'max_workers': task.max_workers,
                'retry_times': task.retry_times,
                'retry_delay': task.retry_delay,
                'verify_integrity': task.verify_integrity,
                'incremental_sync': task.incremental_sync,
                'sync_mode': task.sync_mode,
                'delete_extra': task.delete_extra,
                'file_filter': task.file_filter,
                'exclude_filter': task.exclude_filter,
                'bandwidth_limit': task.bandwidth_limit,
                'chunk_threshold': task.chunk_threshold,
                'chunk_size': task.chunk_size,
                'schedule_type': task.schedule_type,
                'schedule_interval': task.schedule_interval,
                'schedule_time': task.schedule_time,
                'enabled': task.enabled,
                'created_at': task.created_at,
                'last_run': task.last_run,
                'last_status': task.last_status
            }
        return tasks
    
    def run_task(self, task_id: str) -> bool:
        """运行任务"""
        return self.unified_manager.start_task(task_id)
    
    def stop_task(self, task_id: str) -> bool:
        """停止任务"""
        return self.unified_manager.stop_task(task_id)
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        return self.unified_manager.get_task_status(task_id)
    
    def create_s3_client(self, storage_id: str, is_source: bool = True) -> Optional[LegacyS3ClientAdapter]:
        """创建S3客户端适配器"""
        try:
            if is_source:
                config = self.config_manager.get_source(storage_id)
            else:
                config = self.config_manager.get_target(storage_id)
            
            if not config:
                return None
            
            # 创建存储适配器
            adapter = StorageFactory.create_adapter(config)
            
            # 包装为S3Client适配器
            return LegacyS3ClientAdapter(adapter)
        
        except Exception as e:
            print(f"创建S3客户端适配器失败: {e}")
            return None


class UnifiedParallelScanner:
    """统一并行扫描器 - 支持多种存储类型"""
    
    def __init__(self, cache_manager=None):
        self.cache_manager = cache_manager
    
    def scan_files_parallel(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter, 
                           task: UnifiedSyncTask) -> Tuple[List[FileMetadata], List[FileMetadata]]:
        """并行扫描文件"""
        source_files = []
        target_files = []
        
        def scan_source():
            nonlocal source_files
            try:
                source_files = self._scan_all_files(source_adapter, task.prefix)
            except Exception as e:
                print(f"扫描源文件失败: {e}")
        
        def scan_target():
            nonlocal target_files
            try:
                target_files = self._scan_all_files(target_adapter, task.prefix)
            except Exception as e:
                print(f"扫描目标文件失败: {e}")
        
        # 并行扫描
        source_thread = threading.Thread(target=scan_source)
        target_thread = threading.Thread(target=scan_target)
        
        source_thread.start()
        target_thread.start()
        
        source_thread.join()
        target_thread.join()
        
        return source_files, target_files
    
    def _scan_all_files(self, adapter: StorageAdapter, prefix: str = "") -> List[FileMetadata]:
        """扫描所有文件"""
        all_files = []
        continuation_token = None
        
        while True:
            try:
                result = adapter.list_files(prefix=prefix, max_keys=1000, continuation_token=continuation_token)
                all_files.extend(result.files)
                
                if not result.is_truncated:
                    break
                
                continuation_token = result.next_token
            except Exception as e:
                print(f"扫描文件失败: {e}")
                break
        
        return all_files


class UnifiedDeltaSyncManager:
    """统一差异同步管理器"""
    
    def __init__(self):
        self.chunk_size = 1024 * 1024  # 1MB
    
    def apply_delta_sync(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter, 
                        file_key: str, source_data: bytes) -> Dict[str, Any]:
        """应用差异同步"""
        try:
            # 检查目标文件是否存在
            target_meta = target_adapter.get_file_metadata(file_key)
            
            if not target_meta:
                # 目标文件不存在，全量上传
                success = target_adapter.put_file(file_key, source_data)
                return {
                    'success': success,
                    'message': '全量上传（目标文件不存在）',
                    'bytes_transferred': len(source_data) if success else 0,
                    'operations': 1,
                    'optimization_ratio': 0.0
                }
            
            # 获取目标文件内容
            target_data = target_adapter.get_file(file_key)
            if not target_data:
                # 无法读取目标文件，全量上传
                success = target_adapter.put_file(file_key, source_data)
                return {
                    'success': success,
                    'message': '全量上传（无法读取目标文件）',
                    'bytes_transferred': len(source_data) if success else 0,
                    'operations': 1,
                    'optimization_ratio': 0.0
                }
            
            # 比较文件内容
            if source_data == target_data:
                # 文件相同，跳过
                return {
                    'success': True,
                    'message': '文件未改变，跳过同步',
                    'bytes_transferred': 0,
                    'operations': 0,
                    'optimization_ratio': 100.0
                }
            
            # 文件不同，执行全量上传（简化实现）
            success = target_adapter.put_file(file_key, source_data)
            return {
                'success': success,
                'message': '文件已更新，全量上传',
                'bytes_transferred': len(source_data) if success else 0,
                'operations': 1,
                'optimization_ratio': 0.0
            }
        
        except Exception as e:
            return {
                'success': False,
                'message': f'差异同步失败: {str(e)}',
                'bytes_transferred': 0,
                'operations': 0,
                'optimization_ratio': 0.0
            }


def create_legacy_compatible_system(config_file: str = "lightrek_unified_config.json"):
    """创建与遗留系统兼容的统一存储系统"""
    
    # 创建统一配置管理器
    config_manager = UnifiedConfigManager(config_file)
    
    # 创建统一任务管理器
    task_manager = UnifiedTaskManager(config_manager)
    
    # 创建遗留适配器
    legacy_adapter = LegacyTaskManagerAdapter(task_manager)
    
    # 创建统一功能模块
    parallel_scanner = UnifiedParallelScanner()
    delta_sync_manager = UnifiedDeltaSyncManager()
    
    return {
        'config_manager': config_manager,
        'task_manager': task_manager,
        'legacy_adapter': legacy_adapter,
        'parallel_scanner': parallel_scanner,
        'delta_sync_manager': delta_sync_manager
    }


# 示例：如何在现有代码中使用
def example_integration():
    """示例：如何集成到现有系统"""
    
    # 创建兼容系统
    system = create_legacy_compatible_system()
    
    # 使用遗留接口
    legacy_manager = system['legacy_adapter']
    
    # 获取配置（与旧系统兼容）
    sources = legacy_manager.get_sources()
    targets = legacy_manager.get_targets()
    tasks = legacy_manager.get_tasks()
    
    print(f"数据源: {len(sources)}")
    print(f"目标存储: {len(targets)}")
    print(f"同步任务: {len(tasks)}")
    
    # 运行任务（与旧系统兼容）
    for task_id in tasks.keys():
        print(f"启动任务: {task_id}")
        success = legacy_manager.run_task(task_id)
        print(f"任务启动{'成功' if success else '失败'}")
        break  # 只启动第一个任务作为示例


if __name__ == "__main__":
    example_integration()
