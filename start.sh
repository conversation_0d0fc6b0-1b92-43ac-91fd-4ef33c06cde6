#!/bin/bash

echo "🚀 启动LightRek S3同步工具..."
echo "================================"

# 检查Python版本
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到python3，请先安装Python 3.7+"
    exit 1
fi

# 检查配置文件
if [ ! -f "lightrek_config.json" ]; then
    echo "⚠️  配置文件不存在，将使用默认配置"
fi

# 显示帮助信息
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --port PORT           Web界面端口号 (默认: 8001)"
    echo "  --auto-start         启动时自动执行所有已启用的计划任务"
    echo "  --task-id TASK_ID    指定要运行的任务ID"
    echo "  --daemon             后台运行模式"
    echo "  --config CONFIG      配置文件路径 (默认: lightrek_config.json)"
    echo "  --no-web             不启动Web界面，仅运行调度器"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 正常启动"
    echo "  $0 --auto-start      # 启动并自动执行计划任务"
    echo "  $0 --task-id task1   # 运行指定任务"
    echo "  $0 --port 8080       # 使用8080端口启动Web界面"
    echo ""
    exit 0
fi

echo "Web管理界面将在启动后显示"
echo "按 Ctrl+C 退出程序"
echo ""

# 启动程序
python3 start_lightrek.py "$@" 