#!/usr/bin/env python3
"""
LightRek 统一存储同步工具主入口 - 修正版
使用原来的http.server架构，不依赖Flask
"""

import sys
import os
import threading
import time
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入所有存储适配器以注册到工厂
try:
    import s3_storage_adapter
    import sftp_storage_adapter
    import smb_storage_adapter
    import ftp_storage_adapter
    import local_storage_adapter
    print("存储适配器加载成功")
except ImportError as e:
    print(f"部分存储适配器加载失败: {e}")

from unified_config_manager import UnifiedConfigManager
from unified_task_manager import UnifiedTaskManager
from unified_web_interface import UnifiedWebInterface


def main():
    """主函数"""
    print("LightRek 统一存储同步工具")
    print("=" * 50)
    print("支持的存储类型:")
    print("  - S3兼容存储 (AWS S3, 阿里云OSS, 腾讯云COS等)")
    print("  - SFTP (SSH文件传输协议)")
    print("  - SMB/CIFS (Windows网络共享)")
    print("  - FTP/FTPS (文件传输协议)")
    print("  - 本地文件系统 (本地磁盘, NAS设备)")
    print("=" * 50)
    
    try:
        # 创建配置管理器
        config_manager = UnifiedConfigManager()
        
        # 创建任务管理器
        task_manager = UnifiedTaskManager(config_manager)
        
        print("系统初始化成功")
        
        # 启动Web界面（使用原来的http.server架构）
        web_interface = UnifiedWebInterface(config_manager, task_manager, port=8001)
        web_interface.start()
        
        print("启动Web管理界面...")
        print("访问地址: http://localhost:8001")
        print("按 Ctrl+C 停止程序")
        
        # 保持程序运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            web_interface.stop()
        
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
